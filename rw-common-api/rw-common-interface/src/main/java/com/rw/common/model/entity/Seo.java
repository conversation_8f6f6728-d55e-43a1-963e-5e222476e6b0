package com.rw.common.model.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.rw.common.core.web.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.util.Date;

/**
 * SEO对象 seo
 *
 * <AUTHOR>
 * @date 2024-07-23
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "SEO")
public class Seo extends BaseEntity {
    @TableId
    @ApiModelProperty(value = "内部标识")
    private Integer id;
    @ApiModelProperty(value = "所属位置")
    private Integer position;
    @ApiModelProperty(value = "名称")
    private String title;
    @ApiModelProperty(value = "名称(英文)")
    private String titleEn;
    @ApiModelProperty(value = "内容")
    private String content;
    @ApiModelProperty(value = "内容(英文)")
    private String contentEn;
    @ApiModelProperty(value = "描述")
    private String description;
    @ApiModelProperty(value = "描述(英文)")
    private String descriptionEn;
    @TableLogic
    @ApiModelProperty(value = "删除状态 1已删除 0未删除")
    private Long delFlag;
    @ApiModelProperty(value = "创建人id")
    private Long createId;

    @ApiModelProperty(value = "更新人id")
    private Long updateId;


}
