package com.rw.common.model.enums;


/**
 * <AUTHOR>
 * @Description SEO位置枚举
 * @Param
 * @return
 **/
public enum SeoPositionEnum {
    HOME(1, "首页"),
    PRODUCT_LIST(2, "产品列表"),
    PROGRAMME_LIST(3, "方案列表"),
    CASE(4, "成功案例"),
    DOWNLOAD(5, "下载中心"),
    ABOUT(6, "关于瑞物"),
    NEWS(7, "新闻资讯"),
    ;
    private Integer code;
    private String remark;

    SeoPositionEnum(Integer code, String remark) {
        this.code = code;
        this.remark = remark;
    }

    public Integer getCode() {
        return code;
    }

    public void setPlatform(Integer platform) {
        this.code = platform;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public static SeoPositionEnum getEnumByPlatform(int platform) {
        for (SeoPositionEnum chainPlatformEnum : SeoPositionEnum.values()) {
            if (chainPlatformEnum.code == platform) {
                return chainPlatformEnum;
            }
        }

        return null;
    }

    public static String getRemarkByCode(int platform) {
        for (SeoPositionEnum chainPlatformEnum : SeoPositionEnum.values()) {
            if (chainPlatformEnum.code == platform) {
                return chainPlatformEnum.remark;
            }
        }

        return null;
    }


    public static Integer getCodeByRemark(String remark) {
        for (SeoPositionEnum chainPlatformEnum : SeoPositionEnum.values()) {
            if (chainPlatformEnum.remark.equals(remark)) {
                return chainPlatformEnum.code;
            }
        }

        return 0;
    }
}
