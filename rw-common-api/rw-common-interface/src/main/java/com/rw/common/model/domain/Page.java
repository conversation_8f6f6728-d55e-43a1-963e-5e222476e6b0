package com.rw.common.model.domain;

import java.io.Serializable;
import java.util.List;

/**
 * @ClassName Page
 * @Description 分页
 * <AUTHOR>
 * @Date 2022/10/24 15:36
 * @Version 1.0
 */
public class Page<T> implements Serializable {
    private static final long serialVersionUID = -3101101529316214943L;
    private int pageSize = 10;
    private long totalCount;
    private int currentPage;
    private List<T> data;
    private String unit = "条";
    private String extInfo;

    public Page() {
    }

    public Page(int currentPage) {
        this.currentPage = currentPage;
    }

    public Page(int currentPage, int pageSize) {
        this.currentPage = currentPage;
        this.pageSize = pageSize;
    }

    public int getStartIndex() {
        return ((getCurrentPage() - 1) * this.pageSize);
    }

    public int getEndIndex() {
        return (this.pageSize);
    }

    public boolean isFirstPage() {
        return (getCurrentPage() <= 1);
    }

    public boolean isLastPage() {
        return (getCurrentPage() >= getPageCount());
    }

    public int getNextPage() {
        if (isLastPage()) {
            return getCurrentPage();
        }
        return (getCurrentPage() + 1);
    }

    public int getPreviousPage() {
        if (isFirstPage()) {
            return 1;
        }
        return (getCurrentPage() - 1);
    }

    public int getCurrentPage() {
        if (this.currentPage == 0) {
            this.currentPage = 1;
        }
        return this.currentPage;
    }

    public long getPageCount() {
        if (this.totalCount % this.pageSize == 0) {
            return (this.totalCount / this.pageSize);
        }
        return (this.totalCount / this.pageSize + 1);
    }

    public long getTotalCount() {
        return this.totalCount;
    }

    public void setCurrentPage(int currentPage) {
        this.currentPage = currentPage;
    }

    public int getPageSize() {
        return this.pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

    public boolean hasNextPage() {
        return (getCurrentPage() < getPageCount());
    }

    public boolean hasPreviousPage() {
        return (getCurrentPage() > 1);
    }

    public List<T> getResult() {
        return this.data;
    }

    public void setResult(List<T> data) {
        this.data = data;
    }

    public void setTotalCount(long totalCount) {
        this.totalCount = totalCount;
    }

    public void setTotalCount(int totalCount) {
        this.totalCount = totalCount;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public String getUnit() {
        return this.unit;
    }

    public void setExtInfo(String extInfo) {
        this.extInfo = extInfo;
    }

    public String getExtInfo() {
        return this.extInfo;
    }
}
