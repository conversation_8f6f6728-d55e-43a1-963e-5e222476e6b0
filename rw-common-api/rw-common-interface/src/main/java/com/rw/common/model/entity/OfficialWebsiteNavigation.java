package com.rw.common.model.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.rw.common.core.web.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.util.Date;
import java.util.List;

/**
 * 官网导航对象 official_website_navigation
 *
 * <AUTHOR>
 * @date 2024-07-23
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "官网导航")
public class OfficialWebsiteNavigation extends BaseEntity {
    @TableId
    @ApiModelProperty(value = "内部标识")
    private Integer id;

    @ApiModelProperty(value = "标题")
    private String title;
    @ApiModelProperty(value = "标题英文")
    private String titleEn;
    @ApiModelProperty(value = "父ID")
    private Integer fatherId;
    @ApiModelProperty(value = "是否链接 1是")
    private Integer isLink;
    @ApiModelProperty(value = "链接url")
    private String linkUrl;
    @ApiModelProperty(value = "是否显示 1是")
    private Integer isShow;
    @ApiModelProperty(value = "系统标识 1官网，2瑞物云")
    private Integer systemId;
    @ApiModelProperty(value = "排序")
    private Long sort;
    @TableLogic
    @ApiModelProperty(value = "删除状态 1已删除 0未删除")
    private Long delFlag;
    @ApiModelProperty(value = "创建人id")
    private Long createId;

    @ApiModelProperty(value = "更新人id")
    private Long updateId;



    @ApiModelProperty(value = "子导航")
    @TableField(exist = false)
    private List<OfficialWebsiteNavigation> childList;

}
