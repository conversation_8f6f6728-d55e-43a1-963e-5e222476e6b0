package com.rw.common.model.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.rw.common.core.web.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.util.Date;

/**
 * 新闻中心对象 news_center
 *
 * <AUTHOR>
 * @date 2024-07-23
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "新闻中心")
public class NewsCenter extends BaseEntity {
    @TableId
    @ApiModelProperty(value = "内部标识")
    private Integer id;

    @ApiModelProperty(value = "方案类型 1配电柜 2ups 3空调 4安防 5一级配电柜 6EPS 7动环 8机房能源配置")
    private Long classify;

    @ApiModelProperty(value = "名称")
    private String title;

    @ApiModelProperty(value = "名称(英文)")
    private String titleEn;

    @ApiModelProperty(value = "副标题 简述")
    private String subTitle;

    @ApiModelProperty(value = "副标题 简述(英文)")
    private String subTitleEn;

    @ApiModelProperty(value = "内容")
    private String content;

    @ApiModelProperty(value = "内容(英文)")
    private String contentEn;


    @ApiModelProperty(value = "用户ID")
    private Integer authorId;

    @ApiModelProperty(value = "用户类型")
    private Integer authorClassify;

    @ApiModelProperty(value = "是否推荐 1是")
    private Integer isFlag;
    @ApiModelProperty(value = "是否显示 1是")
    private Integer isShow;
    @ApiModelProperty(value = "地址链接")
    private String uri;
    @ApiModelProperty(value = "封面图片路径")
    private String img;
    @ApiModelProperty(value = "商家推荐 1是")
    private Integer facisFlag;

    @ApiModelProperty(value = "文章类别 0 成功案例 2 瑞物云百科 3 瑞物官网新闻")
    private Integer articleType;


    @TableLogic
    @ApiModelProperty(value = "删除状态 1已删除 0未删除")
    private Long delFlag;

    @ApiModelProperty(value = "创建人id")
    private Long createId;

    @ApiModelProperty(value = "更新人id")
    private Long updateId;

}
