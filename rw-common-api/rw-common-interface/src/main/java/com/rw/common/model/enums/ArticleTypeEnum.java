package com.rw.common.model.enums;

import java.util.Objects;

/**
 * 新闻 文章类型
 */
public enum ArticleTypeEnum {

    SUCCESS_STORIES(0,"成功案例"),
    RW_WIKI(2,"瑞物云百科"),
    OFFICIAL_WEBSITE_NEWS(3,"瑞物官网新闻"),
    ;

    private Integer id;
    private String name;

    ArticleTypeEnum(Integer id,String name) {
        this.id = id;
        this.name = name;
    }

    public Integer getId() {
        return this.id;
    }

    public String getName() {
        return this.name;
    }
}
