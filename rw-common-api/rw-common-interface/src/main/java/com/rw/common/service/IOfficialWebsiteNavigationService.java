package com.rw.common.service;

import com.rw.common.core.domain.R;
import com.rw.common.model.Constant;
import com.rw.common.model.entity.OfficialWebsiteNavigation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import com.rw.common.log.annotation.Log;
import com.rw.common.log.enums.BusinessType;
import com.rw.common.core.web.page.TableDataInfo;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import com.rw.common.core.web.domain.AjaxResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.List;

/**
 * 官网导航接口
 * 
 * <AUTHOR>
 * @date 2024-07-23
 */
@Api(tags = "官网导航:OfficialWebsiteNavigation")
@FeignClient(Constant.APPLICATION_NAME)
public interface IOfficialWebsiteNavigationService {

    @ApiOperation(value = "查询官网导航列表", response = AjaxResult.class)
    @GetMapping("navigation/list")
    AjaxResult list(OfficialWebsiteNavigation officialWebsiteNavigation);


    @ApiOperation(value = "获取官网导航详细信息", response = AjaxResult.class)
    @GetMapping(value = "navigation/{id}")
    AjaxResult getInfo(@PathVariable(value = "id") Integer id);

    @ApiOperation(value = "新增官网导航", response = AjaxResult.class)
    @Log(title = "官网导航", businessType = BusinessType.INSERT)
    @PostMapping(value = "navigation")
    AjaxResult add(@RequestBody OfficialWebsiteNavigation officialWebsiteNavigation);

    @ApiOperation(value = "修改官网导航", response = AjaxResult.class)
    @Log(title = "官网导航", businessType = BusinessType.UPDATE)
    @PutMapping(value = "navigation")
    AjaxResult edit(@RequestBody OfficialWebsiteNavigation officialWebsiteNavigation);

    @ApiOperation(value = "删除官网导航", response = AjaxResult.class)
    @Log(title = "官网导航", businessType = BusinessType.DELETE)
    @DeleteMapping("navigation/{ids}")
    AjaxResult remove(@PathVariable(value = "ids") Integer[] ids);



    @ApiOperation(value = "获取导航")
    @PostMapping("officialwebsite/navigation")
    R<List<OfficialWebsiteNavigation>> getOfficialWebsiteNavigation(@RequestBody OfficialWebsiteNavigation req);

}
