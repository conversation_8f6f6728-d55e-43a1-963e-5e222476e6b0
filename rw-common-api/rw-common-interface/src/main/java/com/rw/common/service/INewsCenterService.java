package com.rw.common.service;

import com.rw.common.core.domain.R;
import com.rw.common.core.utils.PageResultUtils;
import com.rw.common.model.Constant;
import com.rw.common.model.entity.NewsCenter;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import com.rw.common.log.annotation.Log;
import com.rw.common.log.enums.BusinessType;
import com.rw.common.core.web.page.TableDataInfo;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import com.rw.common.core.web.domain.AjaxResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.List;

/**
 * 新闻中心接口
 * 
 * <AUTHOR>
 * @date 2024-07-23
 */
@Api(tags = "新闻中心:NewsCenter")
@FeignClient(Constant.APPLICATION_NAME)
public interface INewsCenterService {

    @ApiOperation(value = "查询新闻中心列表", response = AjaxResult.class)
    @GetMapping("news/list")
    TableDataInfo list(NewsCenter newsCenter);


    @ApiOperation(value = "获取新闻中心详细信息", response = AjaxResult.class)
    @GetMapping(value = "news/{id}")
    AjaxResult getInfo(@PathVariable(value = "id") Integer id);

    @ApiOperation(value = "新增新闻中心", response = AjaxResult.class)
    @Log(title = "新闻中心", businessType = BusinessType.INSERT)
    @PostMapping(value = "news")
    AjaxResult add(@RequestBody NewsCenter newsCenter);

    @ApiOperation(value = "修改新闻中心", response = AjaxResult.class)
    @Log(title = "新闻中心", businessType = BusinessType.UPDATE)
    @PutMapping(value = "news")
    AjaxResult edit(@RequestBody NewsCenter newsCenter);

    @ApiOperation(value = "删除新闻中心", response = AjaxResult.class)
    @Log(title = "新闻中心", businessType = BusinessType.DELETE)
    @DeleteMapping("news/{ids}")
    AjaxResult remove(@PathVariable(value = "ids") Integer[] ids);



    @ApiOperation(value = "最后10条新闻", response = R.class)
    @PostMapping(value = "news/last10")
    List<NewsCenter> last10(@RequestBody NewsCenter newsCenter);


    @ApiOperation(value = "新闻列表", response = R.class)
    @PostMapping(value = "news/page")
    PageResultUtils<NewsCenter> page(@RequestBody NewsCenter newsCenter);


    @ApiOperation(value = "新闻详情", response = R.class)
    @PostMapping(value = "news/getInfoById")
    NewsCenter getInfoById(@RequestBody NewsCenter newsCenter);

}
