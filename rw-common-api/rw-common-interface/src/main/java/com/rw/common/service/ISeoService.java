package com.rw.common.service;

import com.rw.common.model.Constant;
import com.rw.common.model.entity.Seo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import com.rw.common.log.annotation.Log;
import com.rw.common.log.enums.BusinessType;
import com.rw.common.core.web.page.TableDataInfo;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import com.rw.common.core.web.domain.AjaxResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * SEO接口
 *
 * <AUTHOR>
 * @date 2024-07-23
 */
@Api(tags = "SEO:Seo")
@FeignClient(Constant.APPLICATION_NAME)
public interface ISeoService {

    @ApiOperation(value = "查询SEO列表", response = AjaxResult.class)
    @GetMapping("seo/list")
    TableDataInfo list(Seo seo);


    @ApiOperation(value = "获取SEO详细信息", response = AjaxResult.class)
    @GetMapping(value = "seo/{id}")
    AjaxResult getInfo(@PathVariable(value = "id") Integer id);

    @ApiOperation(value = "新增SEO", response = AjaxResult.class)
    @Log(title = "SEO", businessType = BusinessType.INSERT)
    @PostMapping(value = "seo")
    AjaxResult add(@RequestBody Seo seo);

    @ApiOperation(value = "修改SEO", response = AjaxResult.class)
    @Log(title = "SEO", businessType = BusinessType.UPDATE)
    @PutMapping(value = "seo")
    AjaxResult edit(@RequestBody Seo seo);

    @ApiOperation(value = "删除SEO", response = AjaxResult.class)
    @Log(title = "SEO", businessType = BusinessType.DELETE)
    @DeleteMapping("seo/{ids}")
    AjaxResult remove(@PathVariable(value = "ids") Integer[] ids);

    @ApiOperation(value = "根据位置获取SEO详细信息", response = AjaxResult.class)
    @PostMapping(value = "seo/getSeoDataByPosition")
    Seo getSeoDataByPosition(@RequestBody Seo seo);
}
