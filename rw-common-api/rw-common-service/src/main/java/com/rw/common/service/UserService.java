package com.rw.common.service;

import com.rw.common.config.Configs;
import com.rw.common.security.utils.SecurityUtils;
import com.rw.system.api.model.LoginUser;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class UserService {

    @Autowired
    Configs configs;


    public String getNickname() {
        LoginUser loginShUser = SecurityUtils.getLoginUser();
        if (loginShUser != null) {
            return loginShUser.getSysUser().getNickName();
        }
        if (configs.getProfileActive().equals("dev")) {
            return "本地应用用户昵称";
        }
        return "";
    }



    public Long getUserId() {
        LoginUser loginShUser = SecurityUtils.getLoginUser();
        if (loginShUser != null) {
            return loginShUser.getSysUser().getUserId();
        }
        return 0L;
    }

    public String getEmail() {
        LoginUser loginShUser = SecurityUtils.getLoginUser();
        if (loginShUser != null) {
            return loginShUser.getSysUser().getEmail();
        }
        LoginUser loginUser = SecurityUtils.getLoginUser();
        if (loginUser != null) {
            return loginUser.getSysUser().getEmail();
        }
        return "";
    }

    public String getPhone() {
        LoginUser loginShUser = SecurityUtils.getLoginUser();
        if (loginShUser != null) {
            return loginShUser.getSysUser().getPhonenumber();
        }
        if (configs.getProfileActive().equals("dev")) {
            return "本地应用用户手机号";
        }
        return "";
    }
}
