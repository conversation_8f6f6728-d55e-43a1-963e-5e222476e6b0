package com.rw.common.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;

import com.rw.common.core.utils.ResultUtils;
import com.rw.common.core.web.controller.BaseController;
import com.rw.common.core.web.domain.AjaxResult;
import com.rw.common.core.web.page.TableDataInfo;
import com.rw.common.mapper.SeoMapper;
import com.rw.common.model.entity.Seo;
import com.rw.common.service.ISeoService;
import com.rw.common.service.UserService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.List;

/**
 * SEOService业务层处理
 *
 * <AUTHOR>
 * @date 2024-07-23
 */
@Slf4j
@RestController
@AllArgsConstructor
public class SeoServiceImpl extends BaseController implements ISeoService {
    @Autowired
    UserService userService;
    @Autowired
    SeoMapper seoMapper;
    @Autowired
    private ResultUtils resultUtils;

    /**
     * 查询SEO列表
     */
    @Override
    public TableDataInfo list(Seo seo) {
        startPage();
        QueryWrapper<Seo> queryWrapper = new QueryWrapper<>();
        queryWrapper.setEntity(seo);
        queryWrapper.orderByDesc("id");
        List<Seo> list = seoMapper.selectList(queryWrapper);
        return getDataTable(list);
    }


    /**
     * 获取SEO详细信息
     */
    @Override
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return AjaxResult.success(seoMapper.selectById(id));
    }

    /**
     * 新增SEO
     */
    @Override
    public AjaxResult add(@RequestBody Seo seo) {
        seo.setCreateId(userService.getUserId());
        seo.setCreateBy(userService.getNickname());
        return toAjax(seoMapper.insert(seo));
    }

    /**
     * 修改SEO
     */
    @Override
    public AjaxResult edit(@RequestBody Seo seo) {
        seo.setUpdateId(userService.getUserId());
        seo.setUpdateBy(userService.getNickname());
        return toAjax(seoMapper.updateById(seo));
    }

    /**
     * 删除SEO
     */
    @Override
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toAjax(seoMapper.deleteBatchIds(Arrays.asList(ids)));
    }

    @Override
    public Seo getSeoDataByPosition(Seo seo) {
        if (seo == null) {
            return null;
        }
        QueryWrapper<Seo> queryWrapper = new QueryWrapper<>();
        String language = seo.getLanguage();
        String collmns = "id,title,content,description";
        if (language.equals("en")) {
            collmns = "id,title_en as title,content_en as content,description_en as description";
        }
        queryWrapper.select(collmns);
        queryWrapper.eq("position", seo.getPosition());
        queryWrapper.last("limit 1");
        return seoMapper.selectOne(queryWrapper);
    }
}
