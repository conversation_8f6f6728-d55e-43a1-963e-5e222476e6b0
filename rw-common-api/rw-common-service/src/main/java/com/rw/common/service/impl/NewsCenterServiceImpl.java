package com.rw.common.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rw.common.core.domain.R;
import com.rw.common.core.utils.PageResultUtils;
import com.rw.common.core.web.controller.BaseController;
import com.rw.common.core.web.domain.AjaxResult;
import com.rw.common.core.web.page.TableDataInfo;
import com.rw.common.mapper.NewsCenterMapper;
import com.rw.common.model.entity.NewsCenter;
import com.rw.common.service.INewsCenterService;
import com.rw.common.service.UserService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.List;

/**
 * 新闻中心Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-07-23
 */
@Slf4j
@RestController
@AllArgsConstructor
public class NewsCenterServiceImpl extends BaseController implements INewsCenterService {
    @Autowired
    UserService userService;
    @Autowired
    NewsCenterMapper newsCenterMapper;

    /**
     * 查询列
     *
     * @param language
     * @return
     */
    private String getColumns(String language) {
        String columns = " id,classify, title, sub_title,content,  author_id, author_classify, is_flag,is_show, uri, img, facis_flag, article_type, del_flag, create_id, create_by,create_time, update_id, update_by, update_time, remark";
        if ("en".equals(language)) {
            columns = "id,classify, title_en as title, sub_title_en as sub_title,content_en as content, author_id, author_classify, is_flag,is_show, uri, img, facis_flag, article_type, del_flag, create_id, create_by,create_time, update_id, update_by, update_time, remark";
        }
        return columns;
    }


    /**
     * 查询新闻中心列表
     */
    @Override
    public TableDataInfo list(NewsCenter newsCenter) {
        startPage();
        QueryWrapper<NewsCenter> queryWrapper = new QueryWrapper<>();
        queryWrapper.setEntity(newsCenter);
        queryWrapper.orderByDesc("id");
        List<NewsCenter> list = newsCenterMapper.selectList(queryWrapper);
        return getDataTable(list);
    }


    /**
     * 获取新闻中心详细信息
     */
    @Override
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return AjaxResult.success(newsCenterMapper.selectById(id));
    }

    /**
     * 新增新闻中心
     */
    @Override
    public AjaxResult add(@RequestBody NewsCenter newsCenter) {
        newsCenter.setCreateId(userService.getUserId());
        newsCenter.setCreateBy(userService.getNickname());
        return toAjax(newsCenterMapper.insert(newsCenter));
    }

    /**
     * 修改新闻中心
     */
    @Override
    public AjaxResult edit(@RequestBody NewsCenter newsCenter) {
        newsCenter.setUpdateId(userService.getUserId());
        newsCenter.setUpdateBy(userService.getNickname());
        return toAjax(newsCenterMapper.updateById(newsCenter));
    }

    /**
     * 删除新闻中心
     */
    @Override
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toAjax(newsCenterMapper.deleteBatchIds(Arrays.asList(ids)));
    }

    @Override
    public List<NewsCenter> last10(@RequestBody NewsCenter newsCenter) {

        QueryWrapper<NewsCenter> queryWrapper = new QueryWrapper<>();
        queryWrapper.select(getColumns(newsCenter.getLanguage()));

        queryWrapper.eq("article_type", newsCenter.getArticleType());
        queryWrapper.eq("is_show", 1);
        queryWrapper.orderByDesc("update_time").last("limit 10");
        return newsCenterMapper.selectList(queryWrapper);
    }

    @Override
    public PageResultUtils<NewsCenter> page(@RequestBody NewsCenter newsCenter) {

        QueryWrapper<NewsCenter> queryWrapper = new QueryWrapper<>();
        queryWrapper.select(getColumns(newsCenter.getLanguage()));

        queryWrapper.eq("article_type", newsCenter.getArticleType());

        if (newsCenter.getAuthorClassify() != null) {
            queryWrapper.eq("author_classify", newsCenter.getAuthorClassify());
        }
        if (newsCenter.getAuthorId() != null) {
            queryWrapper.eq("author_id", newsCenter.getAuthorId());
        }
        queryWrapper.orderByDesc("update_time");


        Page page = new Page();
        page.setCurrent(newsCenter.getPageNum());
        page.setSize(newsCenter.getPageSize());

        Page<NewsCenter> newsPage = newsCenterMapper.selectPage(page, queryWrapper);

        return new PageResultUtils<NewsCenter>((int) newsPage.getTotal(), (int) newsPage.getSize(), (int) newsPage.getPages(), (int) newsPage.getCurrent(), newsPage.getRecords());
    }

    @Override
    public NewsCenter getInfoById(@RequestBody NewsCenter newsCenter) {
        QueryWrapper<NewsCenter> queryWrapper = new QueryWrapper<>();
        queryWrapper.select(getColumns(newsCenter.getLanguage()));
        queryWrapper.eq("id", newsCenter.getId());
        if (newsCenter.getArticleType() != null) {
            queryWrapper.eq("article_type", newsCenter.getArticleType());
        }
        if (newsCenter.getAuthorClassify() != null) {
            queryWrapper.eq("author_classify", newsCenter.getAuthorClassify());
        }
        if (newsCenter.getAuthorId() != null) {
            queryWrapper.eq("author_id", newsCenter.getAuthorId());
        }
        return newsCenterMapper.selectOne(queryWrapper);
    }
}
