package com.rw.common.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.rw.common.core.domain.R;
import com.rw.common.core.web.controller.BaseController;
import com.rw.common.core.web.domain.AjaxResult;
import com.rw.common.core.web.page.TableDataInfo;
import com.rw.common.mapper.OfficialWebsiteNavigationMapper;
import com.rw.common.model.entity.OfficialWebsiteNavigation;
import com.rw.common.model.entity.Seo;
import com.rw.common.service.IOfficialWebsiteNavigationService;
import com.rw.common.service.UserService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;
/**
 * 官网导航Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-07-23
 */
@Slf4j
@RestController
@AllArgsConstructor
public class OfficialWebsiteNavigationServiceImpl extends BaseController implements IOfficialWebsiteNavigationService
{
    @Autowired
    UserService userService;
    @Autowired
    OfficialWebsiteNavigationMapper officialWebsiteNavigationMapper;

    /**
     * 查询列
     * @param language
     * @return
     */
    private String getColumns(String language){
        String columns=" id, title, father_id, is_link, link_url, is_show, system_id, sort, del_flag, create_id, create_by, create_time, update_id,update_by, update_time, remark";
        if ("en".equals(language)) {
            columns = "id, title_en as title, father_id, is_link, link_url, is_show, system_id, sort, del_flag, create_id, create_by, create_time, update_id,update_by, update_time, remark";
        }
        return columns;
    }



    /**
     * 查询官网导航列表
     */
    @Override
    public AjaxResult list(OfficialWebsiteNavigation officialWebsiteNavigation)
    {
        QueryWrapper<OfficialWebsiteNavigation> queryWrapper = new QueryWrapper<>();
        queryWrapper.setEntity(officialWebsiteNavigation);
        queryWrapper.orderByAsc("sort");
        List<OfficialWebsiteNavigation> list =officialWebsiteNavigationMapper.selectList(queryWrapper);
        return AjaxResult.success(list);
    }


    /**
     * 获取官网导航详细信息
     */
    @Override
    public AjaxResult getInfo(@PathVariable("id") Integer id)
    {
        return AjaxResult.success(officialWebsiteNavigationMapper.selectById(id));
    }

    /**
     * 新增官网导航
     */
    @Override
    public AjaxResult add(@RequestBody OfficialWebsiteNavigation officialWebsiteNavigation)
    {
        officialWebsiteNavigation.setCreateId(userService.getUserId());
        officialWebsiteNavigation.setCreateBy(userService.getNickname());
        return toAjax(officialWebsiteNavigationMapper.insert(officialWebsiteNavigation));
    }

    /**
     * 修改官网导航
     */
    @Override
    public AjaxResult edit(@RequestBody OfficialWebsiteNavigation officialWebsiteNavigation)
    {
        officialWebsiteNavigation.setUpdateId(userService.getUserId());
        officialWebsiteNavigation.setUpdateBy(userService.getNickname());
        return toAjax(officialWebsiteNavigationMapper.updateById(officialWebsiteNavigation));
    }

    /**
     * 删除官网导航
     */
    @Override
    public AjaxResult remove(@PathVariable Integer[] ids)
    {
        return toAjax(officialWebsiteNavigationMapper.deleteBatchIds(Arrays.asList(ids)));
    }





    @Override
    public R<List<OfficialWebsiteNavigation>> getOfficialWebsiteNavigation(OfficialWebsiteNavigation req) {


        QueryWrapper<OfficialWebsiteNavigation> queryWrapper = new QueryWrapper<>();
        queryWrapper.select(getColumns(req.getLanguage())) ;

        queryWrapper.eq("system_id",1);
        queryWrapper.eq("is_show",1);
        queryWrapper.orderByAsc("father_id").orderByAsc("sort");
        List<OfficialWebsiteNavigation> list = officialWebsiteNavigationMapper.selectList(queryWrapper);
        if (CollUtil.isEmpty(list)) {
            return R.ok(list);
        }

        //组装返回值
        //过滤出所有一级导航
        List<OfficialWebsiteNavigation> oneList = list.stream().filter(x -> x.getFatherId().equals(0)).collect(Collectors.toList());

        //循环一级导航
        for (OfficialWebsiteNavigation one : oneList) {

            //根据一级导航过滤出二级导航
            List<OfficialWebsiteNavigation> twoList = list.stream().filter(x -> one.getId().equals(x.getFatherId())).collect(Collectors.toList());

            if(CollUtil.isNotEmpty(twoList)){
                for (OfficialWebsiteNavigation two : twoList) {
                    List<OfficialWebsiteNavigation> threeList = list.stream().filter(x -> two.getId().equals(x.getFatherId())).collect(Collectors.toList());
                    if (CollUtil.isNotEmpty(threeList)) {
                        two.setChildList(threeList);
                    }
                }
            }


            if (CollUtil.isNotEmpty(twoList)) {
                one.setChildList(twoList);
            }

        }
        return R.ok(oneList);
    }
}
