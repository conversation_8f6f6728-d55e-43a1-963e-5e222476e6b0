package com.rw.common.utils;

import cn.hutool.core.convert.Convert;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class RedisUtil {

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    public boolean del(String key) {
        if (log.isDebugEnabled()) {
            log.debug("redis del key:{}", key);
        }
        try {
            stringRedisTemplate.delete(key);
            return true;
        } catch (Exception e) {
            log.error("redis del key:{},value:{} error", key, e);
            return false;
        }
    }

    public String get(String key) {
        if (log.isDebugEnabled()) {
            log.debug("redis get key:{}", key);
        }
        try {
            return stringRedisTemplate.opsForValue().get(key);
        } catch (Exception e) {
            log.error("redis get key:{} error", key, e);
            return null;
        }
    }

    public boolean set(String key, String value, long timeout, TimeUnit unit) {
        if (log.isDebugEnabled()) {
            log.debug("redis set key:{}", key);
        }
        try {
            stringRedisTemplate.opsForValue().set(key, value, timeout, unit);
            return true;
        } catch (Exception e) {
            log.error("redis set key:{} error", key, e);
            return false;
        }
    }

    public boolean set(String key, String value) {
        if (log.isDebugEnabled()) {
            log.debug("redis set key:{}", key);
        }
        try {
            stringRedisTemplate.opsForValue().set(key, value);
            return true;
        } catch (Exception e) {
            log.error("redis set key:{} error", key, e);
            return false;
        }
    }

    /**
     * sex nx
     */
    public boolean setNx(String key, String value, Duration duration) {
        if (log.isDebugEnabled()) {
            log.debug("redis set nx key:{}", key);
        }
        Boolean b = null;
        try {
            b = stringRedisTemplate.opsForValue().setIfAbsent(key, value, duration);
        } catch (Exception e) {
            log.error("redis set nx key:{} error", key, e);
        }
        return (b != null && b);
    }

    public boolean setNx(String key, String value, int i, TimeUnit days) {
        return setNx(key, value, Duration.ofDays(365));
    }


    public boolean exist(String key) {
        return null != get(key);
    }


    public Long increment(String key) {
        if (log.isDebugEnabled()) {
            log.debug("redis set nx key:{}", key);
        }
//        Long b = null;
        try {
            return stringRedisTemplate.opsForValue().increment(key);
        } catch (Exception e) {
            log.error("redis set nx key:{} error", key, e);
        }
        return 0L;
    }
    public Long incrBy(String key,Long count) {
        if (log.isDebugEnabled()) {
            log.debug("redis set nx key:{}", key);
        }
//        Long b = null;
        try {
            return stringRedisTemplate.opsForValue().increment(key, count);
        } catch (Exception e) {
            log.error("redis set nx key:{} error", key, e);
        }
        return 0L;
    }

    public Long decrement(String key) {
        try {
            return stringRedisTemplate.opsForValue().decrement(key);
        } catch (Exception e) {
            log.error("redis set nx key:{} error", key, e);
        }
        return 0L;
    }

    public boolean sadd(String key, String... values) {

        try {
            stringRedisTemplate.opsForSet().add(key,values);
            return true;
        } catch (Exception e) {
            log.error("redis set key:{} error", key, e);
            return false;
        }
    }

    public boolean rpush(String key, String... values) {

        try {
            stringRedisTemplate.opsForList().rightPushAll(key,values);
            return true;
        } catch (Exception e) {
            log.error("redis set key:{} error", key, e);
            return false;
        }

    }
    public  String rpop(String key) {
      return   stringRedisTemplate.opsForList().rightPop(key);
    }

    public List<String> range(String key, long start, long end) {

        try {
            return stringRedisTemplate.opsForList().range(key,start,end);
        } catch (Exception e) {
            log.error("redis set key:{} error", key, e);
            return null;
        }
    }
    public Long len(String key) {

        try {
            return stringRedisTemplate.opsForList().size(key);
        } catch (Exception e) {
            log.error("redis set key:{} error", key, e);
            return 0L;
        }
    }

    public Set<String> smem(String key) {
        try {
            return stringRedisTemplate.opsForSet().members(key);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return new HashSet<>();
    }
    public void srem(String key, String... members) {
        stringRedisTemplate.opsForSet().remove(key, members);
    }

    public Long srem(String key, String members) {
        return stringRedisTemplate.opsForSet().remove(key, members);

    }
    public boolean sisMember(String key, String member) {
        return stringRedisTemplate.opsForSet().isMember(key, member);
    }
    public long scard(String key) {
        return Convert.toLong(stringRedisTemplate.opsForSet().size(key),0L);
    }
    public List<String> popN(String key, Long count) {
        return stringRedisTemplate.opsForSet().pop(key,count);
    }

    public String pop(String key) {
        return stringRedisTemplate.opsForSet().pop(key);
    }


    /**
     * 返回redis对象
     */
    public StringRedisTemplate getTemplate() {
        return stringRedisTemplate;
    }

    public void expireKey(String key, long timeout, TimeUnit unit) {
        stringRedisTemplate.expire(key, timeout, unit);
    }

    public void  hdel(String key, String field) {
        stringRedisTemplate.opsForHash().delete(key, field);
    }
    public boolean hexists(String key, String field) {
        return stringRedisTemplate.opsForHash().hasKey(key, field);
    }

    public void hset(String key, String field, String value) {
        stringRedisTemplate.opsForHash().put(key, field, value);
    }

    public String hget(String key, String field) {
        return Convert.toStr(stringRedisTemplate.opsForHash().get(key, field),"");
    }

    public Map<Object, Object> hgetAll(String key){
        return  stringRedisTemplate.opsForHash().entries(key);
    }
}
