# Spring
server:
  port: ${SERVER_PORT:8084}
spring:
  main:
    allow-bean-definition-overriding: true
  application:
    # 应用名称
    name: rw-common-api
  profiles:
    # 环境配置
    active: ${SPRING_PROFILES_ACTIVE:dev}

mybatis-plus:
  global-config:
    banner: false
    db-config:
      logic-delete-value: 1 # 逻辑已删除值(默认为 1)
      logic-not-delete-value: 0 # 逻辑未删除值(默认为 0)
      id-type: auto
logging:
  config: classpath:logback-spring.xml
