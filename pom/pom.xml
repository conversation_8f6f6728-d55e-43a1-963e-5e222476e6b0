<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

    <modelVersion>4.0.0</modelVersion>
    <groupId>com.rw.pom</groupId>
    <artifactId>rw-version-tag</artifactId>
    <version>1.0.0-SNAPSHOT</version>
    <name>ruiwu</name>
    <url>https://www.xiats.com/</url>
    <description>尧曦未来</description>
    <packaging>pom</packaging>
    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <java.version>1.8</java.version>
        <spring-boot.version>2.6.7</spring-boot.version>
        <spring-cloud.version>2021.0.1</spring-cloud.version>
        <spring-cloud-alibaba.version>2021.0.1.0</spring-cloud-alibaba.version>
        <alibaba.nacos.version>2.0.4</alibaba.nacos.version>
        <spring-boot-admin.version>2.6.6</spring-boot-admin.version>
        <spring-boot.mybatis>2.2.2</spring-boot.mybatis>
        <swagger.fox.version>3.0.0</swagger.fox.version>
        <swagger.core.version>1.6.2</swagger.core.version>
        <tobato.version>1.27.2</tobato.version>
        <kaptcha.version>2.3.2</kaptcha.version>
        <pagehelper.boot.version>1.4.1</pagehelper.boot.version>
        <druid.version>1.2.8</druid.version>
        <dynamic-ds.version>3.5.0</dynamic-ds.version>
        <commons.io.version>2.11.0</commons.io.version>
        <commons.fileupload.version>1.4</commons.fileupload.version>
        <velocity.version>2.3</velocity.version>
        <fastjson.version>1.2.80</fastjson.version>
        <jjwt.version>0.9.1</jjwt.version>
        <minio.version>8.2.2</minio.version>
        <poi.version>4.1.2</poi.version>
        <commons-collections.version>3.2.2</commons-collections.version>
        <transmittable-thread-local.version>2.12.2</transmittable-thread-local.version>
        <lombok.version>1.18.24</lombok.version>
        <mybatis-plus.version>3.5.1</mybatis-plus.version>
        <hutool.version>5.8.0.M3</hutool.version>
        <skywalking.version>9.0.0</skywalking.version>

    </properties>
    <dependencies>
        <!-- bootstrap 启动器 -->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-bootstrap</artifactId>
            <version>3.1.1</version>
        </dependency>
    </dependencies>
    <!-- 依赖声明 -->
    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-boot-starter</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>dynamic-datasource-spring-boot-starter</artifactId>
                <version>3.5.0</version>
            </dependency>
            <!--  alibaba log  -->
            <dependency>
                <groupId>com.google.protobuf</groupId>
                <artifactId>protobuf-java</artifactId>
                <version>2.5.0</version>
            </dependency>
            <dependency>
                <groupId>com.aliyun.openservices</groupId>
                <artifactId>aliyun-log-logback-appender</artifactId>
                <version>0.1.18</version>
            </dependency>
            <!--  alibaba log  end  -->
            <dependency>
                <groupId>com.aliyun.oss</groupId>
                <artifactId>aliyun-sdk-oss</artifactId>
                <version>3.10.2</version>
            </dependency>
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>${hutool.version}</version>
            </dependency>
            <!-- SpringCloud 微服务 -->
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring-cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <!-- SpringCloud Alibaba 微服务 -->
            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-alibaba-dependencies</artifactId>
                <version>${spring-cloud-alibaba.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <!-- Alibaba Nacos 配置 -->
            <dependency>
                <groupId>com.alibaba.nacos</groupId>
                <artifactId>nacos-client</artifactId>
                <version>${alibaba.nacos.version}</version>
            </dependency>
            <!-- SpringBoot 依赖配置 -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <!--  SpringBoot 监控客户端 -->
            <!--            <dependency>-->
            <!--                <groupId>de.codecentric</groupId>-->
            <!--                <artifactId>spring-boot-admin-starter-client</artifactId>-->
            <!--                <version>${spring-boot-admin.version}</version>-->
            <!--            </dependency>-->
            <!--            &lt;!&ndash; FastDFS 分布式文件系统 &ndash;&gt;-->
            <!--            <dependency>-->
            <!--                <groupId>com.github.tobato</groupId>-->
            <!--                <artifactId>fastdfs-client</artifactId>-->
            <!--                <version>${tobato.version}</version>-->
            <!--            </dependency>-->
            <!-- Mybatis 依赖配置 -->
            <dependency>
                <groupId>org.mybatis.spring.boot</groupId>
                <artifactId>mybatis-spring-boot-starter</artifactId>
                <version>${spring-boot.mybatis}</version>
            </dependency>
            <!-- Swagger 依赖配置 -->
            <dependency>
                <groupId>io.swagger</groupId>
                <artifactId>swagger-models</artifactId>
                <version>${swagger.core.version}</version>
            </dependency>
            <dependency>
                <groupId>io.swagger</groupId>
                <artifactId>swagger-annotations</artifactId>
                <version>${swagger.core.version}</version>
            </dependency>
            <!-- 验证码 -->
            <dependency>
                <groupId>com.github.penggle</groupId>
                <artifactId>kaptcha</artifactId>
                <version>${kaptcha.version}</version>
            </dependency>
            <!-- pagehelper 分页插件 -->
            <dependency>
                <groupId>com.github.pagehelper</groupId>
                <artifactId>pagehelper-spring-boot-starter</artifactId>
                <version>${pagehelper.boot.version}</version>
            </dependency>
            <!-- io常用工具类 -->
            <dependency>
                <groupId>commons-io</groupId>
                <artifactId>commons-io</artifactId>
                <version>${commons.io.version}</version>
            </dependency>
            <!-- excel工具 -->
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-ooxml</artifactId>
                <version>${poi.version}</version>
            </dependency>
            <!-- 文件上传工具类 -->
            <dependency>
                <groupId>commons-fileupload</groupId>
                <artifactId>commons-fileupload</artifactId>
                <version>${commons.fileupload.version}</version>
            </dependency>
            <!-- 代码生成使用模板 -->
            <dependency>
                <groupId>org.apache.velocity</groupId>
                <artifactId>velocity-engine-core</artifactId>
                <version>${velocity.version}</version>
            </dependency>
            <!-- Collection 增强Java集合框架 -->
            <dependency>
                <groupId>commons-collections</groupId>
                <artifactId>commons-collections</artifactId>
                <version>${commons-collections.version}</version>
            </dependency>
            <!-- JSON 解析器和生成器 -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>${fastjson.version}</version>
            </dependency>
            <!-- JWT -->
            <dependency>
                <groupId>io.jsonwebtoken</groupId>
                <artifactId>jjwt</artifactId>
                <version>${jjwt.version}</version>
            </dependency>
            <!-- 线程传递值 -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>transmittable-thread-local</artifactId>
                <version>${transmittable-thread-local.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.skywalking</groupId>
                <artifactId>apm-toolkit-logback-1.x</artifactId>
                <version>${skywalking.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <profiles>

        <profile>
            <id>public</id>
            <repositories>
                <repository>
                    <id>maven-public</id>
                    <releases>
                        <enabled>true</enabled>
                    </releases>
                    <snapshots>
                        <enabled>true</enabled>
                        <updatePolicy>always</updatePolicy>
                        <checksumPolicy>ignore</checksumPolicy>
                    </snapshots>
                    <url>https://maven.aliyun.com/repository/public</url>
                </repository>
            </repositories>
            <distributionManagement>
                <repository>
                    <id>public</id>
                    <url>https://maven.aliyun.com/repository/public</url>
                </repository>
            </distributionManagement>
        </profile>

        <profile>
            <id>dev</id>
            <repositories>
                <repository>
                    <id>maven-public</id>
                    <releases>
                        <enabled>true</enabled>
                    </releases>
                    <snapshots>
                        <enabled>true</enabled>
                        <updatePolicy>always</updatePolicy>
                        <checksumPolicy>ignore</checksumPolicy>
                    </snapshots>
                    <url>https://packages.aliyun.com/maven/repository/2022079-snapshot-u0gabD/</url>
                </repository>
                <repository>
                    <id>dev</id>
                    <releases>
                        <enabled>true</enabled>
                    </releases>
                    <snapshots>
                        <enabled>true</enabled>
                        <updatePolicy>always</updatePolicy>
                        <checksumPolicy>ignore</checksumPolicy>
                    </snapshots>
                    <url>https://packages.aliyun.com/maven/repository/2022079-snapshot-u0gabD/</url>
                </repository>
            </repositories>
            <distributionManagement>
                <repository>
                    <id>dev</id>
                    <url>https://packages.aliyun.com/maven/repository/2022079-snapshot-u0gabD/</url>
                </repository>
            </distributionManagement>
        </profile>


        <profile>
            <id>prod</id>
            <repositories>
                <repository>
                    <id>maven-public</id>
                    <releases>
                        <enabled>true</enabled>
                    </releases>
                    <snapshots>
                        <enabled>true</enabled>
                        <updatePolicy>always</updatePolicy>
                        <checksumPolicy>ignore</checksumPolicy>
                    </snapshots>
                    <url>https://packages.aliyun.com/maven/repository/2022079-snapshot-u0gabD/</url>
                </repository>
                <repository>
                    <id>prod</id>
                    <releases>
                        <enabled>true</enabled>
                    </releases>
                    <snapshots>
                        <enabled>true</enabled>
                        <updatePolicy>always</updatePolicy>
                        <checksumPolicy>ignore</checksumPolicy>
                    </snapshots>
                    <url>https://packages.aliyun.com/maven/repository/2022079-snapshot-u0gabD/</url>
                </repository>
            </repositories>
            <distributionManagement>
                <repository>
                    <id>prod</id>
                    <url>https://packages.aliyun.com/maven/repository/2022079-snapshot-u0gabD/</url>
                </repository>
            </distributionManagement>
        </profile>

        <!--  mvn clean package -Pdev,docker  -->
        <profile>
            <!--  推荐方式, 自动推送镜像, 并使用统一独立的Dockerfile  -->
            <id>dockerpush</id>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.springframework.boot</groupId>
                        <artifactId>spring-boot-maven-plugin</artifactId>
                    </plugin>
                    <plugin>
                        <groupId>com.spotify</groupId>
                        <!-- <artifactId>docker-maven-plugin</artifactId>  -->
                        <artifactId>dockerfile-maven-plugin</artifactId>
                        <version>${docker.plugin.version}</version>
                        <configuration>
                            <!--  推送镜像名称  -->
                            <!--                            <imageName>${docker.registry}/${image.env}/${image.name}</imageName>-->
                            <repository>${docker.registry}/${image.env}/${image.name}</repository>
                            <tag>latest</tag>
                            <!--  Dockerfile 路径  -->
                            <dockerDirectory>${project.build.directory}/target</dockerDirectory>
                            <!--                            <serverId>docker-registry</serverId>-->
                            <buildArgs>
                                <!--  打包会传入以下变量  -->
                                <PROJECT_NAME>${project.artifactId}</PROJECT_NAME>
                                <IMAGE_ENV>${image.env}</IMAGE_ENV>
                                <BUILD_JAR>${project.build.finalName}.jar</BUILD_JAR>
                            </buildArgs>
                        </configuration>
                        <executions>
                            <execution>
                                <id>build-image</id>
                                <phase>package</phase>
                                <goals>
                                    <goal>build</goal>
                                    <goal>push</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>

    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>3.8.1</version>
                    <configuration>
                        <source>${java.version}</source>
                        <target>${java.version}</target>
                        <encoding>${project.build.sourceEncoding}</encoding>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-surefire-plugin</artifactId>
                    <version>2.22.2</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-deploy-plugin</artifactId>
                    <version>2.8.2</version>
                </plugin>
                <plugin>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-maven-plugin</artifactId>
                    <version>${spring-boot.version}</version>
                    <executions>
                        <execution>
                            <goals>
                                <goal>repackage</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
            </plugins>
        </pluginManagement>

    </build>
</project>
