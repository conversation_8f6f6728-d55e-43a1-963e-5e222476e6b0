// Header top
function collapseNavbar() {
    if ($(window).scrollTop() > 50) {
        $('.navbar-fixed-top').addClass('top-nav-collapse');
    } else {
        $('.navbar-fixed-top').removeClass('top-nav-collapse');
    }
}
//Nav search
$(document).ready(function () {
    $('.navbar-fixed-top .nav-search i').click(function () {
        $('.navbar-fixed-top .search-box').slideToggle();
    });
});

$(window).scroll(collapseNavbar);
$(document).ready(collapseNavbar);

$(window).load(function () {
    // Banner
    $('.flexslider').flexslider({
        animation: 'fade',
    });
});
// Vertical TAB
$(document).ready(function () {
    function close_accordion_section() {
        $('.accordion .accordion-section-title').removeClass('active');
        $('.accordion .accordion-section-content').slideUp(300).removeClass('open');
    }

    $('.accordion-section-title').click(function (e) {
        var currentAttrValue = $(this).attr('href');
        if ($(e.target).is('.active')) {
            close_accordion_section();
        } else {
            close_accordion_section();
            $(this).addClass('active');
            $('.accordion ' + currentAttrValue)
                .slideDown(300)
                .addClass('open');
        }
        e.preventDefault();
    });

    $('#parentVerticalTab').easyResponsiveTabs({
        type: 'vertical', //Types: default, vertical, accordion
        width: 'auto', //auto or any width like 600px
        fit: true, // 100% fit in a container
        closed: 'accordion', // Start closed if in accordion view
        tabidentify: 'hor_1', // The tab groups identifier
        activate: function (event) {
            // Callback function if tab is switched
            var $tab = $(this);
            var $info = $('#nested-tabInfo2');
            var $name = $('span', $info);
            $name.text($tab.text());
            $info.show();
        },
    });

    // show testimonial tab
    $('.testimonials-tab-list ul li a').click(function () {
        $('.testimonials-tab-list ul li').removeClass('active');
        $('.testimonials-tab-content .testimonial-con').removeClass('active');
        $('.testimonials-tab-content .testimonial-con').fadeOut('fast');
        $(this).parent().addClass('active');
        $('.testimonial-tab #testimonial-' + $(this).attr('data-tab'))
            .fadeIn(1000)
            .addClass('active');
    });
});
//Go top
$(window).scroll(function () {
    if ($(this).scrollTop() > 100) {
        $('.service-right').stop().show().animate({ bottom: '-202px' }, 300);
        $('.go-top-mobile').stop().show();
    } else {
        $('.service-right').stop().animate({ bottom: '-700px' }, 300);
        $('.go-top-mobile').stop().hide();
    }
});
$('.go-top').click(function () {
    $('body,html').animate({ scrollTop: 0 }, 500);
    $('.service-right').animate({ bottom: '0px', opacity: '0' }, 500);
    return false;
});

//term list
window.onload = function () {
    var termHeight = $('.term').outerHeight();
    console.log('termHeight==', termHeight);
    $('.term-list').each(function () {
        var dl = $(this);

        dl.children('dt')
            .children('.fa-angle-down')
            .click(function () {
                $('.term-list').children('dd').hide();
                $('.term-list').children('dt').children('.fa-angle-down').show();
                $('.term-list').children('dt').children('.fa-angle-up').hide();
                dl.children('dt').children('.fa-angle-down').hide();
                dl.children('dt').children('.fa-angle-up').show();
                dl.children('dd').css('top', termHeight + 'px');
                dl.children('dd').show();
            });
        dl.children('dt')
            .children('.fa-angle-up')
            .click(function () {
                dl.children('dt').children('.fa-angle-up').hide();
                dl.children('dt').children('.fa-angle-down').show();
                $('.term-list').children('dd').hide();
                // dl.children('dd').toggle();
            });
    });
};
