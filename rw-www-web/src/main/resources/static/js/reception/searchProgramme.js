let _classifyParam = {};

$(function () {
    const $classify = $("#classify").val();
    setSelectOption();
    function setSelectOption() {
        let thisUri = window.location.href;
        let regExp = new RegExp("programList");
        if (regExp.test(thisUri) && new RegExp("&").test(thisUri)) {
            let params = location.search.substring(1, location.search.length).split("&");
            for (let item of params) {
                let param = item.split("=");
                if (param[0] === "labels"){
                    $.each(param[1].split(","), function (index, item) {
                        if (item === "" || item === undefined)
                            return true;
                        let $this = $(".classify[data-id="+ item +"]");
                        let $type = $this.parents("dl").find("dt").attr("data-type");
                        selectType($type, item);
                        if ($type !== "other"){
                            $this.parents("dl").find("dd a").removeClass("active");
                        }else {
                            $this.parents("dl").find("dd a").first().removeClass("active");
                        }
                        $this.addClass("active");
                    });
                }
            }
        } else if (regExp.test(thisUri)) {
            let param = location.search.substring(1, location.search.length).split("=");
            selectType(param[0], param[1]);
        }
    }

    $(".classify").click(function () {
        let $this = $(this);
        let $type = $this.parents("dl").find("dt").attr("data-type");
        let $id = $this.attr("data-id");


        selectType("classify", $classify);
        selectType("pageNo", "");
        selectType($type, $id);
        productLocation();
    });

    function productLocation() {
        let $productUri = "/program/programList?";
        labelId = [];
        for (let item in _classifyParam) {
            if (item === "pageNo" || item === "classify"){
                $productUri += item + "=" + _classifyParam[item] + "&";
            }else{
                labelId.push(_classifyParam[item]);
            }
        }
        $productUri += "labels=" + labelId.toString();


        $productUri=$productUri.endsWith("&")? $productUri.slice(0, -1) : $productUri;
        window.location.href = $productUri;
    }

    function selectType(type, val) {
        if (val === "" || val === undefined){
            val = "";
        }else {
            val = Number(val);
        }
        _classifyParam[type] = val;
    }

    $(".ins-page a").click(function () {
        let $this = $(this);
        selectType("classify", $classify);
        selectType("pageNo", $this.attr("data-page"));
        productLocation();
    });

    $(".clear-option").click(function () {
        let $this = $(this);
        _classifyParam = {};
        selectType("classify", $classify);
        productLocation();
    })

});