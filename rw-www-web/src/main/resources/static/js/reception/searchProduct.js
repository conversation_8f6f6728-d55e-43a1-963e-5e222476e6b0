let _classifyParam = {};
$(function () {
    const $mainclass = $("#mainclass").val();

    setSelectOption();
    function setSelectOption(){
        let thisUri = window.location.href;
        let regExp = new RegExp("products");
        if (regExp.test(thisUri) && new RegExp("&").test(thisUri)){
            let params = location.search.substring(1,location.search.length).split("&");
            for (let item of params){
               let param = item.split("=");
               selectType(param[0], param[1]);
               $(".term-list dt").each(function () {
                    let $this = $(this);
                    if ($this.attr("data-type") === param[0]){
                        let field = $this.siblings("dd").find("a");
                        field.removeClass("active");
                        // $(".classify[data-id="+ param[1] +"]").addClass("active");
                        field.each(function () {
                            let $thisField = $(this);
                            if ($thisField.attr("data-id") === param[1]){
                                $thisField.addClass("active");
                                return false;
                            }
                        });
                        return false;
                    }
               })
            }
        }else if (regExp.test(thisUri)){
            let param = location.search.substring(1,location.search.length).split("=");
            selectType(param[0], param[1]);
        }
    }

    $(".classify").click(function () {
        let $this = $(this);
        let $type = $this.parents("dl").find("dt").attr("data-type");
        let $id = $this.attr("data-id");

        _classifyParam["mainclass"] = $mainclass;

        selectType("pageNo", "");
        selectType($type, $id);
        productLocation();
    });

    function productLocation() {
        var $lang = $("#lang").val();
        let $productUri = "/product/products?";
        if ($lang!=undefined && $lang!="" && $lang=="en"){
            $productUri="/"+$lang+$productUri;
        }
        for (let item in _classifyParam){
            $productUri += item + "=" + _classifyParam[item] + "&";
        }

        $productUri=$productUri.endsWith("&")? $productUri.slice(0, -1) : $productUri;
        window.location.href = $productUri;
    }

    function selectType(type, val) {
        if (val === "" || val === undefined){
            val = "";
        }
        _classifyParam[type] = val;
    }

    $(".ins-page a").click(function () {
        let $this = $(this);
        selectType("mainclass", $mainclass);
        selectType("pageNo", $this.attr("data-page"));
        productLocation();
    });

    $(".clear-option").click(function () {
        let $this = $(this);
        _classifyParam = {};
        selectType("mainclass", $mainclass);
        productLocation();
    })

});