$(function () {
    $(".down-data").click(function () {
        event.preventDefault();
        let $this = $(this);
        let form = $("<form></form>");
        form.attr('action', 'http://127.0.0.1:8080/rw/file/downData');
        form.attr('method', 'post');
        let input1 = $("<input type='text' name='path' />");
        input1.attr('value', $this.attr("data-href"));
        form.append(input1);
        form.appendTo("body");
        form.css('display', 'none');
        form.submit();
    })
});
