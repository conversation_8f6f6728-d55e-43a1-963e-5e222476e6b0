/*------------------------Imported styles------------------------*/
@import url('https://fonts.googleapis.com/css?family=Open+Sans:400,300,600,700,800');
@import url(public.css);
* {
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
}
:after,
:before {
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
}
/*-------------------------------Public--------------------------*/
body {
    width: 100%;
    height: 100%;
    font-family: 'Open Sans', 'Helvetica Neue', Helvetica, sans-serif;
    color: #000;
    background-color: #fff;
    font-size: 14px;
    line-height: 1.42857143;
    color: #333;
    margin: 0;
    padding: 0;
}
html {
    width: 100%;
    height: 100%;
}
h1,
h2,
h3,
h4,
h5,
h6 {
    font-weight: normal;
}
a {
    text-decoration: none;
    transition: color 0.3s ease;
    -webkit-transition: color 0.3s ease;
    -moz-transition: color 0.3s ease;
    -o-transition: color 0.3s ease;
    color: #337ab7;
}
a:focus,
a:hover {
    text-decoration: none;
    background-color: transparent;
    color: #35abdf;
    outline: none;
}
input[type='password'],
input[type='text'],
textarea {
    border: none;
    width: 100%;
    display: block;
    margin-bottom: 20px;
    height: 40px;
    line-hegiht: 40px;
    padding: 0;
    border-bottom: 1px solid #e9e9e9;
    font-weight: 300;
    color: #989898;
    transition: all 0.3s ease;
    -webkit-transition: all 0.3s ease;
    -moz-transition: all 0.3s ease;
    -o-transition: all 0.3s ease;
}
input[type='password']:focus,
input[type='text']:focus,
select:focus,
textarea:focus {
    border-color: #ccc;
}
textarea {
    min-height: 100px;
}
button,
input[type='submit'] {
    border: none;
    cursor: pointer;
}
body {
    webkit-tap-highlight-color: rgba(255, 255, 255, 0.2);
}
li,
ul {
    margin: 0;
    padding: 0;
    list-style: none;
}
em {
    font-style: normal;
}
.left {
    float: left;
}
.right {
    float: right;
}
.clear {
    clear: both;
    float: none;
}
.ml-0 {
    margin-left: 0px !important;
}
.mt-20 {
    margin-top: 20px;
}
.mt-30 {
    margin-top: 30px;
}
.ml-5 {
    margin-left: 5px;
}
.mr-5 {
    margin-right: 5px;
}
.vi_mid {
    vertical-align: middle;
}
.form-control {
    display: block;
    width: 100%;
    height: 34px;
    padding: 6px 12px;
    font-size: 14px;
    line-height: 1.42857143;
    color: #555;
    background-color: #fff;
    background-image: none;
    border: 1px solid #ccc;
    border-radius: 4px;
    -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
    box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
    -webkit-transition: border-color ease-in-out 0.15s, -webkit-box-shadow ease-in-out 0.15s;
    -o-transition: border-color ease-in-out 0.15s, box-shadow ease-in-out 0.15s;
    transition: border-color ease-in-out 0.15s, box-shadow ease-in-out 0.15s;
}
.form-control:focus {
    border-color: #66afe9;
    outline: 0;
    -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 8px rgba(102, 175, 233, 0.6);
    box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 8px rgba(102, 175, 233, 0.6);
}
.form-control::-moz-placeholder {
    color: #999;
    opacity: 1;
}
.form-control:-ms-input-placeholder {
    color: #999;
}
.form-control::-webkit-input-placeholder {
    color: #999;
}
.form-control::-ms-expand {
    background-color: transparent;
    border: 0;
}
.btn-default {
    background-color: #01a3f6;
    line-height: 40px;
    height: 40px;
    color: #fff;
    text-transform: uppercase;
    border: none;
    font-size: 13px;
    font-weight: 400;
    cursor: pointer;
    padding: 0 30px;
    margin: 0;
    border-radius: 25px;
    -webkit-border-radius: 25px;
    -moz-border-radius: 25px;
    box-shadow: inset 0 -3px 0 0 rgba(0, 0, 0, 0.15);
    -webkit-box-shadow: inset 0 -3px 0 0 rgba(0, 0, 0, 0.15);
    -moz-box-shadow: inset 0 -3px 0 0 rgba(0, 0, 0, 0.15);
    -o-box-shadow: inset 0 -3px 0 0 rgba(0, 0, 0, 0.15);
    display: inline-block;
}
.btn-default:active,
.btn-default:focus,
.btn-default:hover {
    color: #fff;
    background-color: #01a3f6;
    -webkit-box-shadow: none;
    box-shadow: none;
    outline: 0;
}
.btn-secondary {
    background-color: #a6a6a6;
    line-height: 40px;
    height: 40px;
    color: #fff;
    text-transform: uppercase;
    border: none;
    font-size: 13px;
    font-weight: 600;
    cursor: pointer;
    padding: 0 30px;
    margin: 0;
    border-radius: 25px;
    -webkit-border-radius: 25px;
    -moz-border-radius: 25px;
    box-shadow: inset 0 -3px 0 0 rgba(0, 0, 0, 0.15);
    -webkit-box-shadow: inset 0 -3px 0 0 rgba(0, 0, 0, 0.15);
    -moz-box-shadow: inset 0 -3px 0 0 rgba(0, 0, 0, 0.15);
    -o-box-shadow: inset 0 -3px 0 0 rgba(0, 0, 0, 0.15);
    display: inline-block;
}
.btn-secondary:active,
.btn-secondary:focus,
.btn-secondary:hover {
    color: #fff;
    background-color: #01a3f6;
    -webkit-box-shadow: none;
    box-shadow: none;
    outline: 0;
}
#page-content {
    margin-top: 80px;
    min-height: calc(100vh - 168px);
}
#page-content h2 {
    font-weight: 300;
    font-size: 30px;
    color: #222f3e;
    margin-top: 50px;
    margin-bottom: 20px;
}
#page-content h5 {
    background: #f6f7fb;
    line-height: 30px;
    padding: 8px 20px;
    margin-bottom: 0;
}
.marginTop {
    margin-top: 70px;
}
.float-left {
    float: left !important;
}
.float-right {
    float: right !important;
}
.float-none {
    float: none !important;
}
.d-none {
    display: none !important;
}
.d-block {
    display: block !important;
}
.pos-relative {
    position: relative;
}
.navbar-fixed-bottom,
.navbar-fixed-top {
    position: fixed;
    right: 0;
    left: 0;
    z-index: 1030;
}
@media (min-width: 768px) and (max-width: 1169px) {
    #page-content {
        margin-top: 80px;
    }
}
@media (min-width: 992px) {
    #page-content {
        min-height: calc(100vh - 168px);
    }
    .d-lg-none {
        display: none !important;
    }
    .d-lg-block {
        display: block !important;
    }
}
@media (max-width: 1200px) {
    #page-content {
        min-height: calc(100vh - 120px);
    }
    .index #page-content {
        min-height: auto;
    }
}
@media (max-width: 767px) {
    body {
        font-size: 12px;
    }
    #page-content {
        margin-top: 80px;
        min-height: calc(100vh - 160px);
    }
    .marginTop {
        margin-top: 50px;
    }
}
@media screen and (max-width: 479px) {
    #page-content h2 {
        font-size: 20px;
        margin: 20px 0;
    }
    .marginTop {
        margin-top: 40px;
    }
    .m-none {
        display: none !important;
    }
}
.btn {
    display: inline-block;
    padding: 6px 12px;
    margin-bottom: 0;
    font-weight: 400;
    line-height: 1.42857143;
    text-align: center;
    white-space: nowrap;
    vertical-align: middle;
    -ms-touch-action: manipulation;
    touch-action: manipulation;
    cursor: pointer;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    background-image: none;
    border: 1px solid transparent;
    border-radius: 4px;
}
.btn.active.focus,
.btn.active:focus,
.btn.focus,
.btn:active.focus,
.btn:active:focus,
.btn:focus {
    outline: thin dotted;
    outline: 5px auto -webkit-focus-ring-color;
    outline-offset: -2px;
}
.btn.focus,
.btn:focus,
.btn:hover {
    color: #333;
    text-decoration: none;
}
.btn.active,
.btn:active {
    background-image: none;
    outline: 0;
    -webkit-box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
    box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
}
.btn.disabled,
.btn[disabled],
fieldset[disabled] .btn {
    cursor: not-allowed;
    filter: alpha(opacity=65);
    -webkit-box-shadow: none;
    box-shadow: none;
    opacity: 0.65;
}
a.btn.disabled,
fieldset[disabled] a.btn {
    pointer-events: none;
}

/* ---------------------------------Logo and tel---------------------------- */
.logo {
    float: left;
}
.logo a {
    float: left;
    height: 50px;
    padding-right: 15px;
}
.logo a:focus,
.logo a:hover {
    text-decoration: none;
}
.logo a > img {
    display: block;
}
.logo p {
    border-left: 1px solid #465a6f;
    padding-left: 15px;
    float: left;
    color: #cddae7;
    font-size: 12px;
    line-height: 20px;
    margin: 5px 0;
}
.logo p b {
    font-size: 16px;
    font-weight: 600;
    display: block;
    color: #01a3f6;
}
@media screen and (max-width: 480px) {
    .logo {
        margin-left: -10px;
    }
    .logo p {
        padding-left: 8px;
    }
    .navbar-brand {
        padding-right: 8px;
    }
}
/* ---------------------------------Header fixed top---------------------------- */
.navbar-fixed-top {
    background: #253242;
    top: 0px;
    padding: 15px 0;
}
@media (min-width: 768px) {
    .navbar-fixed-top {
        -webkit-transition: background 0.5s ease-in-out, padding 0.3s ease-in-out;
        -moz-transition: background 0.5s ease-in-out, padding 0.3s ease-in-out;
        -o-transition: background 0.5s ease-in-out, padding 0.3s ease-in-out;
        transition: background 0.5s ease-in-out, padding 0.3s ease-in-out;
    }
    .navbar-custom.top-nav-collapse {
        padding: 10px 0;
        top: 0;
    }
    .navbar-custom.top-nav-collapse .nav li.dropdown ul.dropdown-menu {
        top: 122%;
    }
}
@media (min-width: 981px) and (max-width: 1169px) {
    .navbar-custom .nav {
        margin-top: 15px;
    }
    .navbar-custom .nav li a {
        padding: 10px 22px;
    }
    .navbar-custom .nav li.dropdown ul.dropdown-menu {
        top: 165%;
    }
    .navbar-custom.top-nav-collapse .nav li.dropdown ul.dropdown-menu {
        top: 155%;
    }
}
@media (min-width: 768px) and (max-width: 980px) {
    .navbar-toggle {
        display: block !important;
    }
    .navbar-collapse.collapse {
        display: none !important;
    }
    .navbar-collapse.in {
        display: block !important;
    }
    .navbar-collapse {
        margin-top: 0;
        padding: 0;
        float: none;
    }
}
@media (max-width: 767px) {
    .top-nav-collapse {
        top: 0;
    }
}
/* ---------------------------------Navigation---------------------------- */
.main-nav,
.main-nav * {
    margin: 0;
    padding: 0;
    list-style: none;
}
.main-nav {
    display: block;
}
.main-nav > ul > li {
    position: relative;
    white-space: nowrap;
    float: left;
}
.main-nav a {
    display: block;
    position: relative;
    color: #98acc0;
    padding: 10px 14px;
    line-height: 30px;
    transition: 0.3s;
    font-size: 14px;
    text-transform: uppercase;
}
.main-nav a:hover,
.main-nav .active > a,
.main-nav li:hover > a {
    color: #01a3f6;
    text-decoration: none;
}
.main-nav .drop-down ul {
    display: block;
    position: absolute;
    left: 0;
    top: 40px;
    z-index: 99;
    opacity: 0;
    visibility: hidden;
    background: #222f3e;
    box-shadow: 0px 0px 30px rgba(0, 0, 0, 0.25);
    transition: ease all 0.3s;
}
.main-nav .drop-down:hover > ul {
    opacity: 1;
    top: 100%;
    visibility: visible;
}
.main-nav .drop-down li {
    min-width: 180px;
    position: relative;
}
.main-nav .drop-down ul a {
    padding: 10px 20px;
    font-size: 14px;
    font-weight: 500;
    text-transform: none;
    color: #98acc0;
    line-height: 24px;
}
.main-nav .drop-down ul a:hover,
.main-nav .drop-down ul .active > a,
.main-nav .drop-down ul li:hover > a {
    background: #1c2939;
    color: #01a3f6;
    padding-left: 30px;
}
.main-nav .drop-down > a:after {
    content: '\f107';
    font-family: FontAwesome;
    padding-left: 10px;
}
.main-nav .drop-down .drop-down ul {
    top: 0;
    left: calc(100% - 30px);
}
.main-nav .drop-down .drop-down:hover > ul {
    opacity: 1;
    top: 0;
    left: 100%;
}
.main-nav .drop-down .drop-down > a {
    padding-right: 35px;
}
.main-nav .drop-down .drop-down > a:after {
    content: '\f105';
    position: absolute;
    right: 15px;
}
/* ---------------------------------Mobile Navigation---------------------------- */
.mobile-nav {
    position: fixed;
    top: 0;
    bottom: 0;
    z-index: 9999;
    overflow-y: auto;
    left: -260px;
    width: 260px;
    padding-top: 18px;
    background: rgba(28, 41, 57, 0.8);
    transition: 0.4s;
}
.mobile-nav * {
    margin: 0;
    padding: 0;
    list-style: none;
}
.mobile-nav a {
    display: block;
    position: relative;
    color: #fff;
    padding: 10px 20px;
    font-weight: 500;
}
.mobile-nav a:hover,
.mobile-nav .active > a,
.mobile-nav li:hover > a {
    color: #01a3f6;
    text-decoration: none;
}
.mobile-nav .drop-down > a:after {
    content: '\f078';
    font-family: FontAwesome;
    padding-left: 10px;
    position: absolute;
    right: 15px;
}
.mobile-nav .active.drop-down > a:after {
    content: '\f077';
}
.mobile-nav .drop-down > a {
    padding-right: 35px;
}
.mobile-nav .drop-down ul {
    display: none;
    overflow: hidden;
}
.mobile-nav .drop-down li {
    padding-left: 20px;
}
.mobile-nav-toggle {
    position: fixed;
    right: 50px;
    top: 16px;
    z-index: 9998;
    border: 0;
    background: none;
    font-size: 24px;
    transition: all 0.4s;
    outline: none !important;
    line-height: 1;
    cursor: pointer;
    text-align: right;
}
.mobile-nav-toggle i {
    margin: 10px 4px;
    color: #01a3f6;
}
.mobile-nav-overly {
    width: 100%;
    height: 100%;
    z-index: 9997;
    top: 0;
    left: 0;
    position: fixed;
    background: rgba(34, 47, 62, 0.8);
    overflow: hidden;
    display: none;
}
.mobile-nav-active {
    overflow: hidden;
}
.mobile-nav-active .mobile-nav {
    left: 0;
}
.mobile-nav-active .mobile-nav-toggle i {
    color: #fff;
}
/* ---------------------------------Header search---------------------------- */
.nav-search {
    float: right;
}
.nav-search i {
    color: #01a3f6;
    line-height: 18px;
    cursor: pointer;
    font-size: 12px;
    font-weight: 600;
    padding: 16px 0px;
    display: inline-block;
}
.search-box {
    display: none;
    position: absolute;
    right: 0;
    top: 50px;
    width: 440px;
    max-width: 100%;
    padding: 16px 20px;
    background: #222f3e;
    box-shadow: 0px 0px 30px rgba(0, 0, 0, 0.25);
    z-index: 999;
}
.search-box select {
    border: 0;
    border-radius: 8px;
    float: left;
    line-height: 40px;
    width: 16%;
    padding: 7px 4px;
    margin-right: 2%;
    outline: none;
    font-size: 13px;
}
.search-box input {
    border: 0;
    border-radius: 8px;
    outline: none;
    font-size: 12px;
    display: inline-block;
    float: left;
}
.search-box input[type='text'] {
    padding: 4px 10px;
    color: #222;
    line-height: 14px;
    height: 30px;
    width: 62%;
    margin-bottom: 0;
    font-weight: 400;
}
.search-box .btn-default {
    line-height: 30px;
    height: 30px;
    padding: 0 14px;
    border-radius: 8px;
    -webkit-border-radius: 10px;
    -moz-border-radius: 10px;
    font-weight: 400;
    float: right;
    width: 16%;
}

/*---------------------------Banner-----------------------------*/
.flex-container a:hover,
.flex-slider a:hover {
    outline: none;
}
.slides,
.slides > li,
.flex-control-nav,
.flex-direction-nav {
    margin: 0 auto;
    padding: 0;
    list-style: none;
}
.flex-pauseplay span {
    text-transform: capitalize;
}
.flexslider {
    margin: 0;
    padding: 0;
    overflow: hidden;
    display: table;
}
.flexslider .slides > li {
    display: none;
    -webkit-backface-visibility: hidden;
}
.flexslider .slides img {
    width: 100%;
    max-width: 1920px;
    display: block;
}
.flexslider .slides:after {
    content: '\0020';
    display: block;
    clear: both;
    visibility: hidden;
    line-height: 0;
    height: 0;
}
html[xmlns] .flexslider .slides {
    display: block;
}
* html .flexslider .slides {
    height: 1%;
}
.no-js .flexslider .slides > li:first-child {
    display: block;
}
.flexslider {
    margin: 0 auto;
    background: #fff;
    position: relative;
    zoom: 1;
}
.flexslider .slides {
    zoom: 1;
    background: #222;
}
.flexslider .slides img {
    height: auto;
    -moz-user-select: none;
    margin: 0 auto;
}
.flex-viewport {
    max-height: 2000px;
    -webkit-transition: all 1s ease;
    -moz-transition: all 1s ease;
    -ms-transition: all 1s ease;
    -o-transition: all 1s ease;
    transition: all 1s ease;
}
.loading .flex-viewport {
    max-height: 300px;
}
.carousel li {
    margin-right: 5px;
}
.flex-direction-nav {
    *height: 0;
}
.flex-direction-nav a {
    font-size: 0;
    text-decoration: none;
    display: block;
    width: 40px;
    height: 55px;
    margin: -20px 0 0;
    position: absolute;
    top: 50%;
    color: #fff;
    z-index: 10;
    overflow: hidden;
    opacity: 0;
    cursor: pointer;
    -webkit-transition: all 0.3s ease-in-out;
    -moz-transition: all 0.3s ease-in-out;
    -ms-transition: all 0.3s ease-in-out;
    -o-transition: all 0.3s ease-in-out;
    transition: all 0.3s ease-in-out;
}
.flex-direction-nav a {
    font-size: 50px;
}
@media screen and (max-width: 991px) {
    .flex-direction-nav a {
        font-size: 30px;
        width: 30px;
        margin-top: -60px;
    }
}
@media screen and (max-width: 480px) {
    .flex-direction-nav a {
        margin-top: -40px;
    }
}
.flex-direction-nav .flex-prev {
    left: 0px;
}
.flex-direction-nav .flex-next {
    right: 0px;
    text-align: right;
}
.flexslider:hover .flex-direction-nav .flex-prev {
    opacity: 0.7;
    left: 10px;
}
.flexslider:hover .flex-direction-nav .flex-prev:hover {
    opacity: 1;
}
.flexslider:hover .flex-direction-nav .flex-next {
    opacity: 0.7;
    right: 10px;
}
.flexslider:hover .flex-direction-nav .flex-next:hover {
    opacity: 1;
}
.flex-direction-nav .flex-disabled {
    opacity: 0 !important;
    filter: alpha(opacity=0);
    cursor: default;
    z-index: -1;
}
.flex-pauseplay a {
    display: block;
    width: 20px;
    height: 20px;
    position: absolute;
    bottom: 5px;
    left: 10px;
    opacity: 0.8;
    z-index: 10;
    overflow: hidden;
    cursor: pointer;
    color: #000;
}
.flex-pauseplay a:hover {
    opacity: 1;
}
.flex-control-nav {
    width: 100%;
    position: absolute;
    bottom: -40px;
    text-align: center;
    display: none;
}
.flex-control-nav li {
    margin: 0 6px;
    display: inline-block;
    zoom: 1;
    *display: inline;
}
.flex-control-paging li a {
    width: 11px;
    height: 11px;
    display: block;
    background: #666;
    background: rgba(0, 0, 0, 0.5);
    cursor: pointer;
    text-indent: -9999px;
    -webkit-box-shadow: inset 0 0 3px rgba(0, 0, 0, 0.3);
    -moz-box-shadow: inset 0 0 3px rgba(0, 0, 0, 0.3);
    -o-box-shadow: inset 0 0 3px rgba(0, 0, 0, 0.3);
    box-shadow: inset 0 0 3px rgba(0, 0, 0, 0.3);
    -webkit-border-radius: 20px;
    -moz-border-radius: 20px;
    border-radius: 20px;
}
.flex-control-paging li a:hover {
    background: #333;
    background: rgba(0, 0, 0, 0.7);
}
.flex-control-paging li a.flex-active {
    background: #000;
    background: rgba(0, 0, 0, 0.9);
    cursor: default;
}
.flex-control-thumbs {
    margin: 5px 0 0;
    position: static;
    overflow: hidden;
}
.flex-control-thumbs li {
    width: 25%;
    float: left;
    margin: 0;
}
.flex-control-thumbs img {
    width: 100%;
    height: auto;
    display: block;
    opacity: 0.7;
    cursor: pointer;
    -moz-user-select: none;
    -webkit-transition: all 1s ease;
    -moz-transition: all 1s ease;
    -ms-transition: all 1s ease;
    -o-transition: all 1s ease;
    transition: all 1s ease;
}
.flex-control-thumbs img:hover {
    opacity: 1;
}
.flex-control-thumbs .flex-active {
    opacity: 1;
    cursor: default;
}

@media screen and (max-width: 860px) {
    .flex-direction-nav .flex-prev {
        opacity: 1;
        left: 10px;
    }
    .flex-direction-nav .flex-next {
        opacity: 1;
        right: 10px;
    }
}

.flexslider .slide-info {
    vertical-align: middle;
    text-align: center;
    position: absolute;
    left: 25%;
    bottom: 50%;
    margin-bottom: -190px;
    background-color: rgba(1, 163, 246, 0.8);
    border-radius: 50%;
    -webkit-border-radius: 50%;
    -moz-border-radius: 50%;
    width: 380px;
    height: 380px;
}
.flexslider .slide-con {
    padding: 80px 40px 0;
    position: relative;
}
.flexslider .slide-con b {
    display: inline-block;
    padding: 0 20px;
    background-color: #0098b0;
    background-color: rgba(0, 0, 0, 0.1);
    font-style: italic;
    font-weight: normal;
    font-size: 14px;
    line-height: 32px;
    border-radius: 16px;
    -webkit-border-radius: 16px;
    -moz-border-radius: 16px;
    color: #1ee0ff;
}
.flexslider .slide-con h3 {
    font-weight: 800;
    color: #fff;
    margin-top: 20px;
    font-size: 32px;
}
.flexslider .slide-con p {
    font-size: 14px;
    margin-top: 15px;
    line-height: 24px;
    color: #c7f6fd;
}
.flexslider .slide-con a {
    position: absolute;
    right: 80px;
    bottom: -140px;
    background-color: #333;
    color: #fff;
    font-size: 24px;
    border-radius: 50%;
    -webkit-border-radius: 50%;
    -moz-border-radius: 50%;
    padding-top: 15px;
    width: 60px;
    text-align: center;
    height: 60px;
}
@media (min-width: 981px) and (max-width: 1169px) {
    .flexslider .slide-info {
        margin-bottom: -150px;
        left: 20%;
        width: 300px;
        height: 300px;
    }
    .flexslider .slide-con {
        padding: 50px 30px 0;
    }
    .flexslider .slide-con a {
        right: 90px;
        bottom: -75px;
        width: 50px;
        height: 50px;
    }
}
@media (min-width: 768px) and (max-width: 980px) {
    .flexslider .slide-info {
        width: 100%;
        height: auto;
        border-radius: 0;
        -webkit-border-radius: 0;
        -moz-border-radius: 0;
        left: 0;
        bottom: 0;
        margin-bottom: 0;
        text-align: left;
    }
    .flexslider .slide-con {
        padding: 20px 120px 20px 40px;
    }
    .flexslider .slide-con b {
        display: none;
    }
    .flexslider .slide-con h3 {
        font-size: 20px;
        margin: 0;
    }
    .flexslider .slide-con p {
        margin-bottom: 0;
        margin-top: 10px;
    }
    .flexslider .slide-con a {
        bottom: 50%;
        margin-bottom: -25px;
        right: 40px;
        width: 50px;
        height: 50px;
        padding-top: 0;
        line-height: 50px;
    }
}
@media (max-width: 767px) {
    .flexslider .slide-info {
        width: 100%;
        height: auto;
        border-radius: 0;
        -webkit-border-radius: 0;
        -moz-border-radius: 0;
        left: 0;
        bottom: 0;
        margin-bottom: 0;
        text-align: left;
    }
    .flexslider .slide-con {
        padding: 14px 100px 14px 20px;
    }
    .flexslider .slide-con b,
    .flexslider .slide-con p {
        display: none;
    }
    .flexslider .slide-con h3 {
        margin: 0;
        font-size: 16px;
        font-weight: 400;
    }
    .flexslider .slide-con a {
        bottom: 50%;
        width: 40px;
        height: 40px;
        margin-bottom: -20px;
        font-size: 16px;
        line-height: 40px;
        padding-top: 0;
        right: 20px;
    }
}
@media screen and (max-width: 480px) {
    .flexslider .slide-info {
        position: relative;
    }
}
/* ---------------------------------Index sort---------------------------- */
#page-content .services h2 {
    margin-bottom: 40px;
}
.services .services-dtl {
    min-height: 155px;
    width: 20%;
    float: left;
    padding: 30px 2%;
    border-radius: 20px;
    text-align: center;
}
.services .services-dtl span {
    color: #01a3f6;
    font-size: 30px;
    line-height: normal;
}
.services .services-dtl i {
    display: inline-block;
    background-color: #01a3f6;
    color: #fff;
    font-size: 20px;
    padding: 15px 10px;
    text-align: center;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    -webkit-border-radius: 50%;
    -moz-border-radius: 50%;
}
.services .services-dtl h3 {
    font-weight: 400;
    color: #222f3e;
    font-size: 16px;
    margin-bottom: 0;
}
.services .services-dtl p {
    color: #989898;
    line-height: 23px;
    font-size: 13px;
    margin-top: 10px;
}
.services .services-dtl:hover {
    background-color: #f3f6f8;
}
.services .services-dtl:hover h3 {
    color: #01a3f6;
}
@media screen and (max-width: 767px) {
    .services .services-dtl {
        min-height: 80px;
    }
    .services .services-dtl p {
        display: none;
    }
}
@media screen and (max-width: 480px) {
    #page-content .services h2 {
        margin-bottom: 10px;
    }
    .services .services-dtl h3 {
        font-size: 12px;
        margin-top: 16px;
        line-height: 20px;
    }
}
/*-----------------------Index plan tabs-------------------------*/
.product-tab {
    background-color: #f3f6f8;
    position: relative;
    z-index: 10;
    overflow: hidden;
    width: 100%;
}
/*.product-tab h2{margin-top:50px;margin-bottom:30px}*/
.product-tab h4 {
    font-size: 24px;
    color: #222f3e;
    font-weight: 300;
    margin-bottom: 20px;
}
.product-tab h4 span {
    background-color: #01a3f6;
    margin-right: 15px;
    margin-top: -25px;
    text-align: center;
    width: 50px;
    display: inline-block;
    padding: 50px 0 20px;
    border-radius: 0 0 25px 25px;
    -moz-border-radius: 0 0 25px 25px;
    -webkit-border-radius: 0 0 25px 25px;
    color: #fff;
    font-size: 24px;
}
.product-tab h4 a {
    margin-left: 20px;
    font-size: 14px;
    float: right;
    margin-top: 30px;
    font-weight: 400;
}
.resp-arrow {
    border-top-color: #01a3f6;
}
h3.resp-tab-active span.resp-arrow {
    border-bottom-color: #01a3f6;
}
@media (max-width: 992px) {
    .product-tab h4 {
        font-size: 14px;
        font-weight: 400;
        margin-bottom: 20px;
        margin-top: 0;
    }
    .product-tab h4 span {
        margin-top: 0px;
        width: 50px;
        padding: 20px 0 20px;
        border-radius: 0 0 25px 25px;
        -moz-border-radius: 0 0 25px 25px;
        -webkit-border-radius: 0 0 25px 25px;
        color: #fff;
        font-size: 24px;
    }
    .product-tab h4 a {
        margin-top: 20px;
    }
}
@media (max-width: 767px) {
    .product-tab h4 span {
        margin-top: 0;
    }
    .product-tab .prod-tab-content {
        display: inline-block;
        width: 100%;
    }
    .product-tab h2 {
        margin-top: 30px;
        margin-bottom: 20px;
        text-align: center;
    }
}
/*------------------Tabs mobile---------------------*/
.resp-tabs-list {
    padding: 0;
    width: 20%;
    float: left;
}
.resp-tabs-list i {
    margin-right: 15px;
    width: 24px;
    height: 24px;
    text-align: center;
    line-height: 24px;
    font-size: 24px;
}
.resp-tabs-list li {
    cursor: pointer;
    border-bottom: 1px solid #e7edee;
    line-height: 86px;
    padding-left: 30px;
    font-weight: 300;
    font-size: 16px;
    transition: all 0.3s ease;
    -webkit-transition: all 0.3s ease;
    -moz-transition: all 0.3s ease;
    -o-transition: all 0.3s ease;
}
.resp-tabs-list li:hover,
.resp-tabs-list li.resp-tab-active,
h3.resp-accordion:hover {
    background-color: #e7edee;
    border-bottom: 1px solid #f3f6f8;
}
.resp-tabs-list li:last-child {
    border: none;
}
h3.resp-tab-active,
h3.resp-tab-active:hover {
    border-bottom: 1px solid #e7edee;
}
h3.resp-accordion i {
    width: 40px;
    text-align: center;
}
h3.resp-accordion {
    cursor: pointer;
    font-size: 18px;
    display: none;
    font-weight: 300;
    border-bottom: 1px solid #e7edee;
    margin: 0;
    line-height: 86px;
    transition: all 0.3s ease;
    -webkit-transition: all 0.3s ease;
    -moz-transition: all 0.3s ease;
    -o-transition: all 0.3s ease;
}
h3.resp-accordion:hover {
}
.resp-tab-content {
    display: none;
}
.resp-content-active,
.resp-accordion-active {
    display: block;
}
/*-----------Vertical tabs-----------*/
.resp-vtabs .resp-tabs-container {
    min-height: 420px;
    width: 80%;
    float: left;
    padding: 15px;
    background-color: #e7edee;
    position: static;
    margin-top: -113px;
}
.resp-vtabs .resp-tab-content {
    border: none;
    word-wrap: break-word;
}
.resp-arrow {
    width: 0;
    height: 0;
    float: right;
    margin-top: 40px;
    margin-right: 15px;
    border-left: 7px solid transparent;
    border-right: 7px solid transparent;
    border-top: 10px solid;
}
h3.resp-tab-active span.resp-arrow {
    border: none;
    border-left: 7px solid transparent;
    border-right: 7px solid transparent;
    border-bottom: 10px solid;
}
@media (max-width: 992px) {
    h3.resp-accordion {
        line-height: 50px;
        font-size: 16px;
        font-weight: 400;
    }
    .resp-arrow {
        margin-top: 20px;
    }
}
@media (max-width: 479px) {
    .resp-arrow {
        border-left: 5px solid transparent;
        border-right: 5px solid transparent;
        border-top: 8px solid;
    }
    h3.resp-tab-active span.resp-arrow {
        border-left: 5px solid transparent;
        border-right: 5px solid transparent;
        border-bottom: 8px solid;
    }
}
/*-----------Accordion styles-----------*/
h3.resp-tab-active {
    background: #e7edee; /* !important;*/
    border-color: #f5f5f5;
}
.resp-easy-accordion h3.resp-accordion {
    display: block;
}
.resp-jfit {
    width: 100%;
    margin: 0px;
}
.resp-tab-content-active {
    display: block;
    background: #e7edee;
    padding: 0 25px 25px;
}
@media only screen and (max-width: 980px) {
    ul.resp-tabs-list {
        display: none;
    }
    h3.resp-accordion {
        display: block;
    }
    .resp-vtabs .resp-tabs-container {
        border: none;
        float: none;
        width: 100%;
        min-height: 100px;
        clear: none;
        padding: 0;
        background-color: #f3f6f8;
        margin-top: 0;
    }
    .resp-accordion-closed {
        display: none !important;
    }
}
/*-----------------------PlanList----------------------*/
.plan-sec-box {
    background-color: rgba(255, 255, 255, 0.6);
    padding: 20px;
    border-bottom: #eee 1px solid;
    border-radius: 20px;
    transition: all 0.3s ease;
    -webkit-transition: all 0.3s ease;
    -moz-transition: all 0.3s ease;
    -o-transition: all 0.3s ease;
    margin-bottom: 20px;
}
.plan-sec-box:hover {
    background-color: rgba(255, 255, 255, 1);
    transition: all 0.3s ease;
    -webkit-transition: all 0.3s ease;
    -moz-transition: all 0.3s ease;
    -o-transition: all 0.3s ease;
}
.plan-sec-box h3 {
    font-size: 14px;
    margin: 16px 0;
    font-weight: 600;
    display: inline-block;
    white-space: nowrap;
    width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
}
.plan-sec-list p {
    margin-bottom: 8px;
    font-size: 13px;
}
.plan-sec-list p span {
    display: block;
    color: #98acc0;
}
.plan-sec-box .btn-default {
    height: 34px;
    line-height: 34px;
    margin: 20px 0 10px 0;
}
@media (max-width: 479px) {
    .plan-sec-list p span {
        float: left;
        width: 60px;
    }
    .plan-sec-box {
        margin-bottom: 10px;
    }
    .plan-sec-box h3 {
        margin-top: 0;
    }
    .plan-sec-box .btn-default {
        margin: 10px 0 0 0;
    }
}
/*--------------------------Index about---------------------*/
.company-box {
    width: 100%;
    background: url('../images/about-img.jpg') no-repeat center center;
    background-size: 100% auto;
}
.company-box:before {
    content: '';
    display: block;
}
.company-box .company-info {
    display: table;
}
.company-box dl {
    display: table-row;
}
.company-box dd {
    display: table-cell;
    height: 320px;
    vertical-align: middle;
}
.company-box .about-logo {
    width: 20%;
    padding: 0 15px;
}
.company-box .about-logo img {
    border-radius: 20px;
    background: rgba(0, 0, 0, 0.4);
    padding: 16px;
    max-width: 100%;
    max-height: 100%;
}
.company-box .about-con {
    width: 80%;
    padding: 0 40px;
    background: rgba(0, 0, 0, 0.3);
    color: #fff;
    font-size: 14px;
}
.company-box .about-con b {
    display: block;
    margin-bottom: 10px;
    font-size: 18px;
    font-weight: 400;
}
.company-box .about-con p {
    margin-bottom: 40px;
}
@media (max-width: 1200px) {
    .company-box {
        background-size: auto 100%;
    }
    .company-box dd {
        height: 280px;
    }
    .company-box .about-con p {
        margin-bottom: 30px;
    }
}
@media (max-width: 992px) {
    .company-box .about-logo {
        display: none;
    }
    .company-box .about-con {
        width: 100%;
    }
    .company-box dd {
        height: 280px;
    }
    .company-box .about-con {
        padding: 0 15px;
    }
}
@media (max-width: 479px) {
    .company-box .about-con {
        padding: 30px 15px;
    }
    .company-box dd {
        height: 320px;
    }
}
.news-slider .slides {
    width: 100%;
}
.news-slider .slides li a {
    width: 49%;
    color: #fff;
    display: block;
    line-height: 30px;
    float: left;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}
.news-slider .slides li a:nth-child(1) {
    margin-right: 2%;
}
.news-slider .slides li a i {
    margin-right: 8px;
}
.news-slider .flex-direction-nav {
    display: none;
}
@media (max-width: 479px) {
    .news-slider h4 {
        display: none;
    }
    .news-slider .slides {
        width: 100%;
    }
    .news-slider .slides li {
        overflow: hidden;
        height: 60px;
    }
    .news-slider .slides li a {
        width: 100%;
        margin: 0;
    }
    .news-slider .slides li a:nth-child(1) {
        margin-right: 0;
    }
}
/*------------------------breadcrumb-----------------------*/
.breadcrumb {
    background-color: #f3f6f8;
    padding: 14px 0;
    list-style: none;
    border-bottom: #ddd 1px solid;
}
.breadcrumb > li {
    display: inline-block;
}
.breadcrumb > li + li:before {
    padding: 0 5px;
    color: #ccc;
    content: '/\00a0';
}
.breadcrumb ul li {
    color: #535353;
    display: inline-block;
}
.breadcrumb ul li a {
    display: inline-block;
    padding: 0 5px;
}
.breadcrumb ul li:last-child {
    color: #253242;
}
/*-------------------------------footer-----------------------------*/
footer {
    background-color: #2c383d;
    color: #8798a0;
    position: relative;
    bottom: 0;
    height: 168px;
}
footer h2 {
    color: #fff;
    font-weight: 800;
    font-size: 30px;
    margin: 25px 0;
}
footer h2 span {
    font-weight: 300;
    color: #01a3f6;
}
footer a {
    color: #8798a0;
}
footer a:hover {
    color: #01a3f6;
}
.contact-sec {
    padding: 30px 0;
    line-height: 50px;
    color: #fff;
    overflow: hidden;
}
.contact-sec a {
    color: #fff;
    font-weight: 600;
    font-size: 16px;
}
.contact-sec i {
    margin-right: 16px;
    font-size: 20px;
    display: block;
    float: left;
    background-color: rgba(255, 255, 255, 0.17);
    color: #fff;
    text-align: center;
    line-height: 50px;
    width: 60px;
    height: 50px;
    border-radius: 40px;
    -webkit-border-radius: 40px;
    -moz-border-radius: 40px;
}
.contact-sec span {
    opacity: 0.4;
    padding: 0 8px;
}
.contact-sec p {
    padding: 10px 0;
    line-height: 20px;
    margin-bottom: 0;
}
.copyright {
    padding: 14px 0;
    background-color: #253135;
    text-align: center;
}
.copyright span a {
    margin: 0 20px;
}
@media (max-width: 1200px) {
    footer {
        height: 120px;
    }
    .contact-sec {
        padding: 20px 0;
        line-height: 40px;
    }
    .contact-sec a {
        font-size: 14px;
    }
    .contact-sec i {
        line-height: 40px;
        width: 50px;
        height: 40px;
        border-radius: 30px;
        -webkit-border-radius: 30px;
        -moz-border-radius: 30px;
    }
    .contact-sec p {
        line-height: 15px;
    }
    .copyright {
        padding: 10px 0;
        font-size: 12px;
    }
}
@media (max-width: 992px) {
    footer {
        height: 168px;
    }
    .contact-sec {
        line-height: 42px;
        padding: 10px 0;
    }
    .contact-sec p {
        padding: 0;
        line-height: 42px;
    }
    .contact-sec i {
        font-size: 14px;
        line-height: 30px;
        width: 40px;
        height: 30px;
        border-radius: 30px;
        -webkit-border-radius: 30px;
        -moz-border-radius: 30px;
        margin: 6px 10px 6px 0;
    }
}
@media (max-width: 767px) {
    footer {
        height: 160px;
    }
    .copyright a {
        margin: 0 5px !important;
    }
}
@media (max-width: 480px) {
    .contact-sec {
        line-height: 24px;
        font-size: 12px;
        padding: 20px 0;
    }
    .contact-sec p {
        line-height: 24px;
    }
    .contact-sec i {
        font-size: 14px;
        line-height: 16px;
        width: 16px;
        height: 16px;
        background-color: transparent;
        margin: 4px 10px 4px 0;
        opacity: 0.6;
    }
    .contact-sec a {
        color: #fff;
        font-weight: 400;
        font-size: 14px;
    }
    .copyright {
        font-size: 12px;
        text-align: left;
        padding: 6px 12px;
    }
}
/*------------------------Page float right*/
.service-right {
    width: 125px;
    height: 740px;
    position: fixed;
    right: 0;
    bottom: -700px;
    display: block;
    z-index: 999;
}
.service-right h4 {
    background: #01a3f6;
    color: #fff;
    font-size: 16px;
    text-align: center;
    margin: 0;
    padding: 10px 0;
    font-weight: bold;
    border-radius: 16px 16px 0 0;
}
.service-right dl {
    background: #fff;
    padding: 10px;
    margin: 0;
    border-bottom: 1px solid #e5e5e5;
    text-align: center;
}
.service-right dl:last-child {
    border-bottom: 0;
}
.service-right dl dd {
    line-height: 20px;
}
.service-right .go-top {
    font-size: 30px;
    line-height: 30px;
    padding: 0;
    background: #253242;
    color: #fff;
}
.service-right .go-top:hover {
    font-size: 30px;
    background: #01a3f6;
    cursor: pointer;
}
.service-right .go-top {
    transition: all 0.2s ease-in-out;
    -webkit-transition: all 0.2s ease-in-out;
    -moz-transition: all 0.2s ease-in-out;
    -ms-transition: all 0.2s ease-in-out;
    -o-transition: all 0.2s ease-in-out;
}
.go-top-mobile {
    display: none;
    position: fixed;
    right: -150px;
    bottom: 100px;
    width: 50px;
    height: 50px;
    border-radius: 20px;
    text-align: center;
    font-size: 30px;
    line-height: 50px;
    padding: 0;
    background: rgba(0, 0, 0, 0.6);
    color: #fff;
}
@media (max-width: 991px) {
    .service-right {
        right: -200px;
    }
    .go-top-mobile {
        right: 15px;
    }
}

.pagination {
    display: inline-block;
    padding-left: 0;
    margin: 20px 0;
}
.pagination > li {
    display: inline-block;
    margin: 0 6px;
}
.pagination > li > a {
    position: relative;
    float: left;
    padding: 8px 16px;
    line-height: 1.42857143;
    color: #333;
    text-decoration: none;
    background-color: #f3f6f9;
    font-weight: 400;
}
.pagination > li > a:focus,
.pagination > li > a:hover {
    z-index: 2;
    color: #fff;
    background-color: #01a3f6;
}
.pagination > .active > a,
.pagination > .active > a:focus,
.pagination > .active > a:hover {
    z-index: 3;
    color: #fff;
    cursor: default;
    background-color: #01a3f6;
    font-weight: 600;
}
.pagination > .disabled > a,
.pagination > .disabled > a:focus,
.pagination > .disabled > a:hover {
    color: #777;
    cursor: not-allowed;
    background-color: transparent;
}
@media (max-width: 479px) {
    .pagination > li {
        margin: 0 2px;
    }
    .pagination > li > a {
        padding: 4px 8px;
    }
}

.error404 {
    margin: 50px 0;
}
.error404 .page-error-box {
    border-radius: 50%;
    -webkit-border-radius: 50%;
    -moz-border-radius: 50%;
    background-color: #01a3f6;
    width: 100%;
    margin: 0 auto;
    max-width: 300px;
    position: relative;
    color: #fff;
    text-align: center;
    font-size: 120px;
    line-height: 300px;
    font-weight: 600;
}
.error404 .page-error-box span {
    position: absolute;
    right: 5px;
    top: 20px;
    border-radius: 50%;
    -webkit-border-radius: 50%;
    -moz-border-radius: 50%;
    background-color: #222f3e;
    width: 60px;
    height: 60px;
    text-align: center;
    line-height: 60px;
    font-size: 24px;
    color: #fff;
}
.error404 .error-txt {
    margin-top: 60px;
    text-align: center;
}
.error404 .error-txt p {
    font-size: 18px;
    color: #01a3f6;
    margin: 0;
}
.error404 .error-txt h2 {
    line-height: 48px;
    color: #222f3e;
    margin: 25px 0;
}
@media (max-width: 980px) {
    .error404 .page-error-box {
        font-size: 120px;
        line-height: 300px;
    }
    .error404 .page-error-box span {
        right: 0;
        top: 35px;
    }
    .error404 .error-txt h2 {
        font-size: 26px !important;
    }
}
/*--------------------------------Product page tabs---------------------------*/
.more-link {
    text-align: right;
    margin: 20px 0 12px 0;
}
.horizontal .resp-tabs-list i {
    height: 50px;
    width: 50px;
    line-height: 24px;
    font-size: 20px;
    padding: 10px 0 10px;
    border-radius: 0 0 25px 25px;
    -moz-border-radius: 0 0 25px 25px;
    -webkit-border-radius: 0 0 25px 25px;
    color: #fff;
    background-color: #01a3f6;
}
.horizontal .resp-vtabs .resp-tabs-container {
    min-height: 520px;
    width: 80%;
    float: left;
    padding: 15px;
    padding-bottom: 30px;
    background-color: #e7edee;
    position: static;
    margin-top: 0px;
}
@media only screen and (max-width: 980px) {
    .horizontal .resp-vtabs .resp-tabs-container {
        border: none;
        float: none;
        width: 100%;
        min-height: 100px;
        clear: none;
        padding: 0;
        background-color: #f3f6f8;
        margin-top: 0;
    }
}
.img-center {
    background: #fff;
    width: 100%;
    overflow: hidden;
    position: relative;
}
.img-center a {
    text-decoration: none;
}
.prod-tab-content .img-center {
    margin: 20px auto 0 auto;
}
.img-center img {
    position: absolute;
    max-width: 100%;
    max-height: 100%;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
}
.prod-tab-content .img-center img {
    padding: 20px;
}
.prod-tab-content .img-center .layer {
    position: absolute;
    top: 0px;
    right: 0px;
    bottom: 0px;
    left: 0px;
}
.prod-tab-content .img-center:hover .layer {
    box-shadow: inset 0px 0px 20px rgba(0, 0, 0, 0.15);
    -moz-box-shadow: inset 0px 0px 20px rgba(0, 0, 0, 0.15);
    -webkit-box-shadow: inset 0px 0px 20px rgba(0, 0, 0, 0.15);
}
.prod-tab-content .img-center .layer {
    transition: all 0.3s ease-in-out;
    -webkit-transition: all 0.3s ease-in-out;
    -moz-transition: all 0.3s ease-in-out;
    -ms-transition: all 0.3s ease-in-out;
    -o-transition: all 0.3s ease-in-out;
}
/*.horizontal img { max-width:100%;height: auto; margin: 0 auto;}*/
.property-info-list {
    padding: 1.2em 1em;
    border-top: 1px solid #ddd;
    background: #f4f5f7;
}
.property-info-list b {
    font-size: 15px;
    display: inline-block;
    white-space: nowrap;
    width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    margin-bottom: 4px;
}
.property-info-list p {
    font-size: 13px;
    color: #535353;
    margin-bottom: 0;
    line-height: 22px;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
}
@media (max-width: 1200px) {
    .horizontal .img-center {
        height: 212px;
    }
    .horizontal .resp-tabs-list li {
        padding-left: 16px;
    }
    .horizontal .resp-tabs-list i {
        margin-right: 4px;
        width: 40px;
        height: 40px;
    }
}
@media (max-width: 479px) {
    .horizontal .img-center {
        height: 100px;
        width: 26%;
        float: left;
    }
    .horizontal .img-center img {
        padding: 10px;
    }
    .property-info-list {
        width: 74%;
        height: 100px;
        overflow: hidden;
        float: right;
        margin-top: 20px;
        border-top: none;
        border-left: 1px solid #ddd;
        background: #fff;
    }
    .property-info-list b {
        margin-bottom: 0;
    }
    .prod-tab-content .img-center .layer {
        display: none;
    }
    .prod-tab-content .img-center img {
        padding: 6px;
    }
}
@media (min-width: 1200px) {
    .prod-tab-content .img-center {
        height: 255px;
    }
}

/*----------------------------ProductList Page------------------------*/
@media (min-width: 480px) {
    .term {
        display: table;
        margin: 20px 0;
        width: 100%;
    }
    .term dl {
        display: table-row;
        font-size: 13px;
    }
    .term dt {
        width: 100px;
        background: #f8f9fa;
        font-weight: 400;
        text-align: right;
    }
    .term dt i {
        display: none;
    }
    .term dt,
    .term dd {
        display: table-cell;
        padding: 6px 15px;
        border-bottom: #eee 1px solid;
    }
    .term dl:last-child dt.term dl:last-child dd {
        border-bottom: 0;
    }
    .term-reset dd {
        border: none;
    }
}
.term dd a {
    background: #f8f9fa;
    padding: 4px 12px;
    display: inline-block;
    margin: 6px 16px 5px 0;
    border-radius: 10px;
}
.term dd a:hover {
    background: #f0f1f2;
}
.term dd a.active,
.term dd a.active:hover {
    background: #01a3f6;
    color: #fff;
}
.term-reset dt {
    visibility: hidden;
}
.term-reset dd a {
    background: none;
    padding: 4px 0;
}
@media (max-width: 479px) {
    .term {
        width: 100%;
        margin: 10px 0;
        float: left;
        position: relative;
    }
    .term dl {
        float: left;
        margin: 6px 10px 6px 0;
        border: #eee 1px solid;
    }
    .term dt,
    .term-reset dd {
        padding: 6px 26px 6px 12px;
        font-weight: 400;
        position: relative;
    }
    .term dt i {
        float: right;
        text-align: right;
        padding: 6px 8px 0 0;
        font-size: 16px;
        position: absolute;
        width: 100%;
        height: 100%;
        left: 0;
        top: 0;
    }
    .term dt i.fa-angle-down {
        display: block;
    }
    .term dt i.fa-angle-up {
        display: none;
    }
    .term dd {
        display: none;
        position: absolute;
        background: #fff;
        border: #eee 1px solid;
        width: 100%;
        left: -1px;
        top: 36px;
        padding: 12px;
        z-index: 10;
    }
    .term dl.term-reset {
        border: none;
        float: right;
    }
    .term-reset dt {
        display: none;
    }
    .term-reset dd {
        border: none;
        display: block;
        position: static;
        width: auto;
        padding: 0;
    }
    .term-reset dd a {
        padding: 0;
    }
}
.bg-gray {
    background: #f6f7fb;
}
.fontblue {
    color: #0198e8;
}
.fontred {
    color: #e52d27;
}
.fontblue,
.fontred {
    padding: 4px 8px;
    border-radius: 4px;
    background: #eeeff0;
}
.list-box {
    background: #fff;
    margin-bottom: 30px;
    border: 1px solid #d8e0ec;
    font-size: 13px;
    overflow: auto;
}
.list-box h5 {
    margin-bottom: 20px;
}
.list-box h5 b {
    margin: 0 6px;
}
.article {
    display: table;
    background: #fff;
    margin: 0 auto;
    width: 100%;
}
.article p {
    line-height: 24px;
}
.article dl {
    display: table-row;
}
.article dt,
.article dd {
    display: table-cell;
    padding: 12px 20px;
    border-bottom: #f3f3f3 1px solid;
    vertical-align: middle;
}
.article dt {
    padding: 20px;
    font-weight: bold;
}
.article dl:last-child dd {
    border-bottom: 0;
}
.article dl:hover dd {
    background: #e2e6ea;
}
.article dl:nth-child(even) {
    background: #f8f9fa;
}
.article dl dd:nth-child(1) img {
    vertical-align: middle;
    border-radius: 100%;
    border: #ddd 1px solid;
    width: 42px;
    height: 42px;
}
.article dl dd {
    transition: all 0.2s ease-in-out;
    -webkit-transition: all 0.2s ease-in-out;
    -moz-transition: all 0.2s ease-in-out;
    -ms-transition: all 0.2s ease-in-out;
    -o-transition: all 0.2s ease-in-out;
}
.article dl:hover dd {
    transition: all 0.2s ease-in-out;
    -webkit-transition: all 0.2s ease-in-out;
    -moz-transition: all 0.2s ease-in-out;
    -ms-transition: all 0.2s ease-in-out;
    -o-transition: all 0.2s ease-in-out;
}
@media (max-width: 767px) {
    .article dt,
    .article dd {
        padding: 12px;
    }
}
/*----------------------------------ProductDetail Page--------------------------------*/
.pro_main {
    background: #fff;
    padding: 24px;
    overflow: hidden;
    width: 100%;
    margin: 40px auto;
}
.pro_main .btn-bg {
    margin: 16px 0;
}
.pro_main .btn-bg span {
    font-size: 16px;
    margin-right: 10px;
}
/*----------------------------------ProductDetail Img--------------------------------*/
#magnifier {
    position: relative;
}
.small-box {
    position: relative;
    max-width: 400px;
    margin-bottom: 16px;
    text-align: center;
    box-sizing: border-box;
}
.small-box img {
    display: block;
    max-width: 100%;
}
.small-box .hover {
    display: none;
    position: absolute;
    left: 0;
    top: 0;
    width: 200px;
    height: 200px;
    border: 1px solid #aaa;
    background: #0099ff;
    opacity: 0.5;
    filter: alpha(opacity: 50);
    cursor: move;
}

.small-box-list {
    display: flex;
    justify-content: space-evenly;
}
.small-box-li {
    width: 74px;
    height: 74px;
    background: #f8f8f8;
    cursor: pointer;
}
.small-box-li:hover {
    border: 1px solid #1865ad;
}

.thumbnail-box {
    position: relative;
    width: 100%;
    padding: 0 40px;
}
.thumbnail-box .btn-prev,
.thumbnail-box .btn-next {
    position: absolute;
    top: 50%;
    margin-top: -11px;
    width: 22px;
    height: 22px;
    font-size: 22px;
    cursor: pointer;
    background: #fff;
}
.thumbnail-box .btn-prev {
    left: 0;
}
.thumbnail-box .btn-next {
    right: 0;
}
.thumbnail-box .list {
    overflow: hidden;
    width: 100%;
    margin: 0 auto;
}
.thumbnail-box .wrapper {
    width: 100000px;
}
.thumbnail-box .list .item {
    float: left;
    margin: 0 5px;
}
.thumbnail-box .list .item-cur {
}
.thumbnail-box .list .item img {
    border: 2px solid #fff;
    width: 60px;
    height: 60px;
}
.thumbnail-box .list .item-cur img {
    border: 2px solid #027bd6;
}
.big-box {
    display: none;
    overflow: hidden;
    position: absolute;
    left: 402px;
    top: 70px;
    width: 500px;
    height: 500px;
    border: 1px solid #e4e4e4;
    z-index: 99999;
}
.big-box img {
    display: block;
    width: 800px;
    height: 800px;
}
/*----------------------------------ProductDetail Product info--------------------------------*/
.pro_info {
    margin-top: 30px;
}
.pro_info p .btn-default {
    margin-top: 20px;
}
.pro_info p a i {
    color: #01a3f6;
    margin-right: 8px;
}
.other-list b,
.detail-plan-list b {
    display: block;
    color: #333;
    margin: 30px 0 10px 0;
}
.other-list b {
    font-size: 16px;
    color: #1865ad;
}
.other-list a {
    background: #ecf1f6;
    padding: 7px 16px;
    display: inline-block;
    margin: 8px 10px 8px 0;
    border-radius: 4px;
    color: #22303e;
}
.other-list a:hover {
    background: #1865ad;
    color: #fff;
}
.detail-plan-list a {
    display: block;
    margin: 4px 0;
}
.detail-plan-list a i {
    color: #01a3f6;
    font-size: 16px;
    margin-right: 10px;
}

.container-detail {
    background: #f3f3f3;
    padding: 50px 0;
}

.container-box {
    width: 1320px;
    padding: 36px 0 130px;
    margin-right: auto;
    margin-left: auto;
    background: #fff;
}

.table-wrap {
    margin-bottom: 38px;
}

.table-title {
    padding: 10px 0;
    margin: 0;
    background: #1865ad;
    color: #fff;
    font-size: 16px;
    font-weight: 600;
    text-align: center;
}

.table-ul {
    display: flex;
}
.table-li {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: stretch;
}
.table-li-label,
.table-li-value {
    padding: 9px 0;
    text-align: center;
    font-size: 14px;
    border-right: 1px solid #fff;
}

.table-li-label {
    background: #eef1f6;

    border-bottom: 1px solid #fff;
}
.table-li-value {
    background: #e6eaf6;
    color: #22303e;
    border-top: 1px solid #fff;
    font-weight: bold;
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
}

.table-li:last-child .table-li-label,
.table-li:last-child .table-li-value {
    border-right: none;
}

.tab-nav {
    width: 100%;
    padding: 0 24px;
    display: flex;
    box-sizing: border-box;
}
.tab-nav li {
    width: 25%;
    height: 68px;
    line-height: 68px;
    font-size: 22px;
    color: #22303e;
    text-align: center;
    border-bottom: 2px solid #1865ad;
    box-sizing: border-box;
    cursor: pointer;
}

.tab-nav li.active {
    background: url('../images/nav-title.png') no-repeat center;
    background-size: 100% 100%;
    color: #fff;
}

.tab-list {
    padding: 0 24px;
}

.tab-list-li-plan {
    display: flex;
    flex-wrap: wrap;
}

.plan-wrap {
    display: inline-block;
    width: calc(50% - 20px);
    padding: 30px;
    margin-top: 40px;
    margin-right: 20px;
    box-sizing: border-box;
    color: #fff;
    background: #105ba1;
    cursor: pointer;
}
.plan-wrap:nth-child(2n) {
    margin-right: 0;
    margin-left: 20px;
}

.plan-title {
    margin-bottom: 32px;
    font-size: 24px;
    font-weight: 600;
    color: #fff;
}

.plan-content-left {
    padding-right: 26px;
    border-right: 1px solid rgba(255, 255, 255, 0.28);
}

.plan-content-right {
    padding-left: 26px;
}

.plan-wrap:hover {
    color: #fff;
    background: #105ba1;
}

.plan-wrap:focus,
.plan-wrap:active {
    background-color: #105ba1;
    color: #fff;
}

.detail-content {
    margin-top: 50px;
    border: 1px solid #18a3f6;
}
.detail-bottom {
    padding: 0 30px 30px;
}

.detail-bottom-title {
    color: #1865ad;
    font-weight: 600;
    margin-top: -20px;
}

table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 40px;
}

th,
td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #ddd;
}

td:nth-child(3) {
    width: 120px;
}

th {
    background-color: #fff;
    color: #717171;
}

td img {
    width: 32px;
    margin-right: 10px;
    vertical-align: middle;
}

.download-btn {
    background-color: #0e5ba1;
    color: white;
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.3s;
}

.download-btn:hover {
    background-color: #084c8a;
}

@media (max-width: 768px) {
    .container-box {
        width: 100%;
    }

    .tab-nav li {
        height: 34px;
        line-height: 34px;
        font-size: 12px;
    }

    .detail-bottom {
        padding: 0 10px 10px;
    }

    .detail-content {
        margin-top: 15px;
    }

    .detail-bottom-title {
        margin-top: 0px;
        font-size: 14px;
    }

    .plan-wrap {
        display: block;
        width: 100%;
        padding: 10px;
        margin-top: 15px;
        margin-right: 0;
    }

    .plan-title {
        font-size: 14px;
    }

    .plan-content-right {
        padding-left: 0;
    }

    .plan-wrap:nth-child(2n) {
        margin-left: 0;
    }

    table {
        margin-top: 15px;
    }

    th,
    td {
        padding: 5px;
    }

    td:nth-child(2) {
        width: 80px;
    }

    td:nth-child(3) {
        width: 70px;
    }

    .download-btn {
        padding: 4px 16px;
        font-size: 12px;
    }
}
/*----------------------------------ProductDetail parameter--------------------------------*/
.pro-body {
    padding: 60px;
    background: #fff;
    margin-bottom: 40px;
}
.pro-body img {
    max-width: 100%;
}
.pro-body-title {
    height: 50px;
}
.pro-body-title b {
    font-size: 16px;
    font-weight: 400;
    padding: 0 50px;
    line-height: 50px;
    background: #fff;
    display: inline-block;
}
.pro-body-title a {
    margin-left: 40px;
    font-size: 16px;
}
.datalist {
    border: 1px solid #ddd;
    border-collapse: collapse;
    background-color: #ffffff;
    margin-bottom: 10px;
}
.datalist caption {
    padding-bottom: 5px;
    font: bold 1.4em;
    text-align: left;
}
.datalist th {
    border: 1px solid #ddd;
    background-color: #f4f4f4;
    font-weight: bold;
    padding: 6px 12px;
    text-align: center;
}
.datalist td {
    border: 1px solid #ddd;
    text-align: left;
    padding: 8px;
}
.h2 {
    margin: 10px 0;
    padding: 5px 0;
}
.datalist tr:hover,
.datalist tr.altrow {
    background-color: #e5f6fe;
}
.border-1 {
    border: 1px solid #ededed;
}
.detail-font-color {
    color: #1865ad;
}
.detail-title {
    margin-top: 0;
}
@media (max-width: 980px) {
    .pro-body {
        padding: 30px;
        margin-bottom: 20px;
    }
}
@media (max-width: 767px) {
    .pro_main {
        padding: 10px;
        margin: 20px 0;
    }
    .pro_main h1 {
        margin-top: 50px;
        font-size: 18px;
    }
    .pro-body-title {
        height: 40px;
    }
    .pro-body-title b {
        padding: 0 40px;
        line-height: 40px;
        font-size: 14px;
    }
    .pro-body-title a {
        margin-left: 30px;
        font-size: 14px;
    }
    .pro_info {
        margin-top: 20px;
    }
    .pro-body {
        font-size: 12px;
    }
    .pro-body h3 {
        font-size: 16px;
    }
}
/*---------------------------------Plan Page----------------------------------*/
.ourServiceBody h2 {
    margin-top: 50px;
}
.ourServiceBody ul {
    margin-bottom: 10px;
    overflow: hidden;
}
.ourServiceBody .section-container li {
    float: left;
    width: 32%;
    height: 340px;
    padding: 30px 20px;
}
.ourServiceBody .section-container li:nth-child(2),
.ourServiceBody .section-container li:nth-child(5),
.ourServiceBody .section-container li:nth-child(9) {
    margin-left: 2%;
    margin-right: 2%;
}
.ourServiceBody .section-container h3 {
    width: 100%;
    margin-bottom: 10px;
    height: 130px;
    font-size: 20px;
    line-height: 30px;
}
.ourServiceBody .section-container h3 a {
    color: #333;
    width: 100%;
    display: block;
    background: no-repeat top left;
    padding-top: 70px;
}
.ourServiceBody .section-container h3 span {
    color: #777;
    display: block;
    line-height: 16px;
    text-transform: uppercase;
    font-size: 12px;
    font-weight: 400;
    padding-bottom: 8px;
    border-bottom: #e7e7e7 1px solid;
    width: 100%;
}
.ourServiceBody .section-container li:nth-child(1) h3 a {
    background-image: url(../images/index-pd.png);
}
.ourServiceBody .section-container li:nth-child(2) h3 a {
    background-image: url(../images/index-ups.png);
}
.ourServiceBody .section-container li:nth-child(3) h3 a {
    background-image: url(../images/index-air.png);
}
.ourServiceBody .section-container li:nth-child(1) h3 a:hover {
    background-image: url(../images/index-pd-on.png);
}
.ourServiceBody .section-container li:nth-child(2) h3 a:hover {
    background-image: url(../images/index-ups-on.png);
}
.ourServiceBody .section-container li:nth-child(3) h3 a:hover {
    background-image: url(../images/index-air-on.png);
}
.ourServiceBody .section-container p {
    font-size: 13px;
    color: #888;
    line-height: 26px;
    height: 52px;
    margin-bottom: 16px;
}
.ourServiceBody .section-container p a {
    color: #888;
    display: block;
}
.ourServiceBody .section-container p a:hover {
    color: #01a3f6;
}
@media (max-width: 1199px) {
    .ourServiceBody .section-container li {
        height: 380px;
        padding: 20px;
    }
    .ourServiceBody .section-container h3 {
        height: 110px;
    }
    .ourServiceBody .section-container h3 a {
        background-size: 48px;
        padding-top: 50px;
    }
    .ourServiceBody .section-container li.developing h3 a {
        background-size: 24px;
    }
    .ourServiceBody .section-container p {
        height: 78px;
    }
    .ourServiceBody .section-container a.btn-default {
        margin: 10px auto;
    }
}
@media (max-width: 991px) {
    .ourServiceBody .section-container li {
        padding: 20px 10px;
    }
}
@media (max-width: 767px) {
    .ourServiceBody ul {
        margin-bottom: 20px;
    }
    .ourServiceBody .section-container li {
        border-radius: 16px;
        background: #f7f7f7;
        margin: 10px 0;
        height: 202px;
        text-align: center;
    }
    .ourServiceBody .section-container h3 {
        height: 74px;
        font-size: 16px;
    }
    .ourServiceBody .section-container h3 a {
        background-position: top center;
    }
    .ourServiceBody .section-container h3 a span,
    .ourServiceBody .section-container p {
        display: none;
    }
    .ourServiceBody .section-container .btn-default {
        line-height: 30px;
        height: 30px;
        padding: 0 20px;
        border-radius: 20px;
        -webkit-border-radius: 20px;
        -moz-border-radius: 20px;
    }
}
@media (max-width: 479px) {
    .ourServiceBody ul {
        margin-bottom: 0px;
    }
    .ourServiceBody .section-container li {
        padding: 10px 0;
        margin: 6px 0;
        height: 170px;
    }
    .ourServiceBody .section-container h3 {
        height: 64px;
        font-size: 14px;
    }
}
/*-----------------------------------PlanList Page-----------------------------*/
/*.plan-list-body h2{ margin-top: 50px;}*/

/*.plan-list-body .plan-sec-box{border-radius: 0;}*/
.plan-list-body .plan-title {
    border: 1px solid #d8e0ec;
    background: #f8f9fa;
    font-size: 15px;
    line-height: 30px;
    font-weight: 400;
    padding: 10px 20px;
    margin-top: 30px;
    display: inline-block;
    float: left;
    white-space: nowrap;
    width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
}
.plan-list-body .plan-title .btn-default {
    float: right;
}
/*.plan-list-body .plan-title p{ margin-bottom:0;}*/
.plan-list-body .plan-sec-list {
    border: 1px solid #d8e0ec;
    border-top: 0;
    padding: 20px;
    float: left;
    width: 100%;
}
.plan-list-body .plan-sec-list p {
    width: 30%;
    float: left;
    display: inline-block;
}
/*.plan-list-body .plan-sec-list span{ float: left; margin-right:6px;}*/
.plan-list-body .plan-sec-list .btn-default {
    margin-top: 10px;
}
.plan-list-body .plan-sec-list .text-center {
    clear: both;
}
.plan-list-body .plan-sec-list:hover {
}
/*.plan-list-body .article dl dd{ padding: 16px 10px;}*/
/*-----------------------------------PlanCon Page-----------------------------*/
.plan-con-body h1 {
    font-size: 24px;
    margin: 30px 0 10px 0;
}
.plan-bewrite {
    margin: 20px 0;
    color: #777;
}
.plan-con-body .article {
    margin-top: 30px;
}
.plan-con-body .text-center img {
    max-width: 100%;
    margin-top: 0;
}
.plan-con-body .article em {
    display: none;
}
.outline-orange {
    border: #ffbf36 1px solid;
    padding: 10px 20px;
    margin-bottom: 30px;
}
.outline-orange i {
    color: #ffbf36;
    margin-right: 8px;
    font-size: 16px;
}
.down-button {
    margin: 40px 0;
    text-align: center;
}
.down-button a {
    margin: 0 6px;
}
.btn-blue {
    color: #fff;
    padding: 0 1.5rem 0 0;
    background: #0198e8;
}
.btn-blue i {
    background: #0177b5;
    padding: 1rem;
    display: inline-block;
    margin-right: 1.5rem;
}
.btn-blue:hover {
    color: #fff;
    box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.3);
}
@media (max-width: 479px) {
    .down-button {
        margin: 20px 0;
    }
    .down-button .btn-blue {
        padding-right: 10px;
    }
    .down-button .btn i {
        padding: 10px;
        margin-right: 10px;
    }
    .plan-con-body .text-center img {
        padding: 6px;
    }
}
/*-----------------------------------Case Page-----------------------------*/
.caseBody .img-center {
    height: 285px;
}
.caseBody .img-center img {
    padding: 0;
    width: 100%;
    height: 100%;
}
.caseBody .case-title {
    width: 100%;
    font-weight: 400;
    padding: 0 10px;
    height: 50px;
    line-height: 50px;
    background-color: rgba(0, 0, 0, 0.8);
    color: #fff;
    position: absolute;
    bottom: 0;
    left: 0;
    z-index: 5;
    display: inline-block;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}
.caseBody .img-center:hover .case-title {
    background-color: rgba(1, 163, 246, 0.8);
    transition: all 0.3s ease-in-out;
    -webkit-transition: all 0.3s ease-in-out;
    -moz-transition: all 0.3s ease-in-out;
    -ms-transition: all 0.3s ease-in-out;
    -o-transition: all 0.3s ease-in-out;
}
.caseBody .col-ss-12,
.caseBody .col-xs-3,
.caseBody .col-sm-3,
.caseBody .col-md-3 {
    padding: 0;
}
/*-----------------------------------Download Page-----------------------------*/
.download-body h2 {
    float: left;
}
.download-body .article dl dd:nth-child(2) {
    padding-left: 0;
}
.download-body .article dl dd:nth-child(1) img {
    width: 60px;
    height: 60px;
    border-radius: 0;
}
.download-body .article dl dd p {
    margin: 4px 0 0 0;
    font-size: 13px;
}
.download-body .article dt:nth-child(1) {
    width: 60px;
}
.download-body .article dt:nth-child(3),
.download-body .article dt:nth-child(4),
.download-body .article dt:nth-child(5) {
    width: 12%;
}
.download-body .input-group {
    position: relative;
    display: table;
    border-collapse: separate;
    float: right;
    width: 40%;
    margin-top: 50px;
}
.download-body .input-group .form-control {
    position: relative;
    z-index: 2;
    float: left;
    width: 85%;
    margin-bottom: 0;
    display: inline-block;
    padding: 0 15px;
    background-color: #f0f1f5;
    color: #333;
    font-weight: 400;
    font-size: 13px;
}
.download-body .input-group .form-control:focus {
    z-index: 3;
}
.download-body .input-group-btn {
    position: relative;
    font-size: 0;
    white-space: nowrap;
    display: inline-block;
    width: 1%;
    vertical-align: middle;
}
.download-body .btn-search {
    max-height: 40px;
    padding: 5px 22px;
    line-height: normal;
    z-index: 2;
    margin-left: -1px;
    position: relative;
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
}
@media (max-width: 1199px) {
    .download-body .article dt:nth-child(3),
    .download-body .article dt:nth-child(4),
    .download-body .article dt:nth-child(5) {
        width: 14%;
    }
}
@media (max-width: 992px) {
    .download-body .article dt:nth-child(3),
    .download-body .article dt:nth-child(4),
    .download-body .article dt:nth-child(5) {
        width: 16%;
    }
    .download-body .btn-default {
        padding: 0 20px;
    }
    .download-body .input-group {
        width: 50%;
    }
}
@media (max-width: 479px) {
    .download-body h2,
    .download-body .input-group {
        width: 100%;
    }
    .download-body .input-group {
        margin-top: 0;
        margin-bottom: 8px;
    }
    .download-body .article dl:nth-child(1),
    .download-body .article dd:nth-child(1) {
        /* display: none; */
    }
    .download-body .article dl {
        background-color: #fff;
    }
    .download-body .article dl dd:nth-child(2) {
        /* width: 100%;
        display: inline-block; */
        padding-left: 20px;
    }
    .download-body .article dd:nth-child(4),
    .download-body .article dd:nth-child(5) {
        text-align: right;
    }
    .download-body .article dd:nth-child(3),
    .download-body .article dd:nth-child(4),
    .download-body .article dd:nth-child(5) {
        /* width: 50%;
        display: inline-block; */
        line-height: 30px;
        /* padding: 8px 20px; */
        /* background-color: #f6f7fb; */
    }

    .article dt,
    .article dd {
        display: table-cell;
    }

    .download-body .article dt:nth-child(1),
    .download-body .article dt:nth-child(2),
    .download-body .article dt:nth-child(3),
    .download-body .article dt:nth-child(4) {
        width: 25% !important;
    }

    .download-body dd {
        width: 25%;
        padding: 3px 5px;
        display: table-cell;
    }

    .download-body .article dd .btn-default {
        height: 30px;
        line-height: 30px;
    }
}
/*-----------------------------------About Page-----------------------------*/
.about-body {
    margin-bottom: 40px;
}
.about-body h3 {
    margin: 90px 0 40px 0;
    font-weight: 600;
    float: left;
    width: 100%;
}
.about-body img {
    max-width: 100%;
}
.about-body p {
    text-indent: 2em;
}
.text-center img {
    padding: 16px;
    margin-top: 20px;
}
@media (max-width: 767px) {
    .about-body h3 {
        margin: 40px 0 20px 0;
        font-size: 18px;
    }
}
/*-----------------------------------Newslist Page-----------------------------*/
.news-box {
    padding: 20px 0 40px 0;
}
.news-box dl {
    border-bottom: #f6f7fb 1px solid;
    padding: 20px 0;
}
.news-box dl dd {
    padding: 12px;
}
.news-box dd:nth-child(1) {
    width: 80px;
    text-align: right;
    color: #999;
}
.news-box dd i {
    font-size: 24px;
    display: block;
}
@media (max-width: 479px) {
    .news-box dd:nth-child(1) {
        width: 50px;
        padding-right: 0;
    }
}
/*-----------------------------------NewsCon Page-----------------------------*/
.news-con h2 {
    text-align: center;
}
.news_time {
    color: #777;
    text-align: center;
    margin: 10px 0 40px 0;
}
/*-----------------------------------Contact-us Page-----------------------------*/
.contact-body {
    padding-bottom: 40px;
}
.contact-text {
    margin-top: 40px;
}
.contact-text i {
    margin-right: 8px;
    font-size: 16px;
    background-color: #01a3f6;
    color: #fff;
    width: 30px;
    height: 30px;
    text-align: center;
    line-height: 30px;
    border-radius: 100%;
    -webkit-border-radius: 100%;
}
.contact-text p button {
    border: #01a3f6 1px solid;
    padding: 6px 12px;
    margin: 12px 0;
    background: #f6f7fa;
}
.contact-text p button:hover {
    background: #01a3f6;
    color: #fff;
    transition: all 0.3s ease-in-out;
    -webkit-transition: all 0.3s ease-in-out;
    -moz-transition: all 0.3s ease-in-out;
    -ms-transition: all 0.3s ease-in-out;
    -o-transition: all 0.3s ease-in-out;
}
.contact-text #InvoiceInfo {
    display: none;
}
.map-box {
    height: 502px;
}
