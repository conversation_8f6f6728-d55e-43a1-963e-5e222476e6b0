##所有定义的品牌ID 用做查询数据时使用，筛选条件不用


##——————---------------------------------———— mainclass = 1 ups —————-----------------------------—————##
ups.functions=brand,series,capacity,voltagesort,batterytype,batteryform

#品牌
ups.brand=6266
#容量
ups.capacity=6298
#输入输出
ups.voltagesort=6349

#电池分类
ups.batterytype=8945
#电池形式
ups.batteryform=8946



##——————------------------------------------———— mainclass = 2 电池 battery —-----------------------———————————##
battery.functions=brand,series,capacity,voltage,batterytype,hourlyrate

#品牌
battery.brand=6373
#容量
battery.capacity=6357
#电压
battery.voltage=6358
#电压
battery.batterytype=8953
#电压
battery.hourlyrate=8954


##————----------------------------------———— mainclass = 4 空开 air —————----------------------------------———##
air.functions=brand,series,enviroment,method,material,structure,turns,circuit,turns,circuit,voltage,poles,kCircuit,capacity,ics,airSwitchType

#品牌
air.brand=
#漏电电流
air.circuit=1980
#
air.part=2417
#额定电流
air.capacity=1583
#框架电流
air.kCircuit=1980
#空开类型
air.airSwitchType=2449
#级数
air.poles=1981
#品类
air.material=3000
#额定电压
air.voltage=2414
#脱扣类型
air.structure=2417
#操作方式
air.turns=3003
#断开能力
air.ics=2422
#接线方式
air.enviroment=5903
#安装方式
air.method=2312



##—————--------------------------------——————— mainclass = 5 室外机 outdoor —————-------------------------—————##
outdoor.functions=brand,series

#品牌
outdoor.brand=6600

##——————------------------------—————— mainclass = 6 室内机 indoor —————--------------------------------------—————##
indoor.functions=refrigerating,airType,coolingType,brand,series

#品牌
indoor.brand=6600
#制冷量
indoor.refrigerating=6592
#空调类型
indoor.airType=6594
#冷却类型
indoor.coolingType=6595
#送风方式
indoor.airSupply=6591
#制热量
indoor.heating=6593
#用电类型
indoor.electricType=6596

##—————---------------------------------------------————— mainclass = 15 柜体 box ------------------------------------
box.functions=brand,series,method,scale,color,thickness,door,material,structure,enviroment

#品牌
box.brand=
#系列
box.series=15
#使用环境
box.enviroment=23
#安装方式
box.method=22
#规格
box.scale=5
#材料
box.material=24
#结构
box.structure=25
#厚度
box.thickness=26
#颜色
box.color=19
#门的层数
box.door=28


##—————------------------------------------------——— mainclass = 16 仪表 meter ———------------------------————##
meter.functions=brand,series,channels,method,fun,monitor,voltagesort

#品牌
meter.brand=
#监控接口
meter.monitor=59
#电压类型
meter.voltagesort=60
#通道数量
meter.channels=56
#安装方式
meter.method=58
#功能
meter.fun=57

##——————------------------------------------------—— mainclass = 17 按钮和指示灯 light —-----------------------———————##
light.functions=brand,series,scale,color,voltagesort,method,material,channels,turns,structure

#品牌
light.brand=
#开孔
light.scale=82
#颜色
light.color=80
#电压
light.voltagesort=81
#辅助触点
light.method=8395
#类别
light.material=8396
#功能
light.channels=8397
#灯源
light.turns=8398
#材质
light.structure=8409

##—————-----------------------------------------——— mainclass = 18 互感器 inductor —————-----------------------------———##
inductor.functions=brand,series,protection,scale,circuit,turns

#品牌
inductor.brand=5488
#精确等级
inductor.protection=149
#匝数
inductor.turns=96
#孔宽
inductor.scale=153
#最大电流
inductor.circuit=95

##——-------------------------------------------—————— mainclass = 19 浪涌保护 protection ——————-----------------------—##
protection.functions=brand,series,circuit,voltage,method,poles

#品牌
protection.brand=
#标称放电电流
protection.circuit=110
#安装方式
protection.method=112
#额定电压
protection.voltage=113
#级数
protection.poles=111

##————------------------------------------------———— mainclass = 20 铜排 capperbar ———————-------------------------———##
capperbar.functions=brand,series

#品牌
capperbar.brand=

##———------------------------------------------————— mainclass = 21 插座 socket ————-----------------------------——————##
socket.functions=brand,series,method,channels,circuit,voltage

#品牌
socket.brand=
#安装方式
socket.method=172
#额定电流
socket.circuit=174
#额定电压
socket.voltage=175
#孔数
socket.channels=173

##—————----------------------------------------————— mainclass = 22 配电柜配件 distbox.part ——-------------------------—##
distbox.part.functions=accessory,brand,series

#类别
distbox.part.accessory=4000

##——————------------------------------------------———— mainclass = 23 ats —------------------------------------———————##
ats.functions=brand,series,capacity,poles,voltage,protection

#品牌
ats.brand=
#容量
ats.capacity=4018
#级数
ats.poles=4021
#电压
ats.voltage=4028
#级别
ats.protection=4025

##—————————------------------------------------------——— mainclass = 24 气液管 gas ———------------------------———————##
gas.functions=brand,series,length

#品牌
gas.brand=6761
#管径
gas.length=6760

##——————------------------------------------—————— mainclass = 25 水管 pipe —————-----------------———————##
pipe.functions=brand,series,length

#品牌
pipe.brand=
#管径
pipe.length=6776

##——————------------------------------—————— mainclass = 27 制冷剂 refrigeration ———————--------------------------————##
refrigeration.functions=brand,series

#品牌
refrigeration.brand=6783


##———————----------------------------------———— mainclass = 30 摄像机 camera —------------------———————————##
camera.functions=brand,series,method,structure,monitor

#品牌
camera.brand=
#类型
camera.method=4204
#外形
camera.structure=4200
#清晰度
camera.monitor=4197

##——————----------------------------------------———— mainclass = 31 录像机  recorder ——————-----------------———————##
recorder.functions=brand,series,method,channels,capacity

#品牌
recorder.brand=4217
#类型
recorder.method=4214
#接入路数
recorder.channels=4208
#盘位
recorder.capacity=4211

##———————-----------------------------------------——— mainclass = 32 交换机 switch ————-----------------------——————————##
switch.functions=brand,series,method,channels,capacity

#品牌
switch.brand=
#类型
switch.method=4223
#接入数量
switch.channels=4229
#端口速率
switch.capacity=4226

##——————-------------------------------———— mainclass = 33 传输类 transmission ——————---------------------—————————##
transmission.functions=accessory,brand,series

#类别
transmission.accessory=4239

##——————--------------------------------------—————— mainclass = 40 eps —————--------------------------—————##
eps.functions=brand,series,capacity,classify

#品牌
eps.brand=5497
#容量
eps.capacity=5496
#类型
eps.classify=5495

##————------------------------------------———————— mainclass = 50 Sts ———————---------------------------———##
sts.functions=brand,series,capacity,voltage,roles

#品牌
sts.brand=5524
#容量
sts.capacity=5505
#电压
sts.voltage=5506
#级数
sts.roles=5507

##———————---------------------------------------————— mainclass = 51 pdu ——————-------------------------————##
pdu.functions=brand,series,capacity,classify,roles

##品牌
pdu.brand=5543
#容量
pdu.capacity=5509
#类型
pdu.classify=5510
#孔数
pdu.roles=5511

##———————-------------------------------------————— mainclass = 52 稳压电源  rps ——————---------------------————##
rps.functions=brand,series,capacity,principle,precision,voltage

#品牌
rps.brand=5549
#容量
rps.capacity=5513
#原理
rps.principle=5514
#精度
rps.precision=5515
#电压
rps.voltage=5516

##————————-----------------------------------———— mainclass = 53 逆变器 inverter ————-----------------------——————##
inverter.functions=brand,series,capacity,voltage,input

#品牌
inverter.brand=5551
#容量
inverter.capacity=5520
#输出电压
inverter.voltage=5521
#输入电压
inverter.input=5522

##————-------------------------------------—————— mainclass = 68 电池柜 batterybox ——---------------------————————##
batterybox.functions=brand,series

#品牌
batterybox.brand=9015

##——————------------------------------------—————— mainclass = 69 电池线 batteryline  ——-------------------——————##
batteryline.functions=brand,series,lineDiameter

#品牌
batteryline.brand=
#规格
batteryline.lineDiameter=6558

##——————-----------------------------------—————— mainclass = 70 监控中心 csc———------------------------———————##
csc.functions=brand,title,area

#品牌
csc.brand=
#容量
csc.capacity=5531
#特色
csc.other=5532
#面积
csc.area=5533

##————————------------------------------------------———— mainclass = 71 配电系统 distboxs ———--------------———————##
distboxs.functions=brand,title,area

#品牌
distboxs.brand=5534
#容量
distboxs.capacity=5535
#特色
distboxs.other=5536
#面积
distboxs.area=5537

##—————————---------------------------------------------——— mainclass = 72 ups系统 upss ————----------------——————##
upss.functions=brand,title,area

#品牌
upss.brand=5538
#容量
upss.capacity=5539
#特色
upss.other=5540
#面积
upss.area=5541

##————————---------------------------------------------———— mainclass = 73 空调系统 airs ———------------------———————##
airs.functions=brand,title,area
#品牌
airs.brand=5542
#容量
airs.capacity=5543
#特色
airs.other=5544
#面积
airs.area=5545


##————————-----------------------------------------———— mainclass = 74 漏水系统 lw —————------------------------—————##
lw.functions=brand,title,area

#品牌
lw.brand=5546
#容量
lw.capacity=5547
#特色
lw.other=5548
#面积
lw.area=5549

##—————————---------------------------------------------——— mainclass = 75 湿度监测 wsd ————------------------——————##
wsd.functions=brand,title,area

#品牌
wsd.brand=5550
#容量
wsd.capacity=5551
#特色
wsd.other=5552
#面积
wsd.area=5553

##————————----------------------------------------———— mainclass = 76 视频监控 video —----------------------—————————##
video.functions=brand,title,area

#品牌
video.brand=5554
#容量
video.capacity=5555
#特色
video.other=5556
#面积
video.area=5557

##———————---------------------------------------------————— mainclass = 77 门禁系统 asc ————-----------------------——————##
asc.functions=brand,title,area

#品牌
acs.brand=5558
#容量
acs.capacity=5559
#特色
acs.other=5560
#面积
acs.area=5561

##———————-------------------------------------------————— mainclass = 78 消防系统 ff ——----------------------————————##
ff.functions=brand,title,area

#品牌
ff.brand=5562
#容量
ff.capacity=5563
#特色
ff.other=5564
#面积
ff.area=5565


##—————------------------------------------------————— mainclass = 80 隔离开关 qs ————----------------------------————##
qs.functions=brand,series,enviroment,method,material,turns,circuit,voltage,poles,capacity

#品牌
qs.brand=
#接线方式
qs.enviroment=6794
#安装方式
qs.method=6795
#品类
qs.material=6796
#操作方式
qs.turns=6797
#熔断器规格
qs.circuit=6798
#额定电压
qs.voltage=6799
#极数
qs.poles=6800
#额定电流
qs.capacity=6801

##——————-----------------------------------------------———— mainclass = 81 负荷开关 ql ———---------------------—————##
ql.functions=brand,series,enviroment,method,material,turns,voltage,poles,capacity

#品牌
ql.brand=
#接线方式
ql.enviroment=6804
#安装方式
ql.method=6805
#品类
ql.material=6806
#手柄类型
ql.turns=6807
#额定电压
ql.voltage=6808
#极数
ql.poles=6809
#额定电流
ql.capacity=6810

##—————-------------------------------------------------————— mainclass = 82 接触器 km ——---------------------——————##
km.functions=brand,series,enviroment,method,voltagesort,voltage,capacity,airSwitchType,material,poles

#品牌
km.brand=
#主触点
km.enviroment=6813
#辅助触点
km.method=6814
#控制线圈电压
km.voltagesort=6815
#额定电压
km.voltage=6817
#额定电流
km.capacity=6818
#控制线圈类型
km.airSwitchType=6816
#品类
km.material=7215
#极数
km.poles=7214

##———————------------------------------------------------——— mainclass = 83 熔断器 fu -----------------------————————##
fu.functions=brand,series,method,scale,material,voltage,poles,capacity,ics

#品牌
fu.brand=
#使用类别
fu.method=6821
#熔断器尺寸
fu.scale=6822
#品类
fu.material=6823
#额定电压
fu.voltage=6824
#极数
fu.poles=6825
#额定电流
fu.capacity=6826
#分段能力
fu.ics=6827

##———————---------------------------------------------------------——— mainclass = 84 继电器 ka —----------------———————##
ka.functions=brand,series,material,structure,kCircuit,capacity

#品牌
ka.brand=
#品类
ka.material=6830
#类型
ka.structure=6831
#壳架电流
ka.kCircuit=6832
#整定电流
ka.capacity=6833

##————————-------------------------------------—— mainclass = 85 断路器附件 ac ——————--------------------------——##
ac.functions=brand,series,material,structure,part

#品牌
ac.brand=
#品类
ac.material=6836
#附件类型
ac.structure=6837
#产品类型
ac.part=6838

##————------------------------------—————— mainclass = 200 ups配件 ups.part------------------------------- ——————##
ups.part.functions=accessory,brand,series

#类别
ups.part.accessory=4042

##—————————-------------------------------—— mainclass = 201 空调配件 air.part —------------------------------—————————##
air.part.functions=accessory,brand,series

#类别
air.part.accessory=6787

##——————------------------------------———— mainclass = 202 安防配件 monitor.part  —————-----------------------------———————##
monitor.part.functions=accessory,brand,series

#类别
monitor.part.accessory=4227

