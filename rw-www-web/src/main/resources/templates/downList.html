<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org" xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="layout/master">

<body>

<div id="page-content" class="horizontal"  layout:fragment="content">
    <section class="breadcrumb">
        <div class="container">
            <ul>
                <li><a th:href="${@urlUtil.getJumpUrl('/')}">[[ ${homepage} ]]</a> ></li>
                <li>[[${download_center}]]</li>
            </ul>
        </div>
    </section>
    <section class="download-body">
        <div class="container">
            <h2>[[${download_center}]]</h2>
            <div class="input-group">
                <form action="/down/down" method="get">
                    <input type="text" name="title" th:value="${title}" class="form-control" placeholder="输入资料名称">
                    <span class="input-group-btn">
                        <button type="submit" class="btn btn-default btn-search">
                            <i class="fa fa-search"></i>
                        </button>
                    </span>
                </form>
            </div>
            <div class="clear"></div>
            <!--            <h5>共找到<b th:text="${data.total}">0</b>个下载资料</h5>-->
            <section class="list-box">
                <div class="article">
                    <dl>
                        <dt style="width: 40%;">[[${download_table_file_name}]]</dt>
                        <dt>[[${download_table_update_time}]]</dt>
                        <dt>[[${download_table_file_size}]]</dt>
                        <dt>[[${download_table_operation}]]</dt>
                    </dl>

                    <dl th:each="item:${list}">
                        <dd>
                            <a th:attr="data-href=${item.attachment}" href="javascript:;" class="down-data"><th:block th:text="${item.title}"/></a>
                        </dd>
                        <dd th:text="${#dates.format(item.updateTime, 'yyyy-MM-dd HH:mm:ss')}"></dd>
                        <dd th:text="${item.attachmentSize}"></dd>
                        <dd><a th:attr="data-href=${item.attachment}" href="javascript:;" class="btn-default down-data">[[ ${download_btn} ]]</a></dd>
                    </dl>

                </div>
            </section>
            <div class="text-center">
                <ul class="pagination ins-page" th:utext="${pages}">

                </ul>
            </div>
        </div>
    </section>







    <script th:src="${staticSite}+'js/down-data.js?v='+${staticVersion}"></script>




</div>


</body>
</html>