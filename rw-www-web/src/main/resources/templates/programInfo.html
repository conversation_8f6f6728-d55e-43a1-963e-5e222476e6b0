<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org" xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="layout/master">


<div id="page-content" class="horizontal"  layout:fragment="content">
    <section class="breadcrumb">
        <div class="container">
            <ul>
                <li><a th:href="${@urlUtil.getJumpUrl('/')}">[[${homepage}]]</a> ></li>
                <li><a th:href="${@urlUtil.getJumpUrl('/program/program')}">[[ ${boxlist_system_solution} ]]</a> ></li>
                <li th:text="${programme.title}"></li>
            </ul>
        </div>
    </section>
    <section class="plan-con-body">
        <div class="container">
            <h1 th:text="${programme.title}"></h1>
            <div class="plan-bewrite" th:text="${boxlist_briefly_describe}+'：'+${programme.title}"></div>
            <span th:if="${programme.imgPath != null}">
                <div th:each="img:${#strings.arraySplit(programme.imgPath,',')}" class="text-center">
                    <div class="col-xs-6 col-sm-3">
                        <img th:src="${img}" alt=""/>
<!--                        <span th:text="${#strings.substring(img, #strings.indexOf(img, '-')+1, #strings.indexOf(img, '.'))}"></span>-->
                    </div>
                </div>
            </span>
            <div class="clear"></div>
            <div th:if="${programme.detailList != null}" class="article">
                <dl>
                    <dt>[[${boxlist_product_category}]]</dt>
                    <dt>[[${boxlist_product_name}]]</dt>
                    <dt>[[${boxlist_quantity}]]</dt>
                </dl>
                <dl th:each="item:${programme.detailList}">
                    <dd th:text="${item.type}"></dd>
                    <dd><a href="productDetail.html" target="_blank"><span th:text="${item.productTitle}"></span></a></dd>
                    <dd><em>X</em><span th:text="${item.num}"></span></dd>
                </dl>
            </div>
            <!--            <div class="down-button">-->
            <!--                <a href="#"><button type="button" class="btn btn-blue"><i class="fa fa-table"></i>下载表格</button></a>-->
            <!--                <a href="#"><button type="button" class="btn btn-blue"><i class="fa fa-file-pdf-o"></i>下载PDF</button></a>-->
            <!--                <a href="#"><button type="button" class="btn btn-blue"><i class="fa fa-picture-o"></i>下载图片</button></a>-->
            <!--            </div>-->
            <div class="outline-orange">
                <i class="fa fa-info-circle"></i>[[${boxlist_description}]]
            </div>
        </div>
    </section>
</div>

</html>