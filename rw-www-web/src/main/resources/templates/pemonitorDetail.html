<!DOCTYPE html>
<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org" xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="layout/master">

<div id="page-content" class="horizontal"  layout:fragment="content">
    <section class="breadcrumb">
        <div class="container">
            <ul>
                <li><a th:href="${@urlUtil.getJumpUrl('/')}">[[ ${homepage} ]]</a> ></li>
                <li>
                    <a th:href="${@urlUtil.getJumpUrl('/product/product')}"
                    >[[ ${product_title} ]]</a
                    >
                    >
                </li>
                <li th:if="${mainclass == 1}">[[ ${product_usp_power_supply} ]]</li>
                <li th:if="${mainclass == 2}">[[ ${product_battery} ]]</li>
                <li th:if="${mainclass == 5}">[[ ${product_outdoor_unit} ]]</li>
                <li th:if="${mainclass == 6}">[[ ${product_indoor_unit} ]]</li>
                <li th:if="${mainclass == 40}">[[ ${product_eps_power_supply} ]]</li>
                <li th:if="${mainclass == 50}">[[ ${product_sts} ]]</li>
                <li th:if="${mainclass == 51}">[[ ${product_pdu} ]]</li>
                <li th:if="${mainclass == 52}">[[ ${product_voltage_stabilizing_power_supply} ]]</li>
                <li th:if="${mainclass == 53}">[[ ${product_inverter} ]]</li>
                <li th:if="${mainclass == 68}">[[ ${product_battery_cabinet} ]]</li>
                <li th:if="${mainclass == 69}">[[ ${product_battery_wire} ]]</li>
            </ul>
        </div>
    </section>
    <div class="container">
        <div class="pro_main">
            <!--begin品牌logo厂家介绍-->
            <div id="magnifier" class="col-sm-5 col-xs-12">
                <div class="small-box">
                    <img th:src="${product.img}" onerror="javascript:this.src='/images/img-err.png'"/>
                </div>
            </div>

            <div class="col-sm-7 col-xs-12">
                <h1 th:text="${product.title}"></h1>

                <span th:if="${mainclass == 1}">
                    <span th:if="${product.capacityTitle !=null and product.capacityTitle != ''}">
                        <p class="btn-bg">
                            <span class="btn-bg fontred" th:text="${capacity}+'：'+${product.capacityTitle}"></span>
                        </p>
                    </span>
                    <span th:if="${product.voltageSortTitle != null and product.voltageSortTitle != ''}">
                        <p class="btn-bg">
                            <span class="btn-bg fontblue" th:text="${voltagesort}+'：'+${product.voltageSortTitle}"></span>
                        </p>
                    </span>
                </span>

                <span th:if="${mainclass == 2}">
                    <span th:if="${product.capacityTitle !=null and product.capacityTitle != ''}">
                        <p class="btn-bg">
                            <span class="btn-bg fontred" th:text="${capacity}+'：'+${product.capacityTitle}"></span>
                        </p>
                    </span>
                    <span th:if="${product.voltageTitle != null and product.voltageTitle != ''}">
                        <p class="btn-bg">
                            <span class="btn-bg fontblue" th:text="${voltage}+'：'+${product.voltageTitle}"></span>
                        </p>
                    </span>
                </span>


                <div class="pro_info">
                    <b th:if="not ${#lists.isEmpty(attachmentList)}">[[${document_download}]] </b>
                    <span th:each="item:${attachmentList}">
                        <p>
                            <a th:attr="data-href=${item.attachment}" href="javascriot:;" class="down-data">
                                <i class="fa fa-download"></i>
                                <span th:text="${item.title}"></span>
                            </a>
                        </p>
                    </span>
                    <div class="other-list">
                        <b>[[ ${product_series_other_model} ]]</b>
                        <span th:each="item:${theSameSeries.list}">
                            <a th:href="${@urlUtil.getJumpUrl('/product/productInfo/m_'+item.mainClass+'/'+item.id+'.html')}"
                               target="_blank"
                               th:text="${item.title}"></a>
                        </span>
                    </div>
                    <div class="detail-plan-list">
                        <b>[[ ${product_application_examples} ]]</b>
                        <span th:each="item:${schemeList}">
                            <a th:href="@{'/programmeInfo?classify='+${item.classify}+'&listno='+${item.listNo}}"
                               target="_blank">
                                <i class="fa fa-caret-right"></i>
                                <span th:text="${item.title}"></span>
                            </a>
                        </span>
                    </div>
                </div>
            </div>
        </div>
        <div class="pro-body-title" th:if="not ${#lists.isEmpty(product.imgs)}"><b>综述</b></div>
        <div class="pro-body">
            <p th:each="item:${#strings.arraySplit(product.imgs,',')}">
                <img th:src="${item}"/>
            </p>
        </div>
    </div>
</div>

</html>