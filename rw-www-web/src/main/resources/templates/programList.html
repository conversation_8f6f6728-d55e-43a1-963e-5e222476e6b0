<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org" xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="layout/master">
    <div id="page-content" class="horizontal"  layout:fragment="content">
        <input type="hidden" id="lang" th:value="${lang}">
        <input type="hidden" id="classify" th:value="${classify}"/>
        <section class="breadcrumb">
        <div class="container">
            <ul>
                <li><a th:href="${@urlUtil.getJumpUrl('/')}">[[ ${homepage} ]]</a> ></li>
                <li><a th:href="${@urlUtil.getJumpUrl('/program/program')}">[[ ${boxlist_system_solution} ]]</a> ></li>
                <li th:if="${classify == 1}" >[[${boxlist_power_distribution_scheme}]]</li>
                <li th:if="${classify == 2}">[[${boxlist_ups_scheme}]]</li>
                <li th:if="${classify == 3}">[[${boxlist_air_conditioning_scheme}]]</li>
            </ul>
        </div>
    </section>
    <section class="plan-list-body">
        <div class="container">
            <h2 th:if="${classify == 1}" >[[${boxlist_power_distribution_scheme}]]</h2>
            <h2 th:if="${classify == 2}">[[${boxlist_ups_scheme}]]</h2>
            <h2 th:if="${classify == 3}">[[${boxlist_air_conditioning_scheme}]]</h2>

            <div class="term">
                <dl th:each="item,itemStat:${programParameters}">
                    <dt th:data-type="${item.labelName}">[[ ${item.title} ]]<i class="fa fa-angle-up"></i><i class="fa fa-angle-down"></i></dt>
                    <dd>
                        <a data-id="" href="javascript:;" class="active classify">[[ ${all} ]]</a>
                        <span th:each="attr:${item.labelList}">
                            <a th:attr="data-id=${attr.id}" class="classify" href="javascript:;"><span th:text="${attr.name}"></span></a>
                        </span>
                    </dd>
                </dl>
                <dl class="term-reset">
                    <dt></dt>
                    <dd><a class="clear-option" href="javascript:;">[[ ${reset_filter} ]]</a></dd>
                </dl>
            </div>

            <div class="clear"></div>
            <span th:text="${#strings.replace(boxlist_count_tip, '{0}', totalCount)}"></span>
            <section class="list-box" th:if="${!#lists.isEmpty(programme) && #lists.size(programme) > 0}">
                <div class="article">
                    <dl>
                        <dt>[[${boxlist_scheme_name}]]</dt>
                        <dt>[[${boxlist_category}]]</dt>
                    </dl>

                    <dl th:each="program:${programme}">
<!--                        <dd><a th:href="@{'/program/programInfo?classify='+${classify}+'&listno='+${program.listNo}}" target="_blank"><span th:text="${program.title}"></span> </a></dd>-->
                        <dd><a th:href="${@urlUtil.getJumpUrl('/program/programInfo/m_'+classify+'/'+program.listNo+'.html')}" target="_blank"><span th:text="${program.title}"></span> </a></dd>
                        <dd><span th:text="${program.labelIdName}"></span></dd>
                        <dd><a th:href="${@urlUtil.getJumpUrl('/program/programInfo/m_'+classify+'/'+program.listNo+'.html')}" target="_blank">[[${boxlist_view_the_checklist}]]</a></dd>
                    </dl>
                </div>
            </section>
            <div class="text-center">
                <ul class="pagination ins-page" th:utext="${pages}">
                </ul>
            </div>
        </div>
    </section>




        <script th:src="${staticSite}+'js/reception/searchProgramme.js?v='+${staticVersion}"></script>
</div>


</html>