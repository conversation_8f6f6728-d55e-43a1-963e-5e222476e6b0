<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org" xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout">
    <head>
        <title th:text="${title}"></title>
        <meta name="keywords" th:content="${keyword}" />
        <meta name="description" th:content="${descriptions}" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no, minimum-scale=1.0, maximum-scale=1.0" />
        <link rel="shortcut icon" href="/favicon.ico" type="image/x-icon" />
        <meta charset="utf-8" />
        <!--[if lt IE 8]><!-->
        <link rel="stylesheet" th:href="${staticSite}+'css/ie7/ie7.css'" />
        <!--<![endif]-->
        <link th:href="${staticSite}+'css/style.css?v='+${staticVersion}" rel="stylesheet" type="text/css" />
        <script th:src="${staticSite}+'js/jquery.min.js'"></script>
        <script th:src="${staticSite}+'js/productDetail.js'"></script>
        <!--    <script th:src="${staticSite}+'js/down-data.js'"></script>-->
        <style layout:fragment="style"></style>
    </head>
    <body>
        <header class="navbar-custom navbar-fixed-top" role="navigation">
            <div class="container pos-relative">
                <div class="logo">
                    <a href="/"><img th:src="${staticSite}+'images/logo.png'" alt="" /></a>
                    <p>[[ ${workday} ]]<b>400-6166-290</b></p>
                </div>
                <div class="nav-search">
                    <i class="" th:utext="${language}"></i>
                </div>
                <nav class="main-nav float-right d-none d-lg-block">
                    <ul th:utext="${navStr}">
                        <!--  <li th:each="one,oneStat:${navigationList}" th:class="${!#lists.isEmpty(one.childList) && #lists.size(one.childList) > 0} ?'drop-down':''">
                    <a th:href="${one.isLink} == 1 ? ${one.linkUrl} :'javascript:void(0)'">[[${one.title}]]</a>
                    <ul th:if="${!#lists.isEmpty(one.childList) && #lists.size(one.childList) > 0}">
                        <li th:each="two,twoStat:${one.childList}" th:class="${!#lists.isEmpty(two.childList) && #lists.size(two.childList) > 0} ?'drop-down':''">
                            <a th:href="${two.isLink} == 1 ? ${two.linkUrl} :'javascript:void(0)'">[[${ two.title }]]</a>
                            <ul th:if="${!#lists.isEmpty(two.childList) && #lists.size(two.childList) > 0}">
                                <li  th:each="three,threeStat:${two.childList}">
                                    <a th:href="${three.isLink} == 1 ? ${three.linkUrl} :'javascript:void(0)'">[[${ three.title }]]</a>
                                </li>
                            </ul>
                        </li>
                    </ul>
                </li>-->
                    </ul>
                </nav>
            </div>
        </header>

        <div id="page-content" class="horizontal" layout:fragment="content"></div>

        <footer>
            <div class="container">
                <div class="row contact-sec">
                    <div class="col-md-4 col-xs-12">
                        <i class="fa fa-map-marker"></i>
                        <p>[[ ${address} ]]</p>
                    </div>
                    <div class="col-md-4 col-xs-12">
                        <i class="fa fa-volume-control-phone"> </i>
                        <a href="tel:400-6166-290">400-6166-290</a>
                    </div>
                    <div class="col-md-4 col-xs-12"><i class="fa fa-envelope-o"></i><a href="Mailto:<EMAIL>"><EMAIL></a></div>
                </div>
            </div>
            <div class="copyright">
                <div class="container">
                    <div class="row">
                        Copyright@ 2016-2024 All rights reserved<span> <a href="https://beian.miit.gov.cn" target="_blank" rel="nofollow">粤ICP备20055603号</a></span>
                    </div>
                </div>
            </div>
        </footer>
    </body>
</html>
