<!DOCTYPE html>
<!DOCTYPE html>
<html
  lang="en"
  xmlns:th="http://www.thymeleaf.org"
  xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
  layout:decorate="layout/master"
>
  <div id="page-content" class="horizontal" layout:fragment="content">
    <section class="breadcrumb">
      <div class="container">
        <ul>
            <li><a th:href="${@urlUtil.getJumpUrl('/')}">[[ ${homepage} ]]</a> ></li>
            <li>
                <a th:href="${@urlUtil.getJumpUrl('/product/product')}"
                >[[ ${product_title} ]]</a
                >
                >
            </li>
          <li th:if="${mainclass == 1}">[[ ${product_usp_power_supply} ]]</li>
          <li th:if="${mainclass == 2}">[[ ${product_battery} ]]</li>
          <li th:if="${mainclass == 5}">[[ ${product_outdoor_unit} ]]</li>
          <li th:if="${mainclass == 6}">[[ ${product_indoor_unit} ]]</li>
          <li th:if="${mainclass == 40}">[[ ${product_eps_power_supply} ]]</li>
          <li th:if="${mainclass == 50}">[[ ${product_sts} ]]</li>
          <li th:if="${mainclass == 51}">[[ ${product_pdu} ]]</li>
          <li th:if="${mainclass == 52}">
            [[ ${product_voltage_stabilizing_power_supply} ]]
          </li>
          <li th:if="${mainclass == 53}">[[ ${product_inverter} ]]</li>
          <li th:if="${mainclass == 68}">[[ ${product_battery_cabinet} ]]</li>
          <li th:if="${mainclass == 69}">[[ ${product_battery_wire} ]]</li>
        </ul>
      </div>
    </section>
    <div class="container">
      <div class="pro_main">
        <!--begin品牌logo厂家介绍-->
        <div id="magnifier" class="col-sm-4 col-xs-12">
          <div class="border-1 small-box">
            <img th:if="${product.img != null && product.img!=''}"
              th:src="${#strings.arraySplit(product.img,',')[0]}"
              onerror="javascript:this.src='/images/img-err.png'"
            />
            <img  th:if="${product.img == null || product.img==''}"
              src=""
              onerror="javascript:this.src='/images/img-err.png'"
            />
          </div>
          <ul class="small-box-list">
            <li class="small-box-li" th:if="${product.img != null && product.img!=''}" th:each="img:${#strings.arraySplit(product.img,',')}">
              <img
                width="100%"
                height="100%"
                th:src="${img}"
                onerror="javascript:this.src='/images/img-err.png'"
              />
            </li>
          </ul>
        </div>

        <div class="col-sm-8 col-xs-12">
          <h1
            class="detail-font-color detail-title"
            th:text="${product.title}"
          ></h1>

          <div class="pro_info">
            <!-- 电器参数 -->
            <div class="table-wrap">
              <!--电气参数-->
              <p class="table-title">[[ ${electrical_parameters} ]]</p>
              <ul class="table-ul">

                  <li class="table-li" th:if="${product.capacityTitle!=null && product.capacityTitle!=''}">
<!--                      产品容量-->
                      <div class="table-li-label">[[ ${product_detail_product_capacity} ]]</div>
                      <div class="table-li-value">[[ ${product.capacityTitle} ]]</div>
                  </li>

                  <li class="table-li" th:if="${product.classifyTitle!=null && product.classifyTitle!=''}">
<!--                      类型-->
                      <div class="table-li-label">[[ ${product_detail_type} ]]</div>
                      <div class="table-li-value">[[ ${product.classifyTitle} ]]</div>
                  </li>

                  <li class="table-li" th:if="${product.voltageSortTitle!=null && product.voltageSortTitle!=''}">
<!--                                      输入输出-->
                    <div class="table-li-label">[[ ${product_detail_input_and_output} ]]</div>
                    <div class="table-li-value">[[ ${product.voltageSortTitle} ]]</div>
                  </li>

<!--                <li class="table-li">-->
<!--                  <div class="table-li-label">产品功率</div>-->
<!--                  <div class="table-li-value">10KW (千瓦)</div>-->
<!--                </li>-->
<!--                <li class="table-li">-->
<!--                  <div class="table-li-label">输入输出</div>-->
<!--                  <div class="table-li-value">三进单出</div>-->
<!--                </li>-->
<!--                <li class="table-li">-->
<!--                  <div class="table-li-label">原理分类</div>-->
<!--                  <div class="table-li-value">高频机</div>-->
<!--                </li>-->
<!--                <li class="table-li">-->
<!--                  <div class="table-li-label">电池形式</div>-->
<!--                  <div class="table-li-value">外接电池</div>-->
<!--                </li>-->
<!--                <li class="table-li">-->
<!--                  <div class="table-li-label">直流电压</div>-->
<!--                  <div class="table-li-value">192VDC</div>-->
<!--                </li>-->
              </ul>
            </div>

            <!-- 物理参数 -->
            <div class="table-wrap">
              <!--物理参数-->
              <p class="table-title">[[ ${physical_parameters} ]]</p>
              <ul class="table-ul">
                <li class="table-li">
<!--                    净重-->
                  <div class="table-li-label">[[ ${product_detail_net_weight} ]](kg)</div>
                  <div class="table-li-value">[[ ${product.weight} ]]</div>
                </li>
                <li class="table-li">
<!--                    毛重-->
                  <div class="table-li-label">[[ ${product_detail_gross_weight} ]](kg)</div>
                  <div class="table-li-value">[[ ${product.fullWeight} ]]</div>
                </li>
                <li class="table-li">
<!--                    尺寸-->
                  <div class="table-li-label">[[ ${product_detail_size} ]](mm)</div>
                  <div class="table-li-value">[[ ${product.width} ]]</div>
                </li>
              </ul>
            </div>

            <!-- 同系列其他型号 -->
            <div class="other-list">
              <b>[[ ${product_series_other_model} ]]</b>
              <span th:each="item:${theSameSeries.list}">
                <a
                  th:href="${@urlUtil.getJumpUrl('/product/productInfo/m_'+item.mainClass+'/'+item.id+'.html')}"
                  target="_blank"
                  th:text="${item.title}"
                ></a>
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="container-detail">
      <div class="container-box">
        <div class="container">
          <ul class="tab-nav">
            <!--产品综述-->
            <li class="tab-nav-li active">[[ ${product_overview} ]]</li>
            <!--详细参数-->
            <li class="tab-nav-li">[[ ${detailed_parameters} ]]</li>
            <!--应用方案-->
            <li class="tab-nav-li">[[${application_scheme}]]</li>
              <!--资料下载-->
            <li class="tab-nav-li">[[${document_download}]]</li>
          </ul>

          <ul class="tab-list">
            <!-- 产品综述 -->
            <li class="tab-list-li">
              <p th:each="item:${#strings.arraySplit(product.imgs,',')}">
                <img width="100%" th:src="${item}" />
              </p>
            </li>
            <!-- 详细参数 -->
            <li class="tab-list-li">
              <div class="detail-content">
                <div>
                  <img
                    width="100%"
                    th:src="${staticSite}+'images/table-list.png'"
                  />
                </div>
                <div class="detail-bottom">
                  <h3 class="detail-bottom-title">[[ ${detailed_parameters} ]]</h3>

                  <!-- 富文本展示区域 -->
                  <div th:if="${product.detailParameters!=null}"  th:utext="${product.detailParameters.content}"></div>
                </div>
              </div>
            </li>
            <!-- 应用方案 -->
            <li class="tab-list-li tab-list-li-plan">

              <a class="plan-wrap" target="_blank" th:each="item:${schemeList}" th:href="@{'/program/programInfo?classify='+${item.classify}+'&listno='+${item.listNo}}">
                <div class="plan-title">[[ ${item.title} ]]</div>
                <div class="plan-content">
<!--                  <span class="plan-content-left">延长6小时</span>-->
<!--                  <span class="plan-content-right">配瑞物电池</span>-->
                  <span class="plan-content-right">[[ ${item.labelIdName} ]]</span>
                </div>
              </a>


            </li>
            <!-- 资料下载 -->
            <li class="tab-list-li">
              <div class="detail-content">
                <div>
                  <img
                    width="100%"
                    th:src="${staticSite}+'images/table-list.png'"
                  />
                </div>
                <div class="detail-bottom">


                  <h3 class="detail-bottom-title">[[${document_download}]]</h3>

                  <table>
                    <thead>
                      <tr>
                        <th>[[ ${download_table_file_name} ]]</th>
                        <th>[[ ${download_table_file_size} ]]</th>
                        <th>[[ ${download_table_operation} ]]</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr th:each="item:${attachmentList}">
                        <td>
                          <img
                            src="pdf-icon.png"
                            alt="PDF Icon"
                            onerror="javascript:this.src='/images/img-err.png'"
                          />
                          [[ ${item.title} ]]
                        </td>
                        <td>[[ ${item.attachmentSize} ]]</td>
                        <td><button th:attr="data-href=${item.attachment}" class="download-btn">[[ ${download_btn} ]]</button></td>
                      </tr>
                      <!-- 重复其他文件 -->
                    </tbody>
                  </table>
                </div>
              </div>
            </li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</html>
