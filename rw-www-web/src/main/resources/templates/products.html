<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org" xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout" layout:decorate="layout/master">
    <div id="page-content" class="horizontal" layout:fragment="content">
        <input type="hidden" id="lang" th:value="${lang}" />
        <input type="hidden" id="mainclass" th:value="${mainclass}" />

        <section class="breadcrumb">
            <div class="container">
                <ul>
                    <!--                <li><a href="/index">[[ ${homepage} ]]</a> ></li>-->
                    <li><a th:href="${@urlUtil.getJumpUrl('/')}">[[ ${homepage} ]]</a> ></li>
                    <li><a th:href="${@urlUtil.getJumpUrl('/product/product')}">[[ ${product_title} ]]</a> ></li>
                    <li th:if="${mainclass == 1}">[[ ${product_usp_power_supply} ]]</li>
                    <li th:if="${mainclass == 2}">[[ ${product_battery} ]]</li>
                    <li th:if="${mainclass == 5}">[[ ${product_outdoor_unit} ]]</li>
                    <li th:if="${mainclass == 6}">[[ ${product_indoor_unit} ]]</li>
                    <li th:if="${mainclass == 40}">[[ ${product_eps_power_supply} ]]</li>
                    <li th:if="${mainclass == 50}">[[ ${product_sts} ]]</li>
                    <li th:if="${mainclass == 51}">[[ ${product_pdu} ]]</li>
                    <li th:if="${mainclass == 52}">[[ ${product_voltage_stabilizing_power_supply} ]]</li>
                    <li th:if="${mainclass == 53}">[[ ${product_inverter} ]]</li>
                    <li th:if="${mainclass == 68}">[[ ${product_battery_cabinet} ]]</li>
                    <li th:if="${mainclass == 69}">[[ ${product_battery_wire} ]]</li>
                </ul>
            </div>
        </section>
        <!-- <div>8888888</div> -->
        <div class="container">
            <div class="term">
                <dl class="term-list" th:each="item,itemStat:${productFunctionParameters}">
                    <dt th:data-type="${item.attrName}">[[ ${item.title} ]]<i class="fa fa-angle-up"></i><i class="fa fa-angle-down"></i></dt>
                    <dd>
                        <a th:attr="data-mainclass=${mainclass}" data-id="" href="javascript:void(0)" class="active classify">[[ ${all} ]]</a>
                        <span th:each="attr:${item.attrList}">
                            <a th:attr="data-id=${attr.id},data-mainclass=${mainclass}" href="javascript:void(0)" class="classify"><span th:text="${attr.title}"></span></a>
                        </span>
                    </dd>
                </dl>
                <!-- <dl class="term-reset">
                    <dt></dt>
                    <dd><a class="clear-option" href="javascript:;">[[ ${reset_filter} ]]</a></dd>
                </dl> -->
            </div>

            <div class="clear"></div>
            <!--        <h5>共找到<b><span th:text="${product.total}">0</span> </b>个产品</h5>-->

            <!--<span th:text="${#strings.replace(product_list_count_tip, '{0}', product.total)}"></span>-->

            <div th:if="${!#lists.isEmpty(product) && #lists.size(product) > 0}">
                <section class="list-box">
                    <div th:if="${mainclass == 1}" class="article">
                        <dl>
                            <dt style="width: 60px">[[${product_model}]]</dt>
                            <dt></dt>
                            <dt>[[ ${product_series} ]]</dt>
                            <dt>[[ ${product_capacity} ]]</dt>
                            <dt>[[ ${product_input_and_output_voltage} ]]</dt>
                            <dt>[[ ${product_size} ]]</dt>
                            <dt class="m-none">[[ ${product_weight} ]]</dt>
                        </dl>
                        <dl th:each="item:${product}">
                            <dd>
                                <img th:if="${item.img != null && item.img!=''}" th:src="${#strings.arraySplit(item.img,',')[0]}" onerror="javascript:this.src='/images/img-err.png'" />
                                <img th:if="${item.img == null || item.img==''}" src="" onerror="javascript:this.src='/images/img-err.png'" />
                            </dd>
                            <dd>
                                <a th:href="${@urlUtil.getJumpUrl('/product/productInfo/m_'+item.mainClass+'/'+item.id+'.html')}"><span th:text="${item.title}"></span></a>
                            </dd>
                            <dd><span th:text="${item.seriesTitle}"></span></dd>
                            <dd><span th:text="${item.capacityTitle}"></span></dd>
                            <dd><span th:text="${item.voltageSortTitle}"></span></dd>
                            <dd><span th:text="${item.length}"></span></dd>
                            <dd><span th:text="${item.fullWeight}"></span></dd>
                        </dl>
                    </div>

                    <div th:if="${mainclass == 2}" class="article">
                        <dl>
                            <dt style="width: 60px">[[${product_model}]]</dt>
                            <dt></dt>
                            <dt>[[ ${product_series} ]]</dt>
                            <dt>[[ ${product_capacity} ]]</dt>
                            <dt>[[ ${product_voltage} ]]</dt>
                            <dt>[[ ${product_size} ]]</dt>
                            <dt class="m-none">[[ ${product_weight} ]]</dt>
                        </dl>
                        <dl th:each="item:${product}">
                            <dd>
                                <img th:if="${item.img != null && item.img!=''}" th:src="${#strings.arraySplit(item.img,',')[0]}" onerror="javascript:this.src='/images/img-err.png'" />
                                <img th:if="${item.img == null || item.img==''}" src="" onerror="javascript:this.src='/images/img-err.png'" />
                            </dd>
                            <dd>
                                <a th:href="${@urlUtil.getJumpUrl('/product/productInfo/m_'+item.mainClass+'/'+item.id+'.html')}"><span th:text="${item.title}"></span></a>
                            </dd>
                            <dd><span th:text="${item.seriesTitle}"></span></dd>
                            <dd><span th:text="${item.capacityTitle}"></span></dd>
                            <dd><span th:text="${item.voltageTitle}"></span></dd>
                            <dd><span th:text="${item.length}"></span></dd>
                            <dd><span th:text="${item.fullWeight}"></span></dd>
                        </dl>
                    </div>

                    <div th:if="${mainclass == 5}" class="article">
                        <dl>
                            <dt style="width: 60px">[[${product_model}]]</dt>
                            <dt></dt>
                            <dt>[[ ${product_series} ]]</dt>
                            <dt>[[ ${product_size} ]]</dt>
                            <dt class="m-none">[[ ${product_weight} ]]</dt>
                        </dl>
                        <dl th:each="item:${product}">
                            <dd>
                                <img th:if="${item.img != null && item.img!=''}" th:src="${#strings.arraySplit(item.img,',')[0]}" onerror="javascript:this.src='/images/img-err.png'" />
                                <img th:if="${item.img == null || item.img==''}" src="" onerror="javascript:this.src='/images/img-err.png'" />
                            </dd>
                            <dd>
                                <a th:href="${@urlUtil.getJumpUrl('/product/productInfo/m_'+item.mainClass+'/'+item.id+'.html')}"><span th:text="${item.title}"></span></a>
                            </dd>
                            <dd><span th:text="${item.seriesTitle}"></span></dd>
                            <dd><span th:text="${item.length}"></span></dd>
                            <dd><span th:text="${item.fullWeight}"></span></dd>
                        </dl>
                    </div>

                    <div th:if="${mainclass == 6}" class="article">
                        <dl>
                            <dt style="width: 60px">[[${product_model}]]</dt>
                            <dt></dt>
                            <dt>[[ ${product_series} ]]</dt>
                            <dt>[[ ${product_refrigerating_capacity} ]]</dt>
                            <dt>[[ ${product_heating_capacity} ]]</dt>
                            <dt>[[ ${product_air_conditioning_type} ]]</dt>
                            <dt>[[ ${product_cooling_method} ]]</dt>
                            <dt>[[ ${product_air_supply_mode} ]]</dt>
                            <dt>[[ ${product_size} ]]</dt>
                            <dt class="m-none">[[ ${product_weight} ]]</dt>
                        </dl>
                        <dl th:each="item:${product}">
                            <dd>
                                <img th:if="${item.img != null && item.img!=''}" th:src="${#strings.arraySplit(item.img,',')[0]}" onerror="javascript:this.src='/images/img-err.png'" />
                                <img th:if="${item.img == null || item.img==''}" src="" onerror="javascript:this.src='/images/img-err.png'" />
                            </dd>
                            <dd>
                                <a th:href="${@urlUtil.getJumpUrl('/product/productInfo/m_'+item.mainClass+'/'+item.id+'.html')}"><span th:text="${item.title}"></span></a>
                            </dd>
                            <dd><span th:text="${item.seriesTitle}"></span></dd>
                            <dd><span th:text="${item.refrigeratingTitle}"></span></dd>
                            <dd><span th:text="${item.heatingTitle}"></span></dd>
                            <dd><span th:text="${item.airTypeTitle}"></span></dd>
                            <dd><span th:text="${item.coolingTypeTitle}"></span></dd>
                            <dd><span th:text="${item.airSupplyTitle}"></span></dd>
                            <dd><span th:text="${item.length}"></span></dd>
                            <dd><span th:text="${item.fullWeight}"></span></dd>
                        </dl>
                    </div>

                    <div th:if="${mainclass == 40}" class="article">
                        <dl>
                            <dt style="width: 60px">[[${product_model}]]</dt>
                            <dt></dt>
                            <dt>[[ ${product_capacity} ]]</dt>
                            <dt>[[ ${product_type} ]]</dt>
                            <dt>[[ ${product_size} ]]</dt>
                            <dt class="m-none">[[ ${product_weight} ]]</dt>
                        </dl>
                        <dl th:each="item:${product}">
                            <dd>
                                <img th:if="${item.img != null && item.img!=''}" th:src="${#strings.arraySplit(item.img,',')[0]}" onerror="javascript:this.src='/images/img-err.png'" />
                                <img th:if="${item.img == null || item.img==''}" src="" onerror="javascript:this.src='/images/img-err.png'" />
                            </dd>
                            <dd>
                                <a th:href="${@urlUtil.getJumpUrl('/product/productInfo/m_'+item.mainClass+'/'+item.id+'.html')}"><span th:text="${item.title}"></span></a>
                            </dd>
                            <dd><span th:text="${item.capacityTitle}"></span></dd>
                            <dd><span th:text="${item.classifyTitle}"></span></dd>
                            <dd><span th:text="${item.width}"></span></dd>
                            <dd><span th:text="${item.fullWeight}"></span></dd>
                        </dl>
                    </div>

                    <div th:if="${mainclass == 50}" class="article">
                        <dl>
                            <dt style="width: 60px">[[${product_model}]]</dt>
                            <dt></dt>
                            <dt>[[ ${product_series} ]]</dt>
                            <dt>[[ ${product_capacity} ]]</dt>
                            <dt>[[ ${product_voltage} ]]</dt>
                            <dt>[[ ${product_grade} ]]</dt>
                            <dt>[[ ${product_size} ]]</dt>
                            <dt class="m-none">[[ ${product_weight} ]]</dt>
                        </dl>
                        <dl th:each="item:${product}">
                            <dd>
                                <img th:if="${item.img != null && item.img!=''}" th:src="${#strings.arraySplit(item.img,',')[0]}" onerror="javascript:this.src='/images/img-err.png'" />
                                <img th:if="${item.img == null || item.img==''}" src="" onerror="javascript:this.src='/images/img-err.png'" />
                            </dd>
                            <dd>
                                <a th:href="${@urlUtil.getJumpUrl('/product/productInfo/m_'+item.mainClass+'/'+item.id+'.html')}"><span th:text="${item.title}"></span></a>
                            </dd>
                            <dd><span th:text="${item.seriesTitle}"></span></dd>
                            <dd><span th:text="${item.capacityTitle}"></span></dd>
                            <dd><span th:text="${item.voltageTitle}"></span></dd>
                            <dd><span th:text="${item.rolesTitle}"></span></dd>
                            <dd><span th:text="${item.width}"></span></dd>
                            <dd><span th:text="${item.fullWeight}"></span></dd>
                        </dl>
                    </div>

                    <div th:if="${mainclass == 51}" class="article">
                        <dl>
                            <dt style="width: 60px">[[${product_model} ]]</dt>
                            <dt></dt>
                            <dt>[[${product_series}]]</dt>
                            <dt>[[${product_capacity}]]</dt>
                            <dt>[[${product_number_of_holes}]]</dt>
                            <dt>[[${product_type}]]</dt>
                            <dt>[[${product_size}]]</dt>
                            <dt class="m-none">[[${product_weight}]]</dt>
                        </dl>
                        <dl th:each="item:${product}">
                            <dd>
                                <img th:if="${item.img != null && item.img!=''}" th:src="${#strings.arraySplit(item.img,',')[0]}" onerror="javascript:this.src='/images/img-err.png'" />
                                <img th:if="${item.img == null || item.img==''}" src="" onerror="javascript:this.src='/images/img-err.png'" />
                            </dd>
                            <dd>
                                <a th:href="${@urlUtil.getJumpUrl('/product/productInfo/m_'+item.mainClass+'/'+item.id+'.html')}"><span th:text="${item.title}"></span></a>
                            </dd>
                            <dd><span th:text="${item.seriesTitle}"></span></dd>
                            <dd><span th:text="${item.capacityTitle}"></span></dd>
                            <dd><span th:text="${item.rolesTitle}"></span></dd>
                            <dd><span th:text="${item.classifyTitle}"></span></dd>
                            <dd><span th:text="${item.width}"></span></dd>
                            <dd><span th:text="${item.fullWeight}"></span></dd>
                        </dl>
                    </div>

                    <div th:if="${mainclass == 52}" class="article">
                        <dl>
                            <dt style="width: 60px">[[${product_model}]]</dt>
                            <dt></dt>
                            <dt>[[${product_series}]]</dt>
                            <dt>[[${product_capacity}]]</dt>
                            <dt>[[${product_principle}]]</dt>
                            <dt>[[${product_accuracy}]]</dt>
                            <dt>[[${product_voltage}]]</dt>
                            <dt>[[${product_size}]]</dt>
                            <dt class="m-none">[[ ${product_weight}]]</dt>
                        </dl>
                        <dl th:each="item:${product}">
                            <dd>
                                <img th:if="${item.img != null && item.img!=''}" th:src="${#strings.arraySplit(item.img,',')[0]}" onerror="javascript:this.src='/images/img-err.png'" />
                                <img th:if="${item.img == null || item.img==''}" src="" onerror="javascript:this.src='/images/img-err.png'" />
                            </dd>
                            <dd>
                                <a th:href="${@urlUtil.getJumpUrl('/product/productInfo/m_'+item.mainClass+'/'+item.id+'.html')}"><span th:text="${item.title}"></span></a>
                            </dd>
                            <dd><span th:text="${item.seriesTitle}"></span></dd>
                            <dd><span th:text="${item.capacityTitle}"></span></dd>
                            <dd><span th:text="${item.principleTitle}"></span></dd>
                            <dd><span th:text="${item.accuracyTitle}"></span></dd>
                            <dd><span th:text="${item.voltageTitle}"></span></dd>
                            <dd><span th:text="${item.width}"></span></dd>
                            <dd><span th:text="${item.fullWeight}"></span></dd>
                        </dl>
                    </div>

                    <div th:if="${mainclass == 53}" class="article">
                        <dl>
                            <dt style="width: 60px">[[${product_model}]]</dt>
                            <dt></dt>
                            <dt>[[ ${product_series} ]]</dt>
                            <dt>[[ ${product_capacity} ]]</dt>
                            <dt>[[ ${product_input_voltage} ]]</dt>
                            <dt>[[ ${product_output_voltage} ]]</dt>
                            <dt>[[ ${product_size} ]]</dt>
                            <dt class="m-none">[[ ${product_weight} ]]</dt>
                        </dl>
                        <dl th:each="item:${product}">
                            <dd>
                                <img th:if="${item.img != null && item.img!=''}" th:src="${#strings.arraySplit(item.img,',')[0]}" onerror="javascript:this.src='/images/img-err.png'" />
                                <img th:if="${item.img == null || item.img==''}" src="" onerror="javascript:this.src='/images/img-err.png'" />
                            </dd>
                            <dd>
                                <a th:href="${@urlUtil.getJumpUrl('/product/productInfo/m_'+item.mainClass+'/'+item.id+'.html')}"><span th:text="${item.title}"></span></a>
                            </dd>
                            <dd><span th:text="${item.seriesTitle}"></span></dd>
                            <dd><span th:text="${item.capacityTitle}"></span></dd>
                            <dd><span th:text="${item.inputTitle}"></span></dd>
                            <dd><span th:text="${item.voltageTitle}"></span></dd>
                            <dd><span th:text="${item.width}"></span></dd>
                            <dd><span th:text="${item.fullWeight}"></span></dd>
                        </dl>
                    </div>

                    <div th:if="${mainclass == 68}" class="article">
                        <dl>
                            <dt style="width: 60px">[[${product_model}]]</dt>
                            <dt></dt>
                            <dt>[[ ${product_series} ]]</dt>
                            <dt>[[ ${product_size} ]]</dt>
                            <dt class="m-none">[[ ${product_weight} ]]</dt>
                        </dl>
                        <dl th:each="item:${product}">
                            <dd>
                                <img th:if="${item.img != null && item.img!=''}" th:src="${#strings.arraySplit(item.img,',')[0]}" onerror="javascript:this.src='/images/img-err.png'" />
                                <img th:if="${item.img == null || item.img==''}" src="" onerror="javascript:this.src='/images/img-err.png'" />
                            </dd>
                            <dd>
                                <a th:href="${@urlUtil.getJumpUrl('/product/productInfo/m_'+item.mainClass+'/'+item.id+'.html')}"><span th:text="${item.title}"></span></a>
                            </dd>
                            <dd><span th:text="${item.seriesTitle}"></span></dd>
                            <dd><span th:text="${item.length}"></span></dd>
                            <dd><span th:text="${item.fullWeight}"></span></dd>
                        </dl>
                    </div>

                    <div th:if="${mainclass == 69}" class="article">
                        <dl>
                            <dt style="width: 60px">[[${product_model}]]</dt>
                            <dt></dt>
                            <dt>[[${product_specification}]]</dt>
                        </dl>
                        <dl th:each="item:${product}">
                            <dd>
                                <img th:if="${item.img != null && item.img!=''}" th:src="${#strings.arraySplit(item.img,',')[0]}" onerror="javascript:this.src='/images/img-err.png'" />
                                <img th:if="${item.img == null || item.img==''}" src="" onerror="javascript:this.src='/images/img-err.png'" />
                            </dd>
                            <dd>
                                <a th:href="${@urlUtil.getJumpUrl('/product/productInfo/m_'+item.mainClass+'/'+item.id+'.html')}"><span th:text="${item.title}"></span></a>
                            </dd>
                            <dd><span th:text="${item.diameterTitle}"></span></dd>
                        </dl>
                    </div>

                    <div class="text-center">
                        <ul class="pagination ins-page" th:utext="${pages}"></ul>
                    </div>
                </section>
            </div>

            <section class="list-box" th:if="${#lists.isEmpty(product) || #lists.size(product) <= 0}">[[ ${no_data_available} ]]</section>
        </div>

        <script th:src="${staticSite}+'js/reception/searchProduct.js?v='+${staticVersion}"></script>
        <script th:src="${staticSite}+'js/custom.js?v='+${staticVersion}"></script>
    </div>
</html>
