<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org" xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="layout/master">

<body>

<div id="page-content" class="horizontal" layout:fragment="content">
    <section class="breadcrumb">
        <div class="container">
            <ul>
                <li><a th:href="${@urlUtil.getJumpUrl('/')}">[[ ${homepage} ]]</a> ></li>
                <li>[[ ${contact_us} ]]</li>
            </ul>
        </div>
    </section>
    <section class="contact-body">
        <div class="container">
            <h2>[[ ${contact_us} ]]</h2>
            <div class="col-md-5 col-xs-12 contact-text">
                <p><i class="fa fa-phone"></i>010-******** / ********</p>
                <p><i class="fa fa-qq"></i>********** / **********</p>
                <p><i class="fa fa-map-marker"></i>[[ ${company_address} ]]</p>
                <br>
                <p>[[ ${company_name_tip} ]]：[[ ${company_name} ]]</p>
                <p>[[ ${tax_registration_number_tip} ]]：91110108MA01Q6Y29A&nbsp;</p>
                <p>[[ ${name_of_the_deposit_bank_tip} ]]：[[ ${name_of_the_deposit_bank} ]]</p>
                <p>[[ ${bank_account_code_tip} ]]：91340078801400000832</p>
                <p>
                    <button class="copyInvoice">[[ ${copy_the_invoicing_information} ]]</button>
                    <textarea id="InvoiceInfo">[[ ${invoicing_information} ]]</textarea>
                </p>
                <p>[[ ${jd_franchised_store} ]]：<a href="https://mall.jd.com/index-731146.html">https://mall.jd.com/index-731146.html</a></p>
                <p>[[ ${jd_self_operated_store} ]]：<a href="https://mall.jd.com/index-**********.html">https://mall.jd.com/index-**********.html</a></p>
            </div>
            <div class="col-md-7 col-xs-12 map-box"><iframe src="/contact/map" width="100%" height="100%" frameborder="0" scrolling="no"></iframe></div>
        </div>
    </section>

    <script th:src="${staticSite}+'js/clipboard.min.js'"></script>
    <script>
        var clipboard = new ClipboardJS('.copyInvoice', {
            text: function() {
                return '[[ ${invoicing_information} ]]';
            }
        });
        clipboard.on('success', function(e) {
            console.log(e);
            alert('[[ ${copy_success} ]]');
        });

        clipboard.on('error', function(e) {
            console.log(e);
        });
    </script>

</div>

</body>
</html>