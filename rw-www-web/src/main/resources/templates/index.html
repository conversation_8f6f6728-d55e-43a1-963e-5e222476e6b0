<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org" xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="layout/master">

<!--<div id="page-content" layout:fragment="content">-->
<div id="page-content" layout:fragment="content">

    <section class="flexslider">
        <ul class="slides">

            <li>
                <img th:src="${staticSite}+'images/slider-img.jpg'"/>
                <div class="slide-info">
                    <div class="slide-con">
                        <b>[[ ${index_slides_1_label} ]]</b>
                        <h3>[[ ${index_slides_1_title} ]]</h3>
                        <p>[[ ${index_slides_1_desc} ]]</p>
                        <a href="/" class="ti-arrow-right"></a>
                    </div>
                </div>
            </li>

            <li>
                <img th:src="${staticSite}+'images/slider-img2.jpg'"/>
                <div class="slide-info">
                    <div class="slide-con">
                        <b>[[ ${index_slides_2_label} ]]</b>
                        <h3>[[ ${index_slides_2_title} ]]</h3>
                        <p>[[ ${index_slides_2_desc} ]]</p>
                        <a th:href="${@urlUtil.getJumpUrl('/')}" class="ti-arrow-right"></a>
                    </div>
                </div>
            </li>

        </ul>
    </section>

    <div class="container">
        <div class="row">
            <section class="services marginTop">
                <h2 class="text-center">[[ ${index_category_title} ]]</h2>
                <div class="container">
                    <div class="row">
                        <a class="services-dtl" th:href="${@urlUtil.getJumpUrl('/product/product')}">
                            <i class="fa fa-power-off"></i>
                            <h3>[[ ${index_category_1_title} ]]</h3>
                            <p>[[ ${index_category_1_desc} ]]</p>
                        </a>
                        <a class="services-dtl" th:href="${@urlUtil.getJumpUrl('/product/product')}">
                            <i class="fa fa-battery-three-quarters"></i>
                            <h3>[[ ${index_category_2_title} ]]</h3>
                            <p>[[ ${index_category_2_desc} ]]</p>
                        </a>
                        <a class="services-dtl" th:href="${@urlUtil.getJumpUrl('/product/product')}">
                            <i class="fa fa-thermometer-three-quarters"></i>
                            <h3>[[ ${index_category_3_title} ]]</h3>
                            <p>[[ ${index_category_3_desc} ]]</p>
                        </a>
                        <a class="services-dtl"  th:href="${@urlUtil.getJumpUrl('/product/product')}">
                            <i class="fa fa-plug"></i>
                            <h3>[[ ${index_category_4_title} ]]</h3>
                            <p>[[ ${index_category_4_desc} ]]</p>
                        </a>
                        <a class="services-dtl" th:href="${@urlUtil.getJumpUrl('/product/product')}">
                            <i class="fa fa-mixcloud"></i>
                            <h3>[[ ${index_category_5_title} ]]</h3>
                            <p>[[ ${index_category_5_desc} ]]</p>
                        </a>
                    </div>
                </div>
            </section>
        </div>
    </div>

    <div class="clear"></div>

    <section class="product-tab marginTop">
        <div class="container">
            <div class="row">
                <div id="parentVerticalTab">
                    <h2>[[ ${index_scheme_title} ]]</h2>
                    <ul class="resp-tabs-list hor_1">
                        <li><i class="fa fa-power-off"></i> [[ ${index_scheme_1_title} ]]</li>
                        <li><i class="fa fa-thermometer-three-quarters"></i> [[ ${index_scheme_2_title} ]]</li>


                        <!--<li><i class="fa fa-mixcloud"></i> 配电柜方案</li>-->
                    </ul>
                    <div class="resp-tabs-container hor_1">

                        <div>
                            <div class="prod-tab-content">
                                <h4>
                                    <span class="prod-cion"><i class="fa fa-power-off"></i></span>
                                    [[ ${index_scheme_1_title} ]]<a th:href="${@urlUtil.getJumpUrl('/program/programList?classify=2')}">[[ ${view_more} ]]</a>
                                </h4>
                                <div class="row">
                                    <div class="col-ss-12 col-xs-4 col-sm-4 col-md-4">
                                        <div class="plan-sec-box">
                                            <h3><a th:href="${@urlUtil.getJumpUrl('/')}">[[ ${index_scheme_list_title} ]]</a></h3>
                                            <div class="plan-sec-list">
                                                <p><span>[[ ${index_scheme_list_capacity} ]]</span>[[ ${index_scheme_list_capacity_value} ]]</p>
                                                <p><span>[[ ${index_scheme_list_battery} ]]</span>[[ ${index_scheme_list_battery_value} ]]</p>
                                                <p><span>[[ ${index_scheme_list_battery_cabinet} ]]</span>[[ ${index_scheme_list_battery_cabinet_value} ]]</p>
                                            </div>
                                            <div class="text-center"><a th:href="${@urlUtil.getJumpUrl(index_scheme_list_url)}" class="btn-default">[[ ${view_the_list} ]]</a>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-ss-12 col-xs-4 col-sm-4 col-md-4">
                                        <div class="plan-sec-box">
                                            <h3><a th:href="${@urlUtil.getJumpUrl('/')}">[[ ${index_scheme_list_title} ]]</a></h3>
                                            <div class="plan-sec-list">
                                                <p><span>[[ ${index_scheme_list_capacity} ]]</span>[[ ${index_scheme_list_capacity_value} ]]</p>
                                                <p><span>[[ ${index_scheme_list_battery} ]]</span>[[ ${index_scheme_list_battery_value} ]]</p>
                                                <p><span>[[ ${index_scheme_list_battery_cabinet} ]]</span>[[ ${index_scheme_list_battery_cabinet_value} ]]</p>
                                            </div>
                                            <div class="text-center"><a th:href="${@urlUtil.getJumpUrl(index_scheme_list_url)}" class="btn-default">[[ ${view_the_list} ]]</a>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-ss-12 col-xs-4 col-sm-4 col-md-4">
                                        <div class="plan-sec-box">
                                            <h3><a th:href="${@urlUtil.getJumpUrl('/')}">[[ ${index_scheme_list_title} ]]</a></h3>
                                            <div class="plan-sec-list">
                                                <p><span>[[ ${index_scheme_list_capacity} ]]</span>[[ ${index_scheme_list_capacity_value} ]]</p>
                                                <p><span>[[ ${index_scheme_list_battery} ]]</span>[[ ${index_scheme_list_battery_value} ]]</p>
                                                <p><span>[[ ${index_scheme_list_battery_cabinet} ]]</span>[[ ${index_scheme_list_battery_cabinet_value} ]]</p>
                                            </div>
                                            <div class="text-center"><a th:href="${@urlUtil.getJumpUrl(index_scheme_list_url)}" class="btn-default">[[ ${view_the_list} ]]</a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="clear"></div>
                            </div>
                        </div>

                        <div>
                            <div class="prod-tab-content">
                                <h4>
                                    <span class="prod-cion"><i class="fa fa-thermometer-three-quarters"></i></span>
                                    [[ ${index_scheme_2_title} ]]<a th:href="${@urlUtil.getJumpUrl('/program/programList?classify=3')}">[[ ${view_more} ]]</a>
                                </h4>

                                <div class="row">
                                    <div class="col-ss-12 col-xs-4 col-sm-4 col-md-4">
                                        <div class="plan-sec-box">
                                            <h3><a th:href="${@urlUtil.getJumpUrl('/')}">[[ ${index_scheme_list_title} ]]</a></h3>
                                            <div class="plan-sec-list">
                                                <p><span>[[ ${index_scheme_list_capacity} ]]</span>[[ ${index_scheme_list_capacity_value} ]]</p>
                                                <p><span>[[ ${index_scheme_list_battery} ]]</span>[[ ${index_scheme_list_battery_value} ]]</p>
                                                <p><span>[[ ${index_scheme_list_battery_cabinet} ]]</span>[[ ${index_scheme_list_battery_cabinet_value} ]]</p>
                                            </div>
                                            <div class="text-center"><a th:href="${@urlUtil.getJumpUrl(index_scheme_list_url)}" class="btn-default">[[ ${view_the_list} ]]</a>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-ss-12 col-xs-4 col-sm-4 col-md-4">
                                        <div class="plan-sec-box">
                                            <h3><a th:href="${@urlUtil.getJumpUrl('/')}">[[ ${index_scheme_list_title} ]]</a></h3>
                                            <div class="plan-sec-list">
                                                <p><span>[[ ${index_scheme_list_capacity} ]]</span>[[ ${index_scheme_list_capacity_value} ]]</p>
                                                <p><span>[[ ${index_scheme_list_battery} ]]</span>[[ ${index_scheme_list_battery_value} ]]</p>
                                                <p><span>[[ ${index_scheme_list_battery_cabinet} ]]</span>[[ ${index_scheme_list_battery_cabinet_value} ]]</p>
                                            </div>
                                            <div class="text-center"><a th:href="${@urlUtil.getJumpUrl(index_scheme_list_url)}" class="btn-default">[[ ${view_the_list} ]]</a>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-ss-12 col-xs-4 col-sm-4 col-md-4">
                                        <div class="plan-sec-box">
                                            <h3><a th:href="${@urlUtil.getJumpUrl('/')}">[[ ${index_scheme_list_title} ]]</a></h3>
                                            <div class="plan-sec-list">
                                                <p><span>[[ ${index_scheme_list_capacity} ]]</span>[[ ${index_scheme_list_capacity_value} ]]</p>
                                                <p><span>[[ ${index_scheme_list_battery} ]]</span>[[ ${index_scheme_list_battery_value} ]]</p>
                                                <p><span>[[ ${index_scheme_list_battery_cabinet} ]]</span>[[ ${index_scheme_list_battery_cabinet_value} ]]</p>
                                            </div>
                                            <div class="text-center"><a th:href="${@urlUtil.getJumpUrl(index_scheme_list_url)}" class="btn-default">[[ ${view_the_list} ]]</a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="clear"></div>
                            </div>
                        </div>

                    </div>
                </div>
            </div>
        </div>
    </section>

<div class="clear"></div>

<section class="company-box">
    <div class="container">
        <div class="row company-info">
            <dl>
                <dd class="about-logo">
                    <img th:src="${staticSite}+'images/logo-200.png'" alt="">
                </dd>
                <dd class="about-con flexslider news-slider">
                    <b>[[ ${index_company_title} ]]</b>
                    <p>[[ ${index_company_desc} ]]</p>


                    <div th:if="${!#lists.isEmpty(newsList) && #lists.size(newsList) > 0}">
                        <b><a th:href="${@urlUtil.getJumpUrl('/news/listNews')}">[[ ${index_company_news} ]]</a></b>
                        <ul class="slides">
                            <li th:each="news:${newsList}">
                                <a th:href="${@urlUtil.getJumpUrl('/news/newsInfo/'+news.id+'.html')}" th:title="${news.title}">
                                    <i class="fa fa-angle-right"></i>[[ ${news.title} ]]
                                </a>
                            </li>
                        </ul>
                    </div>

                </dd>
            </dl>
        </div>
    </div>
</section>



<script th:src="${staticSite}+'js/mobile-nav.js?v='+${staticVersion}"></script>
<script th:src="${staticSite}+'js/jquery.flexslider-min.js'"></script>
<script th:src="${staticSite}+'js/easyResponsiveTabs.js'"></script>
<script th:src="${staticSite}+'js/custom.js?v='+${staticVersion}"></script>

</div>
</html>