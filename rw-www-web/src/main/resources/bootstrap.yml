server:
  port: ${SERVER_PORT:8081}

# Spring
spring:
  main:
    allow-bean-definition-overriding: true
  application:
    # 应用名称
    name: rw-www-web
  profiles:
    # 环境配置
    active: ${SPRING_PROFILES_ACTIVE:dev}
  thymeleaf:
    encoding: UTF-8
    cache: false #热部署静态文件，禁止缓存
    mode: HTML5 #使用HTML5标准
  messages:
    # 国际化资源文件路径
    basename: i18n/messages
    encoding: UTF-8
    cache-duration: 3600



mybatis-plus:
  typeAliasesPackage: com.rw.**.entity
  mapperLocations: classpath:mapper/**/*.xml
  global-config:
    db-config:
      id-type: auto
      logic-delete-field: delFlag
      logic-delete-value: 1
      logic-not-delete-value: 0
logging:
  config: classpath:logback-spring.xml




