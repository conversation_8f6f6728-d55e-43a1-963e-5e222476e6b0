package com.rw.pc.web.controller;

import cn.hutool.core.convert.Convert;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rw.common.core.utils.PageResultUtils;
import com.rw.common.core.utils.ResultUtils;
import com.rw.pc.web.constant.LanguageCodeConstants;
import com.rw.pc.web.util.RwPageUtil;
import com.rw.product.model.entity.PSeriesAttachment;
import com.rw.product.service.IPSeriesAttachmentService;
import lombok.AllArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

@AllArgsConstructor
@Controller
@RequestMapping("/down")
public class DownController extends BaseController {

    @Autowired
    ResultUtils resultUtils;


    @Autowired
    IPSeriesAttachmentService seriesAttachmentService;


    RwPageUtil rwPageUtil;

    /**
     * 下载中心
     */
    @GetMapping("down")
    public ModelAndView listCase(PSeriesAttachment req, HttpServletRequest request, HttpServletResponse response) {

        ModelAndView view = baseView(request, "下载中心", "downList");
        view.addObject("keywords", "");
        view.addObject("descriptions", "");
        view.addObject("title", req.getTitle());

        Integer pageNo = Convert.toInt(request.getParameter("pageNo"), 1);

        String homepage = resultUtils.getMsg(LanguageCodeConstants.homepage);
        String download_center = resultUtils.getMsg(LanguageCodeConstants.download_center);
        String download_list_count_tip = resultUtils.getMsg(LanguageCodeConstants.download_list_count_tip);
        String download_search = resultUtils.getMsg(LanguageCodeConstants.download_search);
        String download_table_file_name = resultUtils.getMsg(LanguageCodeConstants.download_table_file_name);
        String download_table_update_time = resultUtils.getMsg(LanguageCodeConstants.download_table_update_time);
        String download_table_file_size = resultUtils.getMsg(LanguageCodeConstants.download_table_file_size);
        String download_table_operation = resultUtils.getMsg(LanguageCodeConstants.download_table_operation);
        String download_btn = resultUtils.getMsg(LanguageCodeConstants.download_btn);



        view.addObject("homepage", homepage);
        view.addObject("download_center", download_center);
        view.addObject("download_list_count_tip", download_list_count_tip);
        view.addObject("download_search", download_search);
        view.addObject("download_table_file_name", download_table_file_name);
        view.addObject("download_table_update_time", download_table_update_time);
        view.addObject("download_table_file_size", download_table_file_size);
        view.addObject("download_table_operation", download_table_operation);
        view.addObject("download_btn", download_btn);


        //下载中心
        PSeriesAttachment seriesAttachment = new PSeriesAttachment();
        seriesAttachment.setPageNum(pageNo);
        seriesAttachment.setPageSize(10);
        seriesAttachment.setLanguage(LocaleContextHolder.getLocale().getLanguage());
        seriesAttachment.setTitle(req.getTitle());

        PageResultUtils<PSeriesAttachment> pageList = seriesAttachmentService.page(seriesAttachment);



        Page page = new Page();
        page.setTotal(pageList.getTotalCount());
        page.setSize(pageList.getPageSize());
        page.setPages(pageList.getTotalPage());
        page.setCurrent(pageList.getCurrPage());

        view.addObject("list", pageList.getList());
        view.addObject("pages", rwPageUtil.getRwPageHTML(page, request));
        view.addObject("totalCount", pageList.getTotalCount());

        return view;
    }


}
