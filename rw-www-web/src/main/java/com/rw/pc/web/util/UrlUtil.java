package com.rw.pc.web.util;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import com.rw.common.core.utils.ServletUtils;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;

@Component("urlUtil")
public class UrlUtil {
    public String getJumpUrl(String url, String language) {
        if (StrUtil.isEmpty(language) || language.toLowerCase().equals("zh")) {
            return url;
        }
        if (url.toLowerCase().equals("/")) {
            url = "";
        }
        if (url.toLowerCase().equals("javascript:void(0)")) {
            return url;
        }
        return "/en" + url;
    }

    public String getJumpUrl(String url) {
        if (StrUtil.isEmpty(url)){
            return "javascript:void(0)";
        }
        HttpServletRequest request = ServletUtils.getRequest();
        String language = request.getParameter("lang");
        if (StrUtil.isEmpty(language) || language.toLowerCase().equals("zh")) {
            return url;
        }
        if (url.toLowerCase().equals("/")) {
            url = "";
        }
        if (url.toLowerCase().equals("javascript:void(0)")) {
            return url;
        }
        return "/en" + url;
    }

}
