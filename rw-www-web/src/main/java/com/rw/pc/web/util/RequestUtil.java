package com.rw.pc.web.util;

import cn.hutool.core.util.StrUtil;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.Map;

@Component
public class RequestUtil {
    public Map<String, String[]> getAllParameters(HttpServletRequest request) {
        final Map<String, String[]> params = new HashMap<>();
        final Enumeration<String> paramNames = request.getParameterNames();
        while (paramNames.hasMoreElements()) {
            final String paramName = paramNames.nextElement();
            final String[] values = request.getParameterValues(paramName);
            params.put(paramName, values);
        }
        return params;
    }

    public String getParameterMapString(HttpServletRequest request) {
        final Map<String, String[]> params = getAllParameters(request);
        final StringBuilder paramStr = new StringBuilder();
        params.forEach((key, values) -> {
            if (values != null) {
                for (String value : values) {
                    paramStr.append(key).append(": ").append(value).append(", ");
                }
            }
        });
        final String paramsString = StrUtil.removeSuffix(paramStr.toString(), ", ");
        return paramsString.isEmpty() ? "{}" : paramsString;
    }

    public String getParameterMapUrl(HttpServletRequest request, Integer current) {
        final Map<String, String[]> params = getAllParameters(request);
        final StringBuilder paramStr = new StringBuilder();
        params.forEach((key, values) -> {
            if (values != null && !key.equals("lang")) {
                for (String value : values) {
                    if (key.equals("pageNo") && current != null) {
                        paramStr.append(key).append("=").append(current).append("&");
                    } else {
                        paramStr.append(key).append("=").append(value).append("&");
                    }
                }
            }
        });
        if (null == params.get("pageNo")) {
            //如果参数里没有pageNo参数，则需要拼接
            paramStr.append("pageNo=").append(current);
        }
        return StrUtil.removeSuffix(paramStr.toString(), "&");
    }
}