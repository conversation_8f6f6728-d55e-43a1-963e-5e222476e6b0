package com.rw.pc.web.util;

import com.rw.common.core.utils.ResultUtils;
import com.rw.common.redis.service.RedisService;
import com.rw.product.service.ISLabelService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ResourceBundle;

/**
 * 方案筛选条件
 */
@Service
public class RwUtil {
    /**
     * @Fields rm : Redis的业务 数据输入输出
     */
    @Autowired
    private RedisService rm;

    @Autowired
    private ISLabelService labelService;

    @Autowired
    ResultUtils resultUtils;

    /**
     * @Fields bundle : 方案功能参数文件
     */
    private static final ResourceBundle PROGRAMME_LABEL = ResourceBundle.getBundle("rw");


    /**
     * 获取方案品牌ID
     * @param classify
     * @return
     */
    public String getBoxlistBrand(int classify) {
        String brandId="";
        try {
            brandId=PROGRAMME_LABEL.getString(classify + ".label.brand.rw");
        } catch (Exception e) {
            //如果报错，说明没有声明这个属性的ID，就是没有这个品牌
        }
        return brandId;
    }


}
