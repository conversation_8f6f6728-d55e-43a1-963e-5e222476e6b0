package com.rw.pc.web.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.List;

@Data
@Component
@ConfigurationProperties(prefix = "author")
public class AuthorConfig {
    //用户ID
    private Integer authorId;
    //用户类型
    private Integer authorClassify;

    //用户类型
    private Integer userType;

}
