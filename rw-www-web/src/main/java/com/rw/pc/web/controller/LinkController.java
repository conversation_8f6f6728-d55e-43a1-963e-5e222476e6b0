package com.rw.pc.web.controller;


import com.rw.common.core.utils.ResultUtils;
import com.rw.pc.web.constant.LanguageCodeConstants;
import lombok.AllArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * 用户信息
 *
 * <AUTHOR>
 */
@AllArgsConstructor
@Controller
@RequestMapping("/link")
public class LinkController extends BaseController {

    @Autowired
    ResultUtils resultUtils;

    /**
     * 联系我们
     */
    @GetMapping("contactUs")
    public ModelAndView contactUs(HttpServletRequest request, HttpServletResponse response) {
        ModelAndView view = baseView(request, "联系我们", "contact-us");
        view.addObject("keywords", "");
        view.addObject("descriptions", "");



        String homepage=resultUtils.getMsg(LanguageCodeConstants.homepage);
        String contact_us=resultUtils.getMsg(LanguageCodeConstants.contact_us);
        String company_address=resultUtils.getMsg(LanguageCodeConstants.company_address);

        String company_name_tip=resultUtils.getMsg(LanguageCodeConstants.company_name_tip);
        String company_name=resultUtils.getMsg(LanguageCodeConstants.company_name);
        String tax_registration_number_tip=resultUtils.getMsg(LanguageCodeConstants.tax_registration_number_tip);
        String name_of_the_deposit_bank_tip=resultUtils.getMsg(LanguageCodeConstants.name_of_the_deposit_bank_tip);
        String name_of_the_deposit_bank=resultUtils.getMsg(LanguageCodeConstants.name_of_the_deposit_bank);
        String bank_account_code_tip=resultUtils.getMsg(LanguageCodeConstants.bank_account_code_tip);
        String copy_the_invoicing_information=resultUtils.getMsg(LanguageCodeConstants.copy_the_invoicing_information);
        String invoicing_information=resultUtils.getMsg(LanguageCodeConstants.invoicing_information);
        String copy_success=resultUtils.getMsg(LanguageCodeConstants.copy_success);
        String jd_franchised_store=resultUtils.getMsg(LanguageCodeConstants.jd_franchised_store);
        String jd_self_operated_store=resultUtils.getMsg(LanguageCodeConstants.jd_self_operated_store);
        view.addObject("homepage",homepage);
        view.addObject("contact_us",contact_us);
        view.addObject("company_address",company_address);
        view.addObject("company_name_tip",company_name_tip);
        view.addObject("company_name",company_name);
        view.addObject("tax_registration_number_tip",tax_registration_number_tip);
        view.addObject("name_of_the_deposit_bank_tip",name_of_the_deposit_bank_tip);
        view.addObject("name_of_the_deposit_bank",name_of_the_deposit_bank);
        view.addObject("bank_account_code_tip",bank_account_code_tip);
        view.addObject("copy_the_invoicing_information",copy_the_invoicing_information);
        view.addObject("invoicing_information",invoicing_information);
        view.addObject("copy_success",copy_success);
        view.addObject("jd_franchised_store",jd_franchised_store);
        view.addObject("jd_self_operated_store",jd_self_operated_store);
        return view;
    }

    /**
     * 联系我们地图
     */
    @GetMapping("map")
    public ModelAndView map(HttpServletRequest request, HttpServletResponse response) {
        ModelAndView view = baseView(request, "地图", "map");
        view.addObject("keywords", "");
        view.addObject("descriptions", "");
        return view;
    }

}
