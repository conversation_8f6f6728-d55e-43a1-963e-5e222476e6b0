package com.rw.pc.web.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class ProductListReq {

    private String id;

    private String mainclass;

    @ApiModelProperty(value = "系列")
    private Integer series;

    @ApiModelProperty(value = "容量")
    private Integer capacity;

    @ApiModelProperty(value = "输入输出")
    private String voltagesort;

    @ApiModelProperty(value = "版本")
    private String version;

    @ApiModelProperty(value = "电池分类")
    private String batterytype;

    @ApiModelProperty(value = "电池形式")
    private String batteryform;




    @ApiModelProperty(value = "原理")
    private String principle;

    @ApiModelProperty(value = "精度")
    private String accuracy;

    @ApiModelProperty(value = "电压")
    private String voltage;

    @ApiModelProperty(value = "输入电压")
    private String input;


    @ApiModelProperty(value = "类型")
    private String classify;

    @ApiModelProperty(value = "孔数 级数")
    private String roles;



    @ApiModelProperty(value = "送风方式")
    private String airSupply;

    @ApiModelProperty(value = "尺寸")
    private String length;

    @ApiModelProperty(value = "制热量")
    private String heating;

    @ApiModelProperty(value = "制冷量")
    private String refrigerating;

    @ApiModelProperty(value = "用电类型")
    private String electricType;

    @ApiModelProperty(value = "空调类型")
    private String airType;

    @ApiModelProperty(value = "冷却方式")
    private String coolingType;

    @ApiModelProperty(value = "配件类别")
    private String accessory;

    private String pageNo;
}
