package com.rw.pc.web.util;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.rw.common.core.domain.R;
import com.rw.common.core.utils.ResultUtils;
import com.rw.common.redis.service.RedisService;
import com.rw.pc.web.model.ProductListWhereModel;
import com.rw.product.model.entity.PAttr;
import com.rw.product.service.IPAttrService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.ResourceBundle;

/**
 * 产品筛选条件
 */
@Service
public class ProductConditionsUtil {
    /**
     * @Fields rm : Redis的业务 数据输入输出
     */
    @Autowired
    private RedisService redisService;

    @Autowired
    private IPAttrService attrService;

    @Autowired
    ResultUtils resultUtils;

    /**
     * @Fields bundle : 产品功能参数文件
     */
    private static final ResourceBundle PRODUCTS_FUNCTION = ResourceBundle.getBundle("products");

    /**
     * 获取筛选参数
     *
     * @param mainclass
     * @return
     */
    public List<ProductListWhereModel> getProductParameters(int mainclass) {
        List<ProductListWhereModel> list = new ArrayList<>();

        String redisKey = "products:" + mainclass + ":function";
        //当前分类名称 例：ups.   air.
        String classField = getClassField(mainclass);
        //存储要查询的属性名
        List<String> functions = new ArrayList<String>();
        //在products.properties里查找对应的值  例：ups.functions  的值，会取出series,capacity,voltagesort,version,batterytype,batteryform，根据逗号分割放到functions集合里
        Collections.addAll(functions, PRODUCTS_FUNCTION.getString(classField + "functions").split(","));

        //循环functions查询属性
        for (String field : functions) {

            int fatherId = 0;
            try {
                fatherId = Integer.parseInt(PRODUCTS_FUNCTION.getString(classField + field));
            } catch (Exception e) {
                //如果报错，说明没有声明这个属性的ID，就是不需要查询
                continue;
            }

            //系列
            long seriesCount = list.stream().filter(x -> x.getAttrName().equals("series")).count();
            //如果当前要查询的是系列，但是已经有了，就不用在查了
            if ("series".equals(field) && seriesCount > 0) {
                continue;
            }

            PAttr pAttr = new PAttr();
            pAttr.setFatherId(fatherId);
            pAttr.setIsShow(1);

            //如果是品牌，直接根据品牌ID取系列，因为官网不需要展示品牌，全是瑞物品牌的
            if ("brand".equals(field)) {
                // 柜体品牌跟系列没有联系 各独立取值
                if (mainclass == 15) {
                    pAttr.setFatherId(Integer.parseInt(PRODUCTS_FUNCTION.getString(classField + "series")));
                }

                List<PAttr> seriesList = getAttrList(pAttr);
                if (CollUtil.isNotEmpty(seriesList)) {
                    ProductListWhereModel series = new ProductListWhereModel();
                    series.setAttrName("series");
                    series.setAttrList(getAttrList(pAttr));// 系列
                    list.add(series);
                }

            }
            else {
                List<PAttr> otherList = getAttrList(pAttr);
                if (CollUtil.isNotEmpty(otherList)) {
                    ProductListWhereModel other = new ProductListWhereModel();
                    other.setAttrName(field);
                    other.setAttrList(getAttrList(pAttr));
                    list.add(other);
                }
            }
        }


        if (CollUtil.isNotEmpty(list)) {
            for (ProductListWhereModel model : list) {
                String title = model.getAttrName();

                try {
                    title = resultUtils.getMsg(model.getAttrName());
                } catch (Exception e) {

                }
                model.setTitle(title);
            }
        }

        return list;
    }


    /**
     * 获取分类前缀
     * @param mainclass
     * @return
     */
    private String getClassField(int mainclass) {
        String classField = "";
        switch (mainclass) {
            case 1:
                classField = "ups.";
                break;
            case 2:
                classField = "battery.";
                break;
            case 4:
                classField = "air.";
                break;
            case 5:
                classField = "outdoor.";
                break;
            case 6:
                classField = "indoor.";
                break;
            case 15:
                classField = "box.";
                break;
            case 16:
                classField = "meter.";
                break;
            case 17:
                classField = "light.";
                break;
            case 18:
                classField = "inductor.";
                break;
            case 19:
                classField = "protection.";
                break;
            case 20:
                classField = "capperbar.";
                break;
            case 21:
                classField = "socket.";
                break;
            case 22:
                classField = "distbox.part.";
                break;
            case 23:
                classField = "ats.";
                break;
            case 24:
                classField = "gas.";
                break;
            case 25:
                classField = "pipe.";
                break;
            case 27:
                classField = "refrigeration.";
                break;
            case 30:
                classField = "camera.";
                break;
            case 31:
                classField = "recorder.";
                break;
            case 32:
                classField = "switch.";
                break;
            case 33:
                classField = "transmission.";
                break;
            case 40:
                classField = "eps.";
                break;
            case 50:
                classField = "sts.";
                break;
            case 51:
                classField = "pdu.";
                break;
            case 52:
                classField = "rps.";
                break;
            case 53:
                classField = "inverter.";
                break;
            case 68:
                classField = "batterybox.";
                break;
            case 69:
                classField = "batteryline.";
                break;
            case 70:
                classField = "csc.";
                break;
            case 71:
                classField = "distboxs.";
                break;
            case 72:
                classField = "upss.";
                break;
            case 73:
                classField = "airs.";
                break;
            case 74:
                classField = "lw.";
                break;
            case 75:
                classField = "wsd.";
                break;
            case 76:
                classField = "video.";
                break;
            case 77:
                classField = "acs.";
                break;
            case 78:
                classField = "ff.";
                break;
            case 80:
                classField = "qs.";
                break;
            case 81:
                classField = "ql.";
                break;
            case 82:
                classField = "km.";
                break;
            case 83:
                classField = "fu.";
                break;
            case 84:
                classField = "ka.";
                break;
            case 85:
                classField = "ac.";
                break;
            case 200:
                classField = "ups.part.";
                break;
            case 201:
                classField = "air.part.";
                break;
            case 202:
                classField = "monitor.part.";
                break;
        }

        return classField;
    }

    /**
     * 获取属性
     *
     * @param pAttr
     * @return
     */
    private List<PAttr> getAttrList(PAttr pAttr) {
        pAttr.setLanguage(LocaleContextHolder.getLocale().getLanguage());
        R r = attrService.getAttrList(pAttr);
        return BeanUtil.copyToList((List<PAttr>) r.getData(), PAttr.class);
    }


    /**
     * 获取分类的品牌ID
     * @param mainclass
     * @return
     */
    public int getBrandId(int mainclass) {
        //获取分类前缀
        String classField = getClassField(mainclass);

        int brandId=0;
        try {
            brandId=Integer.parseInt(PRODUCTS_FUNCTION.getString(classField + "brand"));
        } catch (Exception e) {
            //如果报错，说明没有声明这个属性的ID，就是没有这个品牌
        }

        return brandId;
    }


}
