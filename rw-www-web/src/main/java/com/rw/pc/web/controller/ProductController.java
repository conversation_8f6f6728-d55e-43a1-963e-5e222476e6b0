package com.rw.pc.web.controller;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.convert.Convert;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rw.common.core.domain.R;
import com.rw.common.core.utils.PageResultUtils;
import com.rw.common.core.utils.ResultUtils;
import com.rw.pc.web.config.AuthorConfig;
import com.rw.pc.web.constant.LanguageCodeConstants;
import com.rw.pc.web.model.ProductListReq;
import com.rw.pc.web.model.ProductListWhereModel;
import com.rw.pc.web.util.ProductConditionsUtil;
import com.rw.pc.web.util.ProgramConditionsUtil;
import com.rw.pc.web.util.RwPageUtil;
import com.rw.product.model.constant.ProductMainClassConstants;
import com.rw.product.model.entity.*;
import com.rw.product.model.enums.YesAndNoEnum;
import com.rw.product.model.req.BoxList10ByProductIdReq;
import com.rw.product.service.*;
import lombok.AllArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 用户信息
 *
 * <AUTHOR>
 */
@AllArgsConstructor
@Controller
@RequestMapping("/product")
public class ProductController extends BaseController {


    @Autowired
    ResultUtils resultUtils;


    @Autowired
    private AuthorConfig authorConfig;

    @Autowired
    private ProductConditionsUtil productConditionsUtil;

    @Autowired
    private ProgramConditionsUtil programConditionsUtil;

    @Autowired
    private IPAirService airService;

    @Autowired
    private IPEpsService epsService;

    @Autowired
    private IPPemonitorService pemonitorService;

    @Autowired
    private IPProductService productService;

    @Autowired
    private IPRoomService roomService;

    @Autowired
    private IPSingleService singleService;

    @Autowired
    private IPUpsService upsService;

    @Autowired
    private ISBoxlistService boxlistService;
    RwPageUtil rwPageUtil;

    @Autowired
    private IPSeriesAttachmentService seriesAttachmentService;

    /**
     * 产品中心
     */
    @GetMapping("product")
    public ModelAndView product(HttpServletRequest request, HttpServletResponse response) {
        ModelAndView view = baseView(request, "产品中心", "product");
        view.addObject("keywords", "");
        view.addObject("descriptions", "");
        view.addObject("articleList", null);
        String homepage = resultUtils.getMsg(LanguageCodeConstants.homepage);
        String product_title = resultUtils.getMsg(LanguageCodeConstants.product_title);

        String product_category_1_title = resultUtils.getMsg(LanguageCodeConstants.product_category_1_title);
        String product_category_1_list_1_title = resultUtils.getMsg(LanguageCodeConstants.product_category_1_list_1_title);
        String product_category_1_list_1_desc = resultUtils.getMsg(LanguageCodeConstants.product_category_1_list_1_desc);

        String product_category_1_list_2_title = resultUtils.getMsg(LanguageCodeConstants.product_category_1_list_2_title);
        String product_category_1_list_2_desc = resultUtils.getMsg(LanguageCodeConstants.product_category_1_list_2_desc);

        String product_category_1_list_3_title = resultUtils.getMsg(LanguageCodeConstants.product_category_1_list_3_title);
        String product_category_1_list_3_desc = resultUtils.getMsg(LanguageCodeConstants.product_category_1_list_3_desc);

        String product_category_1_list_4_title = resultUtils.getMsg(LanguageCodeConstants.product_category_1_list_4_title);
        String product_category_1_list_4_desc = resultUtils.getMsg(LanguageCodeConstants.product_category_1_list_4_desc);

        String product_category_1_list_5_title = resultUtils.getMsg(LanguageCodeConstants.product_category_1_list_5_title);
        String product_category_1_list_5_desc = resultUtils.getMsg(LanguageCodeConstants.product_category_1_list_5_desc);

        String product_category_2_title = resultUtils.getMsg(LanguageCodeConstants.product_category_2_title);
        String product_category_2_list_1_title = resultUtils.getMsg(LanguageCodeConstants.product_category_2_list_1_title);
        String product_category_2_list_1_desc = resultUtils.getMsg(LanguageCodeConstants.product_category_2_list_1_desc);

        String product_category_2_list_2_title = resultUtils.getMsg(LanguageCodeConstants.product_category_2_list_2_title);
        String product_category_2_list_2_desc = resultUtils.getMsg(LanguageCodeConstants.product_category_2_list_2_desc);

        String product_category_2_list_3_title = resultUtils.getMsg(LanguageCodeConstants.product_category_2_list_3_title);
        String product_category_2_list_3_desc = resultUtils.getMsg(LanguageCodeConstants.product_category_2_list_3_desc);

        String product_category_2_list_4_title = resultUtils.getMsg(LanguageCodeConstants.product_category_2_list_4_title);
        String product_category_2_list_4_desc = resultUtils.getMsg(LanguageCodeConstants.product_category_2_list_4_desc);

        String product_category_3_title = resultUtils.getMsg(LanguageCodeConstants.product_category_3_title);
        String product_category_3_list_1_title = resultUtils.getMsg(LanguageCodeConstants.product_category_3_list_1_title);
        String product_category_3_list_1_desc = resultUtils.getMsg(LanguageCodeConstants.product_category_3_list_1_desc);

        String product_category_3_list_2_title = resultUtils.getMsg(LanguageCodeConstants.product_category_3_list_2_title);
        String product_category_3_list_2_desc = resultUtils.getMsg(LanguageCodeConstants.product_category_3_list_2_desc);

        String product_category_3_list_3_title = resultUtils.getMsg(LanguageCodeConstants.product_category_3_list_3_title);
        String product_category_3_list_3_desc = resultUtils.getMsg(LanguageCodeConstants.product_category_3_list_3_desc);

        String product_category_4_title = resultUtils.getMsg(LanguageCodeConstants.product_category_4_title);
        String product_category_4_list_1_title = resultUtils.getMsg(LanguageCodeConstants.product_category_4_list_1_title);
        String product_category_4_list_1_desc = resultUtils.getMsg(LanguageCodeConstants.product_category_4_list_1_desc);

        String product_category_4_list_2_title = resultUtils.getMsg(LanguageCodeConstants.product_category_4_list_2_title);
        String product_category_4_list_2_desc = resultUtils.getMsg(LanguageCodeConstants.product_category_4_list_2_desc);

        String product_category_4_list_3_title = resultUtils.getMsg(LanguageCodeConstants.product_category_4_list_3_title);
        String product_category_4_list_3_desc = resultUtils.getMsg(LanguageCodeConstants.product_category_4_list_3_desc);

        String product_category_4_list_4_title = resultUtils.getMsg(LanguageCodeConstants.product_category_4_list_4_title);
        String product_category_4_list_4_desc = resultUtils.getMsg(LanguageCodeConstants.product_category_4_list_4_desc);

        String product_category_5_title = resultUtils.getMsg(LanguageCodeConstants.product_category_5_title);
        String product_category_5_list_1_title = resultUtils.getMsg(LanguageCodeConstants.product_category_5_list_1_title);
        String product_category_5_list_1_desc = resultUtils.getMsg(LanguageCodeConstants.product_category_5_list_1_desc);

        String product_category_5_list_2_title = resultUtils.getMsg(LanguageCodeConstants.product_category_5_list_2_title);
        String product_category_5_list_2_desc = resultUtils.getMsg(LanguageCodeConstants.product_category_5_list_2_desc);

        view.addObject("homepage", homepage);
        view.addObject("product_title", product_title);

        view.addObject("product_category_1_title", product_category_1_title);
        view.addObject("product_category_1_list_1_title", product_category_1_list_1_title);
        view.addObject("product_category_1_list_1_desc", product_category_1_list_1_desc);

        view.addObject("product_category_1_list_2_title", product_category_1_list_2_title);
        view.addObject("product_category_1_list_2_desc", product_category_1_list_2_desc);

        view.addObject("product_category_1_list_3_title", product_category_1_list_3_title);
        view.addObject("product_category_1_list_3_desc", product_category_1_list_3_desc);

        view.addObject("product_category_1_list_4_title", product_category_1_list_4_title);
        view.addObject("product_category_1_list_4_desc", product_category_1_list_4_desc);

        view.addObject("product_category_1_list_5_title", product_category_1_list_5_title);
        view.addObject("product_category_1_list_5_desc", product_category_1_list_5_desc);

        view.addObject("product_category_2_title", product_category_2_title);
        view.addObject("product_category_2_list_1_title", product_category_2_list_1_title);
        view.addObject("product_category_2_list_1_desc", product_category_2_list_1_desc);

        view.addObject("product_category_2_list_2_title", product_category_2_list_2_title);
        view.addObject("product_category_2_list_2_desc", product_category_2_list_2_desc);

        view.addObject("product_category_2_list_3_title", product_category_2_list_3_title);
        view.addObject("product_category_2_list_3_desc", product_category_2_list_3_desc);

        view.addObject("product_category_2_list_4_title", product_category_2_list_4_title);
        view.addObject("product_category_2_list_4_desc", product_category_2_list_4_desc);

        view.addObject("product_category_3_title", product_category_3_title);
        view.addObject("product_category_3_list_1_title", product_category_3_list_1_title);
        view.addObject("product_category_3_list_1_desc", product_category_3_list_1_desc);

        view.addObject("product_category_3_list_2_title", product_category_3_list_2_title);
        view.addObject("product_category_3_list_2_desc", product_category_3_list_2_desc);

        view.addObject("product_category_3_list_3_title", product_category_3_list_3_title);
        view.addObject("product_category_3_list_3_desc", product_category_3_list_3_desc);

        view.addObject("product_category_4_title", product_category_4_title);
        view.addObject("product_category_4_list_1_title", product_category_4_list_1_title);
        view.addObject("product_category_4_list_1_desc", product_category_4_list_1_desc);

        view.addObject("product_category_4_list_2_title", product_category_4_list_2_title);
        view.addObject("product_category_4_list_2_desc", product_category_4_list_2_desc);

        view.addObject("product_category_4_list_3_title", product_category_4_list_3_title);
        view.addObject("product_category_4_list_3_desc", product_category_4_list_3_desc);

        view.addObject("product_category_4_list_4_title", product_category_4_list_4_title);
        view.addObject("product_category_4_list_4_desc", product_category_4_list_4_desc);

        view.addObject("product_category_5_title", product_category_5_title);
        view.addObject("product_category_5_list_1_title", product_category_5_list_1_title);
        view.addObject("product_category_5_list_1_desc", product_category_5_list_1_desc);

        view.addObject("product_category_5_list_2_title", product_category_5_list_2_title);
        view.addObject("product_category_5_list_2_desc", product_category_5_list_2_desc);

        return view;
    }


    /**
     * 产品列表
     */
    @GetMapping("products")
    public ModelAndView products(ProductListReq req, HttpServletRequest request, HttpServletResponse response) {
        ModelAndView view = baseView(request, "产品列表", "products");
        view.addObject("keywords", "");
        view.addObject("descriptions", "");
        view.addObject("articleList", null);
        view.addObject("mainclass", Convert.toInt(req.getMainclass()));
        view.addObject("lang", request.getParameter("lang"));
        //获取筛选参数
        List<ProductListWhereModel> productParameters = productConditionsUtil.getProductParameters(Convert.toInt(req.getMainclass()));
        view.addObject("productFunctionParameters", productParameters);
        //获取品牌ID
        int brandId = productConditionsUtil.getBrandId(Convert.toInt(req.getMainclass()));
        //如果没有品牌ID，直接返回，不查询产品
        if (brandId <= 0) {
            Page page = new Page();
            page.setTotal(0);
            view.addObject("product", page);
        }

//        RwPageUtil rwPageUtil = new RwPageUtil();
        //根据产品获取数据
        if (ProductMainClassConstants.air.contains(req.getMainclass())) {
            PAir modelReq = new PAir();
            modelReq.setPageNum(Convert.toInt(req.getPageNo()));
            modelReq.setPageSize(10);
            modelReq.setLanguage(LocaleContextHolder.getLocale().getLanguage());
            modelReq.setMainClass(Convert.toInt(req.getMainclass()));
            modelReq.setBrand(brandId);
            modelReq.setSeries(Convert.toInt(req.getSeries()));
            modelReq.setAirSupply(Convert.toInt(req.getAirSupply()));
            modelReq.setLength(req.getLength());
            modelReq.setHeating(Convert.toInt(req.getHeating()));
            modelReq.setRefrigerating(Convert.toInt(req.getRefrigerating()));
            modelReq.setElectricType(Convert.toInt(req.getElectricType()));
            modelReq.setAirType(Convert.toInt(req.getAirType()));
            modelReq.setCoolingType(Convert.toInt(req.getCoolingType()));
            modelReq.setAccessory(Convert.toInt(req.getAccessory()));
            modelReq.setIsShow(YesAndNoEnum.YES.getCode());
            PageResultUtils<PAir> pageList = airService.page(modelReq);
            view.addObject("pages", rwPageUtil.getRwPageHTML(pageList, request));
            view.addObject("product", pageList.getList());

        }
        else if (ProductMainClassConstants.eps.contains(req.getMainclass())) {
            PEps modelReq = new PEps();
            modelReq.setPageNum(Convert.toInt(req.getPageNo()));
            modelReq.setPageSize(10);
            modelReq.setLanguage(LocaleContextHolder.getLocale().getLanguage());
            modelReq.setMainClass(Convert.toInt(req.getMainclass()));
            modelReq.setBrand(brandId);
            modelReq.setSeries(Convert.toInt(req.getSeries()));
            modelReq.setCapacity(Convert.toInt(req.getCapacity()));
            modelReq.setClassify(Convert.toInt(req.getClassify()));
            PageResultUtils<PEps> pageList = epsService.page(modelReq);
            modelReq.setIsShow(YesAndNoEnum.YES.getCode());
            view.addObject("pages", rwPageUtil.getRwPageHTML(pageList, request));
            view.addObject("product", pageList.getList());
        }
        else if (ProductMainClassConstants.pemonitor.contains(req.getMainclass())) {

            //PPemonitor modelReq = new PPemonitor();
            //modelReq.setPageNum(Convert.toInt(req.getPageNo()));
            //modelReq.setPageSize(10);
            //modelReq.setLanguage(LocaleContextHolder.getLocale().getLanguage());
            //modelReq.setMainClass(Convert.toInt(req.getMainclass()));
            //modelReq.setBrand(brandId);
            //r = pemonitorService.page(modelReq);

        } else if (ProductMainClassConstants.product.contains(req.getMainclass())) {

        } else if (ProductMainClassConstants.room.contains(req.getMainclass())) {
            PRoom modelReq = new PRoom();
            modelReq.setPageNum(Convert.toInt(req.getPageNo()));
            modelReq.setPageSize(10);
            modelReq.setLanguage(LocaleContextHolder.getLocale().getLanguage());
            modelReq.setMainClass(Convert.toInt(req.getMainclass()));
            modelReq.setBrand(brandId);
            modelReq.setSeries(Convert.toInt(req.getSeries()));
            modelReq.setCapacity(Convert.toInt(req.getCapacity()));
            modelReq.setClassify(Convert.toInt(req.getClassify()));
            modelReq.setVoltage(Convert.toInt(req.getVoltage()));
            modelReq.setRoles(Convert.toInt(req.getRoles()));
            modelReq.setIsShow(YesAndNoEnum.YES.getCode());
            PageResultUtils<PRoom> pageList = roomService.page(modelReq);
            view.addObject("pages", rwPageUtil.getRwPageHTML(pageList, request));
            view.addObject("product", pageList.getList());

        }
        else if (ProductMainClassConstants.single.contains(req.getMainclass())) {
            PSingle modelReq = new PSingle();
            modelReq.setPageNum(Convert.toInt(req.getPageNo()));
            modelReq.setPageSize(10);
            modelReq.setLanguage(LocaleContextHolder.getLocale().getLanguage());
            modelReq.setMainClass(Convert.toInt(req.getMainclass()));
            modelReq.setBrand(brandId);
            modelReq.setSeries(Convert.toInt(req.getSeries()));
            modelReq.setCapacity(Convert.toInt(req.getCapacity()));
            modelReq.setPrinciple(Convert.toInt(req.getPrinciple()));
            modelReq.setAccuracy(Convert.toInt(req.getAccuracy()));
            modelReq.setVoltage(Convert.toInt(req.getVoltage()));
            modelReq.setInput(Convert.toInt(req.getInput()));
            modelReq.setIsShow(YesAndNoEnum.YES.getCode());
            PageResultUtils<PSingle> pageList = singleService.page(modelReq);
            view.addObject("pages", rwPageUtil.getRwPageHTML(pageList, request));
            view.addObject("product", pageList.getList());
        }
        else if (ProductMainClassConstants.ups.contains(req.getMainclass())) {
            PUps modelReq = new PUps();
            modelReq.setPageNum(Convert.toInt(req.getPageNo()));
            modelReq.setPageSize(10);
            modelReq.setLanguage(LocaleContextHolder.getLocale().getLanguage());
            modelReq.setMainClass(Convert.toInt(req.getMainclass()));
            modelReq.setBrand(brandId);
            modelReq.setSeries(Convert.toInt(req.getSeries()));
            modelReq.setCapacity(Convert.toInt(req.getCapacity()));
            modelReq.setVoltageSort(Convert.toInt(req.getVoltagesort()));
            modelReq.setVoltage(Convert.toInt(req.getVoltage()));
            modelReq.setBatteryType(Convert.toInt(req.getBatterytype()));
            modelReq.setBatteryForm(Convert.toInt(req.getBatteryform()));
            modelReq.setIsShow(YesAndNoEnum.YES.getCode());
            PageResultUtils<PUps> pageList = upsService.page(modelReq);
            view.addObject("pages", rwPageUtil.getRwPageHTML(pageList, request));
            view.addObject("product", pageList.getList());
        }


        String homepage = resultUtils.getMsg(LanguageCodeConstants.homepage);
        String product_title = resultUtils.getMsg(LanguageCodeConstants.product_title);

        String product_usp_power_supply = resultUtils.getMsg(LanguageCodeConstants.product_usp_power_supply);
        String product_battery = resultUtils.getMsg(LanguageCodeConstants.product_battery);
        String product_outdoor_unit = resultUtils.getMsg(LanguageCodeConstants.product_outdoor_unit);
        String product_indoor_unit = resultUtils.getMsg(LanguageCodeConstants.product_indoor_unit);
        String product_eps_power_supply = resultUtils.getMsg(LanguageCodeConstants.product_eps_power_supply);
        String product_sts = resultUtils.getMsg(LanguageCodeConstants.product_sts);
        String product_pdu = resultUtils.getMsg(LanguageCodeConstants.product_pdu);
        String product_voltage_stabilizing_power_supply = resultUtils.getMsg(LanguageCodeConstants.product_voltage_stabilizing_power_supply);
        String product_inverter = resultUtils.getMsg(LanguageCodeConstants.product_inverter);
        String product_battery_cabinet = resultUtils.getMsg(LanguageCodeConstants.product_battery_cabinet);
        String product_battery_wire = resultUtils.getMsg(LanguageCodeConstants.product_battery_wire);
        String product_model = resultUtils.getMsg(LanguageCodeConstants.product_model);
        String product_series = resultUtils.getMsg(LanguageCodeConstants.product_series);
        String product_capacity = resultUtils.getMsg(LanguageCodeConstants.product_capacity);
        String product_input_and_output_voltage = resultUtils.getMsg(LanguageCodeConstants.product_input_and_output_voltage);
        String product_size = resultUtils.getMsg(LanguageCodeConstants.product_size);
        String product_weight = resultUtils.getMsg(LanguageCodeConstants.product_weight);
        String product_voltage = resultUtils.getMsg(LanguageCodeConstants.product_voltage);
        String product_refrigerating_capacity = resultUtils.getMsg(LanguageCodeConstants.product_refrigerating_capacity);
        String product_heating_capacity = resultUtils.getMsg(LanguageCodeConstants.product_heating_capacity);
        String product_air_conditioning_type = resultUtils.getMsg(LanguageCodeConstants.product_air_conditioning_type);
        String product_cooling_method = resultUtils.getMsg(LanguageCodeConstants.product_cooling_method);
        String product_air_supply_mode = resultUtils.getMsg(LanguageCodeConstants.product_air_supply_mode);
        String product_type = resultUtils.getMsg(LanguageCodeConstants.product_type);
        String product_grade = resultUtils.getMsg(LanguageCodeConstants.product_grade);
        String product_number_of_holes = resultUtils.getMsg(LanguageCodeConstants.product_number_of_holes);
        String product_principle = resultUtils.getMsg(LanguageCodeConstants.product_principle);
        String product_accuracy = resultUtils.getMsg(LanguageCodeConstants.product_accuracy);
        String product_input_voltage = resultUtils.getMsg(LanguageCodeConstants.product_input_voltage);
        String product_output_voltage = resultUtils.getMsg(LanguageCodeConstants.product_output_voltage);
        String product_specification = resultUtils.getMsg(LanguageCodeConstants.product_specification);
        String no_data_available = resultUtils.getMsg(LanguageCodeConstants.no_data_available);
        String reset_filter = resultUtils.getMsg(LanguageCodeConstants.reset_filter);
        String all = resultUtils.getMsg(LanguageCodeConstants.all);
        String product_list_count_tip = resultUtils.getMsg(LanguageCodeConstants.product_list_count_tip);


        view.addObject("homepage", homepage);
        view.addObject("product_title", product_title);
        view.addObject("product_usp_power_supply", product_usp_power_supply);
        view.addObject("product_battery", product_battery);
        view.addObject("product_outdoor_unit", product_outdoor_unit);
        view.addObject("product_indoor_unit", product_indoor_unit);
        view.addObject("product_eps_power_supply", product_eps_power_supply);
        view.addObject("product_sts", product_sts);
        view.addObject("product_pdu", product_pdu);
        view.addObject("product_voltage_stabilizing_power_supply", product_voltage_stabilizing_power_supply);
        view.addObject("product_inverter", product_inverter);
        view.addObject("product_battery_cabinet", product_battery_cabinet);
        view.addObject("product_battery_wire", product_battery_wire);
        view.addObject("product_model", product_model);
        view.addObject("product_series", product_series);
        view.addObject("product_capacity", product_capacity);
        view.addObject("product_input_and_output_voltage", product_input_and_output_voltage);
        view.addObject("product_size", product_size);
        view.addObject("product_weight", product_weight);
        view.addObject("product_voltage", product_voltage);
        view.addObject("product_refrigerating_capacity", product_refrigerating_capacity);
        view.addObject("product_heating_capacity", product_heating_capacity);
        view.addObject("product_air_conditioning_type", product_air_conditioning_type);
        view.addObject("product_cooling_method", product_cooling_method);
        view.addObject("product_air_supply_mode", product_air_supply_mode);
        view.addObject("product_type", product_type);
        view.addObject("product_grade", product_grade);
        view.addObject("product_number_of_holes", product_number_of_holes);
        view.addObject("product_principle", product_principle);
        view.addObject("product_accuracy", product_accuracy);
        view.addObject("product_input_voltage", product_input_voltage);
        view.addObject("product_output_voltage", product_output_voltage);
        view.addObject("product_specification", product_specification);
        view.addObject("no_data_available", no_data_available);
        view.addObject("reset_filter", reset_filter);
        view.addObject("all", all);
        view.addObject("product_list_count_tip", product_list_count_tip);


        return view;
    }


    /**
     * 产品详情
     */
    @GetMapping("productInfo")
    public ModelAndView productInfo(ProductListReq req, HttpServletRequest request, HttpServletResponse response) {

        ModelAndView view = baseView(request, "产品详情", getDetailToViewName(req.getMainclass()));
        view.addObject("keywords", "");
        view.addObject("descriptions", "");
        view.addObject("articleList", null);
        view.addObject("mainclass", Convert.toInt(req.getMainclass()));
        view.addObject("id", Convert.toInt(req.getId()));

        //系列ID
        Integer seriesId=null;


        R r = new R();
        //根据产品获取数据
        if (ProductMainClassConstants.air.contains(req.getMainclass())) {
            PAir modelReq = new PAir();
            modelReq.setLanguage(LocaleContextHolder.getLocale().getLanguage());
            modelReq.setId(Convert.toInt(req.getId()));
            modelReq.setMainClass(Convert.toInt(req.getMainclass()));
            r = airService.getInfoById(modelReq);

            if (r != null && r.getData() != null) {
                PAir airModel = BeanUtil.toBean(r.getData(), PAir.class);
                //查询相同系列
                modelReq = new PAir();
                modelReq.setPageNum(1);
                modelReq.setPageSize(100);
                modelReq.setLanguage(LocaleContextHolder.getLocale().getLanguage());
                modelReq.setMainClass(Convert.toInt(req.getMainclass()));
                modelReq.setSeries(airModel.getSeries());
                modelReq.setIsShow(YesAndNoEnum.YES.getCode());
                PageResultUtils<PAir> pageList = airService.page(modelReq);
                if (pageList != null) {
                    view.addObject("theSameSeries", pageList);
                }


                //记录系列ID，后台查询资料使用
                seriesId=airModel.getSeries();


            }
        }
        else if (ProductMainClassConstants.eps.contains(req.getMainclass())) {
            PEps modelReq = new PEps();
            modelReq.setLanguage(LocaleContextHolder.getLocale().getLanguage());
            modelReq.setId(Convert.toInt(req.getId()));
            modelReq.setMainClass(Convert.toInt(req.getMainclass()));
            r = epsService.getInfoById(modelReq);


            if (r != null && r.getData() != null) {
                PEps epsModel = BeanUtil.toBean(r.getData(), PEps.class);
                //查询相同系列
                modelReq = new PEps();
                modelReq.setPageNum(1);
                modelReq.setPageSize(100);
                modelReq.setLanguage(LocaleContextHolder.getLocale().getLanguage());
                modelReq.setMainClass(Convert.toInt(req.getMainclass()));
                modelReq.setSeries(epsModel.getSeries());
                modelReq.setIsShow(YesAndNoEnum.YES.getCode());
                PageResultUtils<PEps> pageList = epsService.page(modelReq);
                if (pageList != null) {
                    view.addObject("theSameSeries", pageList);
                }



                //记录系列ID，后台查询资料使用
                seriesId=epsModel.getSeries();

            }

        }
        else if (ProductMainClassConstants.pemonitor.contains(req.getMainclass())) {

            //PPemonitor modelReq = new PPemonitor();
            //modelReq.setId(Convert.toInt(req.getId()));
            //modelReq.setLanguage(LocaleContextHolder.getLocale().getLanguage());
            //modelReq.setMainClass(Convert.toInt(req.getMainclass()));
            //r = pemonitorService.getInfoById(modelReq);

        } else if (ProductMainClassConstants.product.contains(req.getMainclass())) {



        } else if (ProductMainClassConstants.room.contains(req.getMainclass())) {
            PRoom modelReq = new PRoom();
            modelReq.setLanguage(LocaleContextHolder.getLocale().getLanguage());
            modelReq.setId(Convert.toInt(req.getId()));
            modelReq.setMainClass(Convert.toInt(req.getMainclass()));
            r = roomService.getInfoById(modelReq);

            if (r != null && r.getData() != null) {
                PRoom roomModel = BeanUtil.toBean(r.getData(), PRoom.class);
                //查询相同系列
                modelReq = new PRoom();
                modelReq.setPageNum(1);
                modelReq.setPageSize(100);
                modelReq.setLanguage(LocaleContextHolder.getLocale().getLanguage());
                modelReq.setMainClass(Convert.toInt(req.getMainclass()));
                modelReq.setSeries(roomModel.getSeries());
                modelReq.setIsShow(YesAndNoEnum.YES.getCode());
                PageResultUtils<PRoom> pageList = roomService.page(modelReq);

                if (pageList != null) {
                    view.addObject("theSameSeries", pageList);
                }




                //记录系列ID，后台查询资料使用
                seriesId=roomModel.getSeries();
            }


        }
        else if (ProductMainClassConstants.single.contains(req.getMainclass())) {
            //查询详情
            PSingle modelReq = new PSingle();
            modelReq.setLanguage(LocaleContextHolder.getLocale().getLanguage());
            modelReq.setId(Convert.toInt(req.getId()));
            modelReq.setMainClass(Convert.toInt(req.getMainclass()));
            r = singleService.getInfoById(modelReq);


            if (r != null && r.getData() != null) {
                PSingle singleModel = BeanUtil.toBean(r.getData(), PSingle.class);
                //查询相同系列
                modelReq = new PSingle();
                modelReq.setPageNum(1);
                modelReq.setPageSize(100);
                modelReq.setLanguage(LocaleContextHolder.getLocale().getLanguage());
                modelReq.setMainClass(Convert.toInt(req.getMainclass()));
                modelReq.setSeries(singleModel.getSeries());
                modelReq.setIsShow(YesAndNoEnum.YES.getCode());
                PageResultUtils<PSingle> pageList = singleService.page(modelReq);
                if (pageList != null) {
                    view.addObject("theSameSeries", pageList);
                }


                //记录系列ID，后台查询资料使用
                seriesId=singleModel.getSeries();
            }


        }
        else if (ProductMainClassConstants.ups.contains(req.getMainclass())) {

            //查询详情
            PUps modelReq = new PUps();
            modelReq.setLanguage(LocaleContextHolder.getLocale().getLanguage());
            modelReq.setId(Convert.toInt(req.getId()));
            modelReq.setMainClass(Convert.toInt(req.getMainclass()));
            r = upsService.getInfoById(modelReq);


            if (r != null && r.getData() != null) {
                PUps upsModel = BeanUtil.toBean(r.getData(), PUps.class);
                //查询相同系列
                modelReq = new PUps();
                modelReq.setPageNum(1);
                modelReq.setPageSize(100);
                modelReq.setLanguage(LocaleContextHolder.getLocale().getLanguage());
                modelReq.setMainClass(Convert.toInt(req.getMainclass()));
                modelReq.setSeries(upsModel.getSeries());
                modelReq.setIsShow(YesAndNoEnum.YES.getCode());
                PageResultUtils<PUps> pageList = upsService.page(modelReq);

                if (pageList != null) {
                    view.addObject("theSameSeries", pageList);
                }




                //记录系列ID，后台查询资料使用
                seriesId=upsModel.getSeries();
            }

        }


        //产品详情
        view.addObject("product", r.getData());


        //方案应用案例
        if (r.getData() != null) {
            BoxList10ByProductIdReq detail = new BoxList10ByProductIdReq();
            detail.setMainClass(Convert.toInt(req.getMainclass()));
            detail.setLanguage(LocaleContextHolder.getLocale().getLanguage());
            detail.setProductId(Convert.toInt(req.getId()));
            detail.setUserId(Convert.toStr(authorConfig.getAuthorId()));
            R list10ByProductId = boxlistService.getBoxList10ByProductId(detail);
            if (list10ByProductId != null && list10ByProductId.getData() != null) {
                view.addObject("schemeList", list10ByProductId.getData());
            }
        }

        //查询资料
        if (seriesId!=null && seriesId>0) {
            //资料
            PSeriesAttachment attachmentReq = new PSeriesAttachment();
            attachmentReq.setLanguage(LocaleContextHolder.getLocale().getLanguage());
            attachmentReq.setSeries(seriesId);
            R attachmentList = seriesAttachmentService.getListBySeries(attachmentReq);
            if (attachmentList != null && attachmentList.getData() != null) {
                view.addObject("attachmentList", attachmentList.getData());
            }
        }





        String homepage = resultUtils.getMsg(LanguageCodeConstants.homepage);
        String product_title = resultUtils.getMsg(LanguageCodeConstants.product_title);

        String product_usp_power_supply = resultUtils.getMsg(LanguageCodeConstants.product_usp_power_supply);
        String product_battery = resultUtils.getMsg(LanguageCodeConstants.product_battery);
        String product_outdoor_unit = resultUtils.getMsg(LanguageCodeConstants.product_outdoor_unit);
        String product_indoor_unit = resultUtils.getMsg(LanguageCodeConstants.product_indoor_unit);
        String product_eps_power_supply = resultUtils.getMsg(LanguageCodeConstants.product_eps_power_supply);
        String product_sts = resultUtils.getMsg(LanguageCodeConstants.product_sts);
        String product_pdu = resultUtils.getMsg(LanguageCodeConstants.product_pdu);
        String product_voltage_stabilizing_power_supply = resultUtils.getMsg(LanguageCodeConstants.product_voltage_stabilizing_power_supply);
        String product_inverter = resultUtils.getMsg(LanguageCodeConstants.product_inverter);
        String product_battery_cabinet = resultUtils.getMsg(LanguageCodeConstants.product_battery_cabinet);
        String product_battery_wire = resultUtils.getMsg(LanguageCodeConstants.product_battery_wire);
        String product_series_other_model = resultUtils.getMsg(LanguageCodeConstants.product_series_other_model);
        String product_application_examples = resultUtils.getMsg(LanguageCodeConstants.product_application_examples);
        String document_download = resultUtils.getMsg(LanguageCodeConstants.document_download);


        String brand = resultUtils.getMsg(LanguageCodeConstants.brand);
        String series = resultUtils.getMsg(LanguageCodeConstants.series);
        String capacity = resultUtils.getMsg(LanguageCodeConstants.product_battery_wire);
        String voltagesort = resultUtils.getMsg(LanguageCodeConstants.voltagesort);
        String version = resultUtils.getMsg(LanguageCodeConstants.version);
        String batterytype = resultUtils.getMsg(LanguageCodeConstants.batterytype);
        String batteryform = resultUtils.getMsg(LanguageCodeConstants.batteryform);
        String voltage = resultUtils.getMsg(LanguageCodeConstants.voltage);


        view.addObject("homepage", homepage);
        view.addObject("product_title", product_title);
        view.addObject("product_usp_power_supply", product_usp_power_supply);
        view.addObject("product_battery", product_battery);
        view.addObject("product_outdoor_unit", product_outdoor_unit);
        view.addObject("product_indoor_unit", product_indoor_unit);
        view.addObject("product_eps_power_supply", product_eps_power_supply);
        view.addObject("product_sts", product_sts);
        view.addObject("product_pdu", product_pdu);
        view.addObject("product_voltage_stabilizing_power_supply", product_voltage_stabilizing_power_supply);
        view.addObject("product_inverter", product_inverter);
        view.addObject("product_battery_cabinet", product_battery_cabinet);
        view.addObject("product_battery_wire", product_battery_wire);
        view.addObject("product_series_other_model", product_series_other_model);
        view.addObject("product_application_examples", product_application_examples);
        view.addObject("document_download", document_download);
        view.addObject("brand", brand);
        view.addObject("series", series);
        view.addObject("capacity", capacity);
        view.addObject("voltagesort", voltagesort);
        view.addObject("version", version);
        view.addObject("batterytype", batterytype);
        view.addObject("batteryform", batteryform);
        view.addObject("voltage", voltage);



        String download_table_file_name = resultUtils.getMsg(LanguageCodeConstants.download_table_file_name);
        String download_table_file_size = resultUtils.getMsg(LanguageCodeConstants.download_table_file_size);
        String download_table_operation = resultUtils.getMsg(LanguageCodeConstants.download_table_operation);
        String download_btn = resultUtils.getMsg(LanguageCodeConstants.download_btn);

        view.addObject("download_table_file_name", download_table_file_name);
        view.addObject("download_table_file_size", download_table_file_size);
        view.addObject("download_table_operation", download_table_operation);
        view.addObject("download_btn", download_btn);


        String electrical_parameters = resultUtils.getMsg(LanguageCodeConstants.electrical_parameters);
        String physical_parameters = resultUtils.getMsg(LanguageCodeConstants.physical_parameters);
        String detailed_parameters = resultUtils.getMsg(LanguageCodeConstants.detailed_parameters);
        String application_scheme = resultUtils.getMsg(LanguageCodeConstants.application_scheme);
        String product_overview = resultUtils.getMsg(LanguageCodeConstants.product_overview);

        view.addObject("electrical_parameters", electrical_parameters);
        view.addObject("physical_parameters", physical_parameters);
        view.addObject("detailed_parameters", detailed_parameters);
        view.addObject("application_scheme", application_scheme);
        view.addObject("product_overview", product_overview);





        String product_detail_net_weight = resultUtils.getMsg(LanguageCodeConstants.product_detail_net_weight);
        String product_detail_gross_weight = resultUtils.getMsg(LanguageCodeConstants.product_detail_gross_weight);
        String product_detail_size = resultUtils.getMsg(LanguageCodeConstants.product_detail_size);
        String product_detail_product_capacity = resultUtils.getMsg(LanguageCodeConstants.product_detail_product_capacity);
        String product_detail_type = resultUtils.getMsg(LanguageCodeConstants.product_detail_type);
        String product_detail_input_and_output = resultUtils.getMsg(LanguageCodeConstants.product_detail_input_and_output);
        String product_detail_direct_current_voltage = resultUtils.getMsg(LanguageCodeConstants.product_detail_direct_current_voltage);
        String product_detail_product_power = resultUtils.getMsg(LanguageCodeConstants.product_detail_product_power);
        String product_detail_principle_classification = resultUtils.getMsg(LanguageCodeConstants.product_detail_principle_classification);
        String product_detail_battery_form = resultUtils.getMsg(LanguageCodeConstants.product_detail_battery_form);
        String product_detail_voltage = resultUtils.getMsg(LanguageCodeConstants.product_detail_voltage);
        String product_detail_input_voltage = resultUtils.getMsg(LanguageCodeConstants.product_detail_input_voltage);
        String product_detail_output_voltage = resultUtils.getMsg(LanguageCodeConstants.product_detail_output_voltage);
        String product_detail_principle = resultUtils.getMsg(LanguageCodeConstants.product_detail_principle);
        String product_detail_precision = resultUtils.getMsg(LanguageCodeConstants.product_detail_precision);
        String product_detail_series = resultUtils.getMsg(LanguageCodeConstants.product_detail_series);
        String product_detail_number_of_holes = resultUtils.getMsg(LanguageCodeConstants.product_detail_number_of_holes);
        String product_detail_cooling_capacity = resultUtils.getMsg(LanguageCodeConstants.product_detail_cooling_capacity);
        String product_detail_heating_capacity = resultUtils.getMsg(LanguageCodeConstants.product_detail_heating_capacity);
        String product_detail_air_conditioning_type = resultUtils.getMsg(LanguageCodeConstants.product_detail_air_conditioning_type);
        String product_detail_air_supply_mode = resultUtils.getMsg(LanguageCodeConstants.product_detail_air_supply_mode);
        String product_detail_cooling_type = resultUtils.getMsg(LanguageCodeConstants.product_detail_cooling_type);
        String product_detail_power_consumption_type = resultUtils.getMsg(LanguageCodeConstants.product_detail_power_consumption_type);
        String product_detail_structure = resultUtils.getMsg(LanguageCodeConstants.product_detail_structure);

        view.addObject("product_detail_net_weight", product_detail_net_weight);
        view.addObject("product_detail_gross_weight", product_detail_gross_weight);
        view.addObject("product_detail_size", product_detail_size);
        view.addObject("product_detail_product_capacity", product_detail_product_capacity);
        view.addObject("product_detail_type", product_detail_type);
        view.addObject("product_detail_input_and_output", product_detail_input_and_output);
        view.addObject("product_detail_direct_current_voltage", product_detail_direct_current_voltage);
        view.addObject("product_detail_product_power", product_detail_product_power);
        view.addObject("product_detail_principle_classification", product_detail_principle_classification);
        view.addObject("product_detail_battery_form", product_detail_battery_form);
        view.addObject("product_detail_voltage", product_detail_voltage);
        view.addObject("product_detail_input_voltage", product_detail_input_voltage);
        view.addObject("product_detail_output_voltage", product_detail_output_voltage);
        view.addObject("product_detail_principle", product_detail_principle);
        view.addObject("product_detail_precision", product_detail_precision);
        view.addObject("product_detail_series", product_detail_series);
        view.addObject("product_detail_number_of_holes", product_detail_number_of_holes);
        view.addObject("product_detail_cooling_capacity", product_detail_cooling_capacity);
        view.addObject("product_detail_heating_capacity", product_detail_heating_capacity);
        view.addObject("product_detail_air_conditioning_type", product_detail_air_conditioning_type);
        view.addObject("product_detail_air_supply_mode", product_detail_air_supply_mode);
        view.addObject("product_detail_cooling_type", product_detail_cooling_type);
        view.addObject("product_detail_power_consumption_type", product_detail_power_consumption_type);
        view.addObject("product_detail_structure", product_detail_structure);



        return view;
    }



    //产品详情跳转页面名称
    private String getDetailToViewName(String mainClass){

        String toViewName = "";

        if (ProductMainClassConstants.air.contains(mainClass)) {
            toViewName = "airDetail";
        }
        else if (ProductMainClassConstants.eps.contains(mainClass)) {
            toViewName = "epsDetail";
        }
        else if (ProductMainClassConstants.pemonitor.contains(mainClass)) {
            toViewName = "pemonitorDetail";
        }
        else if (ProductMainClassConstants.product.contains(mainClass)) {
            toViewName = "productDetail";
        }
        else if (ProductMainClassConstants.room.contains(mainClass)) {
            toViewName = "roomDetail";
        }
        else if (ProductMainClassConstants.single.contains(mainClass)) {
            toViewName = "singleDetail";
        }
        else if (ProductMainClassConstants.ups.contains(mainClass)) {
            toViewName = "upsDetail";
        }

        return toViewName;
    }

}
