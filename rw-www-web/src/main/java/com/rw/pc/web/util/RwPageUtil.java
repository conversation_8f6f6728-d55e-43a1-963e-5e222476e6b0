package com.rw.pc.web.util;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rw.common.core.utils.PageResultUtils;
import lombok.Data;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;

@Component
public class RwPageUtil {
    @Autowired
    RequestUtil requestUtil;

    @Data
    private class PageModel {
        private int startPage;
        private int endPage;
        private int displayNum = 3;
    }

    public String getRwPageHTML(PageResultUtils pageResultUtils, HttpServletRequest request) {
        Page page = new Page();
        page.setTotal(pageResultUtils.getTotalCount());
        page.setSize(pageResultUtils.getPageSize());
        page.setPages(pageResultUtils.getTotalPage());
        page.setCurrent(pageResultUtils.getCurrPage());
        return getRwPageHTML(page, request);
    }

    public String getRwPageHTML(Page page, HttpServletRequest request) {
        int total = Convert.toInt(page.getTotal());
        if (total <= 0) {
            return "";
        }
        int pageSize = Convert.toInt(page.getSize());
        int totalPage = (total + pageSize - 1) / pageSize;
        int current = Convert.toInt(page.getCurrent());
        StringBuffer displayInfo = new StringBuffer();
        // 判断如果当前是第一页 则“首页”和“第一页”失去链接
        if (current >= 3 && totalPage>3) {
            displayInfo.append("<li><a href=\"" + convertListUrl(request, 1) + "\">1</a></li>");
            displayInfo.append("<li style=\"pointer-events:none\" class=\"disabled\"><a href=\"javascript:;\">···</a></li>");
        }
        PageModel pageModel = new PageModel();
        countPages(current, totalPage, pageModel);
        for (int i = pageModel.getStartPage(); i <= pageModel.getEndPage(); i++) {
            if (i == current) {
                displayInfo.append("<li style=\"pointer-events:none\" class=\"active\"><a href=\"javascript:void(0)\" style=\"pointer-events:none\"><span>" + i + "</span></a></li>");
            } else {
                displayInfo.append("<li><a href=\"" + convertListUrl(request, (i)) + "\"><span>" + i + "</span></a></li>");
            }
        }
        if (current + 3 <= totalPage) {
            displayInfo.append("<li style=\"pointer-events:none\" class=\"disabled\"><a href=\"javascript:;\">···</a></li>");
        }
        if (current != totalPage && current - pageModel.getDisplayNum() < totalPage && current + 1 != totalPage && totalPage>3) {
            displayInfo.append("<li><a href=\"" + convertListUrl(request, totalPage) + "\">" + totalPage + "</a></li>");
        }

        return displayInfo.toString();
    }

    public String convertListUrl(HttpServletRequest request, int current) {
        String uri = request.getRequestURI().toLowerCase();
        if (uri.equals("/news/listcase") || uri.equals("/news/listnews")) {
            return caseListUrl(request, current);
        } else if (uri.equals("/product/products") || uri.equals("/program/programlist")) {
            return productUrl(request, current);
        }else if (uri.equals("/down/down")) {
            return downloadListUrl(request, current);
        }
        return "";
    }



    private String productUrl(HttpServletRequest request, int current) {
        String uri = request.getRequestURI();
        String lang = request.getParameter("lang");
        String parameterMapUrl = requestUtil.getParameterMapUrl(request, current);
        if (StrUtil.isEmpty(lang)) {
            return StrUtil.concat(false, uri + "?" + parameterMapUrl);
        }
        return StrUtil.concat(false, "/" + lang + uri + "?" + parameterMapUrl);
    }

    //成功案例的地址
    private String caseListUrl(HttpServletRequest request, int current) {
        String uri = request.getRequestURI();
        String lang = request.getParameter("lang");
        if (StrUtil.isEmpty(lang)) {
            return StrUtil.concat(false, uri, "/" + current + ".html");
        }
        return StrUtil.concat(false, "/" + lang + uri, "/" + current + ".html");
    }

    //下载中心
    private String downloadListUrl(HttpServletRequest request, int current) {
        String uri = request.getRequestURI();
        String lang = request.getParameter("lang");
        String parameterMapUrl = requestUtil.getParameterMapUrl(request, current);
        if (StrUtil.isEmpty(lang)) {
            return StrUtil.concat(false, uri + "?" + parameterMapUrl);
        }
        return StrUtil.concat(false, "/" + lang + uri + "?" + parameterMapUrl);
    }

    /**
     * 计算起始页和结束页
     */
    private void countPages(int current, int totalPage, PageModel pageModel) {
        if (current - pageModel.getDisplayNum() / 2 < 1) {
            pageModel.setStartPage(1);
            pageModel.setEndPage(pageModel.getDisplayNum() > totalPage ? totalPage : pageModel.getDisplayNum());
        } else if (current + pageModel.getDisplayNum() / 2 > totalPage) {
            int n = totalPage - pageModel.getDisplayNum() + 1;
            pageModel.setStartPage(n > 0 ? n : 1);
            pageModel.setEndPage(totalPage);
        } else {
            pageModel.setStartPage(current - pageModel.getDisplayNum() / 2);
            pageModel.setEndPage(pageModel.getStartPage() + pageModel.getDisplayNum() - 1);
        }
    }
}
