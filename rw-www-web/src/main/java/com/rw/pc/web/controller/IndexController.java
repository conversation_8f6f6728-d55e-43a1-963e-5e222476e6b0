package com.rw.pc.web.controller;


import com.rw.common.core.domain.R;
import com.rw.common.core.utils.ResultUtils;
import com.rw.common.model.entity.NewsCenter;
import com.rw.common.model.entity.Seo;
import com.rw.common.model.enums.ArticleTypeEnum;
import com.rw.common.model.enums.SeoPositionEnum;
import com.rw.common.service.INewsCenterService;
import com.rw.common.service.ISeoService;
import com.rw.pc.web.constant.LanguageCodeConstants;
import lombok.AllArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 用户信息
 *
 * <AUTHOR>
 */
@AllArgsConstructor
@Controller
@RequestMapping("/")
public class IndexController extends BaseController {



    @Autowired
    ResultUtils resultUtils;

    @Autowired
    private INewsCenterService iNewsCenterService;

    @Autowired
    private ISeoService seoService;


    /**
     * 首页
     */

    @GetMapping("")
    public ModelAndView home(HttpServletRequest request, HttpServletResponse response) {
        ModelAndView view = baseView(request, "首页", "index");
        view.addObject("keywords", "");
        view.addObject("descriptions", "");
        view.addObject("articleList", null);


        String index_slides_1_label=resultUtils.getMsg(LanguageCodeConstants.index_slides_1_label);
        String index_slides_1_title=resultUtils.getMsg(LanguageCodeConstants.index_slides_1_title);
        String index_slides_1_desc=resultUtils.getMsg(LanguageCodeConstants.index_slides_1_desc);

        String index_slides_2_label=resultUtils.getMsg(LanguageCodeConstants.index_slides_2_label);
        String index_slides_2_title=resultUtils.getMsg(LanguageCodeConstants.index_slides_2_title);
        String index_slides_2_desc=resultUtils.getMsg(LanguageCodeConstants.index_slides_2_desc);

        String index_category_title=resultUtils.getMsg(LanguageCodeConstants.index_category_title);
        String index_category_1_title=resultUtils.getMsg(LanguageCodeConstants.index_category_1_title);
        String index_category_1_desc=resultUtils.getMsg(LanguageCodeConstants.index_category_1_desc);

        String index_category_2_title=resultUtils.getMsg(LanguageCodeConstants.index_category_2_title);
        String index_category_2_desc=resultUtils.getMsg(LanguageCodeConstants.index_category_2_desc);

        String index_category_3_title=resultUtils.getMsg(LanguageCodeConstants.index_category_3_title);
        String index_category_3_desc=resultUtils.getMsg(LanguageCodeConstants.index_category_3_desc);

        String index_category_4_title=resultUtils.getMsg(LanguageCodeConstants.index_category_4_title);
        String index_category_4_desc=resultUtils.getMsg(LanguageCodeConstants.index_category_4_desc);

        String index_category_5_title=resultUtils.getMsg(LanguageCodeConstants.index_category_5_title);
        String index_category_5_desc=resultUtils.getMsg(LanguageCodeConstants.index_category_5_desc);

        String index_scheme_title=resultUtils.getMsg(LanguageCodeConstants.index_scheme_title);
        String index_scheme_1_title=resultUtils.getMsg(LanguageCodeConstants.index_scheme_1_title);
        String index_scheme_2_title=resultUtils.getMsg(LanguageCodeConstants.index_scheme_2_title);
        String view_more=resultUtils.getMsg(LanguageCodeConstants.view_more);
        String view_the_list=resultUtils.getMsg(LanguageCodeConstants.view_the_list);



        String index_company_title=resultUtils.getMsg(LanguageCodeConstants.index_company_title);
        String index_company_desc=resultUtils.getMsg(LanguageCodeConstants.index_company_desc);

        String index_company_news=resultUtils.getMsg(LanguageCodeConstants.index_company_news);
        String index_scheme_list_title=resultUtils.getMsg(LanguageCodeConstants.index_scheme_list_title);
        String index_scheme_list_capacity=resultUtils.getMsg(LanguageCodeConstants.index_scheme_list_capacity);
        String index_scheme_list_capacity_value=resultUtils.getMsg(LanguageCodeConstants.index_scheme_list_capacity_value);
        String index_scheme_list_battery=resultUtils.getMsg(LanguageCodeConstants.index_scheme_list_battery);
        String index_scheme_list_battery_value=resultUtils.getMsg(LanguageCodeConstants.index_scheme_list_battery_value);
        String index_scheme_list_battery_cabinet=resultUtils.getMsg(LanguageCodeConstants.index_scheme_list_battery_cabinet);
        String index_scheme_list_battery_cabinet_value=resultUtils.getMsg(LanguageCodeConstants.index_scheme_list_battery_cabinet_value);






        view.addObject("index_slides_1_label",index_slides_1_label);
        view.addObject("index_slides_1_title",index_slides_1_title);
        view.addObject("index_slides_1_desc",index_slides_1_desc);

        view.addObject("index_slides_2_label",index_slides_2_label);
        view.addObject("index_slides_2_title",index_slides_2_title);
        view.addObject("index_slides_2_desc",index_slides_2_desc);

        view.addObject("index_category_title",index_category_title);
        view.addObject("index_category_1_title",index_category_1_title);
        view.addObject("index_category_1_desc",index_category_1_desc);

        view.addObject("index_category_2_title",index_category_2_title);
        view.addObject("index_category_2_desc",index_category_2_desc);

        view.addObject("index_category_3_title",index_category_3_title);
        view.addObject("index_category_3_desc",index_category_3_desc);

        view.addObject("index_category_4_title",index_category_4_title);
        view.addObject("index_category_4_desc",index_category_4_desc);

        view.addObject("index_category_5_title",index_category_5_title);
        view.addObject("index_category_5_desc",index_category_5_desc);

        view.addObject("index_scheme_title",index_scheme_title);
        view.addObject("index_scheme_1_title",index_scheme_1_title);
        view.addObject("index_scheme_2_title",index_scheme_2_title);
        view.addObject("view_more",view_more);
        view.addObject("view_the_list",view_the_list);

        view.addObject("index_company_title",index_company_title);
        view.addObject("index_company_desc",index_company_desc);

        view.addObject("index_company_news",index_company_news);
        view.addObject("index_scheme_list_title",index_scheme_list_title);
        view.addObject("index_scheme_list_capacity",index_scheme_list_capacity);
        view.addObject("index_scheme_list_capacity_value",index_scheme_list_capacity_value);
        view.addObject("index_scheme_list_battery",index_scheme_list_battery);
        view.addObject("index_scheme_list_battery_value",index_scheme_list_battery_value);
        view.addObject("index_scheme_list_battery_cabinet",index_scheme_list_battery_cabinet);
        view.addObject("index_scheme_list_battery_cabinet_value",index_scheme_list_battery_cabinet_value);


        //新闻
        NewsCenter newsCenter = new NewsCenter();
        newsCenter.setLanguage(LocaleContextHolder.getLocale().getLanguage());
        newsCenter.setArticleType(ArticleTypeEnum.OFFICIAL_WEBSITE_NEWS.getId());
        List<NewsCenter> list = iNewsCenterService.last10(newsCenter);
        view.addObject("newsList",list);

        return view;
    }



    @GetMapping("en/index")
    public ModelAndView en(HttpServletRequest request, HttpServletResponse response) {
        Seo seo = new Seo();
        seo.setPosition(SeoPositionEnum.HOME.getCode());
        seo.setLanguage(LocaleContextHolder.getLocale().getLanguage());
        Seo seoDataByPosition = seoService.getSeoDataByPosition(seo);
        ModelAndView view = baseView(request, "首页", "index");
        view.addObject("keywords", "");
        view.addObject("descriptions", "");
        view.addObject("articleList", null);
        String msg = resultUtils.getMsg(LanguageCodeConstants.SYSTEM_TYPE_ILLEGAL);
        view.addObject("msg", msg);
        return view;
    }


}
