package com.rw.pc.web.controller;


import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rw.common.core.domain.R;
import com.rw.common.core.utils.PageResultUtils;
import com.rw.common.core.utils.ResultUtils;
import com.rw.common.model.entity.NewsCenter;
import com.rw.common.model.enums.ArticleTypeEnum;
import com.rw.common.service.INewsCenterService;
import com.rw.pc.web.config.AuthorConfig;
import com.rw.pc.web.constant.LanguageCodeConstants;
import com.rw.pc.web.util.RwPageUtil;
import com.rw.pc.web.util.UrlUtil;
import lombok.AllArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 用户信息
 *
 * <AUTHOR>
 */
@AllArgsConstructor
@Controller
@RequestMapping("/news")
public class NewsController extends BaseController {

    @Autowired
    ResultUtils resultUtils;

    @Autowired
    private INewsCenterService iNewsCenterService;

    @Autowired
    private AuthorConfig authorConfig;
    @Autowired
    RwPageUtil rwPageUtil;
    @Autowired
    UrlUtil urlUtil;

    /**
     * 成功案例
     */
    @GetMapping("listCase")
    public ModelAndView listCase(HttpServletRequest request, HttpServletResponse response) {
        Integer pageNo = Convert.toInt(request.getParameter("pageNo"), 1);
        ModelAndView view = baseView(request, "成功案例", "listCase");
        view.addObject("keywords", "");
        view.addObject("descriptions", "");


        String homepage = resultUtils.getMsg(LanguageCodeConstants.homepage);
        String success_case = resultUtils.getMsg(LanguageCodeConstants.success_case);
        view.addObject("homepage", homepage);
        view.addObject("success_case", success_case);


        //成功案例
        NewsCenter newsCenter = new NewsCenter();
        newsCenter.setPageNum(pageNo);
        newsCenter.setPageSize(10);
//        newsCenter.setPageSize(1);
        newsCenter.setLanguage(LocaleContextHolder.getLocale().getLanguage());
        newsCenter.setArticleType(ArticleTypeEnum.SUCCESS_STORIES.getId());
        //newsCenter.setAuthorClassify(authorConfig.getAuthorClassify());
        //newsCenter.setAuthorId(authorConfig.getAuthorId());

        String language = request.getParameter("lang");
        PageResultUtils<NewsCenter> pageNewsCenter = iNewsCenterService.page(newsCenter);
        initCaseListData(pageNewsCenter, language, view, ArticleTypeEnum.SUCCESS_STORIES.getId());
//        RwPageUtil rwPageUtil = new RwPageUtil();

        Page page = new Page();
        page.setTotal(pageNewsCenter.getTotalCount());
        page.setSize(pageNewsCenter.getPageSize());
        page.setPages(pageNewsCenter.getTotalPage());
        page.setCurrent(pageNewsCenter.getCurrPage());
        view.addObject("page", rwPageUtil.getRwPageHTML(page, request));
        return view;
    }

    //列表页数据
    private void initCaseListData(PageResultUtils<NewsCenter> page, String language, ModelAndView view, Integer articleType) {
        List<NewsCenter> list = page.getList();
        StringBuilder sb = new StringBuilder();
        for (NewsCenter center : list) {
            String url = "/news/caseInfo/" + center.getId() + ".html";
            if (articleType.intValue() == ArticleTypeEnum.OFFICIAL_WEBSITE_NEWS.getId()) {
                url = "/news/newsInfo/" + center.getId() + ".html";
            }
            sb.append(" <dl><dd><i class=\"fa fa-calendar\"></i></dd><dd>");
            sb.append("<a href=\"" + urlUtil.getJumpUrl(url, language) + "\" title=\"" + center.getTitle() + "\" >" + center.getTitle() + "</a>");
            sb.append("</dd>");
            sb.append("<span>" + DateUtil.format(center.getCreateTime(), "yyyy-MM-dd") + "</span>");
            sb.append("</dl>");
        }
        view.addObject("newsStr", sb.toString());
    }

    /**
     * 成功案例详情
     */
    @GetMapping("caseInfo")
    public ModelAndView caseInfo(@RequestParam String id, HttpServletRequest request, HttpServletResponse response) {
        NewsCenter newsCenter = new NewsCenter();
        newsCenter.setLanguage(LocaleContextHolder.getLocale().getLanguage());
        newsCenter.setId(Convert.toInt(id));
        newsCenter.setArticleType(ArticleTypeEnum.SUCCESS_STORIES.getId());
        //newsCenter.setAuthorClassify(authorConfig.getAuthorClassify());
        //newsCenter.setAuthorId(authorConfig.getAuthorId());
        NewsCenter newsCenterResult = iNewsCenterService.getInfoById(newsCenter);

        ModelAndView view = baseView(request, newsCenterResult.getTitle(), "newsInfo");
        view.addObject("keywords", "");
        view.addObject("descriptions", "");

        //成功案例

        view.addObject("news", newsCenterResult);
        return view;
    }

    /**
     * 新闻中心
     */
    @GetMapping("listNews")
    public ModelAndView listNews(HttpServletRequest request, HttpServletResponse response) {
        Integer pageNo = Convert.toInt(request.getParameter("pageNo"), 1);
        ModelAndView view = baseView(request, "成功案例", "listNews");
        view.addObject("keywords", "");
        view.addObject("descriptions", "");
        String homepage = resultUtils.getMsg(LanguageCodeConstants.homepage);
        String news_and_information = resultUtils.getMsg(LanguageCodeConstants.news_and_information);
        view.addObject("homepage", homepage);
        view.addObject("news_and_information", news_and_information);


        //成功案例
        NewsCenter newsCenter = new NewsCenter();
        newsCenter.setPageNum(pageNo);
        newsCenter.setPageSize(10);
        newsCenter.setLanguage(LocaleContextHolder.getLocale().getLanguage());
        newsCenter.setArticleType(ArticleTypeEnum.OFFICIAL_WEBSITE_NEWS.getId());


        String language = request.getParameter("lang");
        PageResultUtils<NewsCenter> pageNewsCenter = iNewsCenterService.page(newsCenter);
        initCaseListData(pageNewsCenter, language, view, ArticleTypeEnum.OFFICIAL_WEBSITE_NEWS.getId());
//        RwPageUtil rwPageUtil = new RwPageUtil();

        view.addObject("page", rwPageUtil.getRwPageHTML(pageNewsCenter, request));
        return view;
    }


    /**
     * 新闻详情
     */
    @GetMapping("newsInfo")
    public ModelAndView newsInfo(@RequestParam String id, HttpServletRequest request, HttpServletResponse response) {
        //新闻
        NewsCenter newsCenter = new NewsCenter();
        newsCenter.setLanguage(LocaleContextHolder.getLocale().getLanguage());
        newsCenter.setId(Convert.toInt(id));
        newsCenter.setArticleType(ArticleTypeEnum.OFFICIAL_WEBSITE_NEWS.getId());
        NewsCenter infoById = iNewsCenterService.getInfoById(newsCenter);
        ModelAndView view = baseView(request, infoById.getTitle(), "newsInfo");
        view.addObject("keywords", "");
        view.addObject("descriptions", "");
        view.addObject("news", infoById);
        return view;
    }

}
