package com.rw.pc.web.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.useragent.UserAgent;
import cn.hutool.http.useragent.UserAgentUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.rw.common.core.constant.Constants;
import com.rw.common.core.domain.R;
import com.rw.common.core.text.Convert;
import com.rw.common.core.utils.ResultUtils;
import com.rw.common.core.web.domain.AjaxResult;
import com.rw.common.core.web.page.TableDataInfo;
import com.rw.common.model.entity.NewsCenter;
import com.rw.common.model.entity.OfficialWebsiteNavigation;
import com.rw.common.service.INewsCenterService;
import com.rw.common.service.IOfficialWebsiteNavigationService;
import com.rw.pc.web.config.Configs;
import com.rw.pc.web.constant.LanguageCodeConstants;
import com.rw.pc.web.util.UrlUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Locale;

public class BaseController {
    @Autowired
    Configs config;

    @Autowired
    ResultUtils resultUtils;

    @Autowired
    private IOfficialWebsiteNavigationService officialWebsiteNavigationService;

    @Autowired
    private INewsCenterService iNewsCenterService;
    @Autowired
    UrlUtil urlUtil;

    public ModelAndView baseView(HttpServletRequest request, String title, String viewPath) {
        ModelAndView view = new ModelAndView(viewPath);
        view.addObject("title", title);
        view.addObject("keywords", "keywords");
        view.addObject("descriptions", "descriptions ！！");
        view.addObject("webSite", config.getWebSite());
        view.addObject("staticVersion", config.getStaticVersion());
        view.addObject("defaultPageSize", config.getDefaultPageSize());
        view.addObject("staticSite", config.getStaticSite());
        view.addObject("imgUrl", config.getImgUrl());
        boolean isLogin = false;
        view.addObject("isLogin", isLogin);

        OfficialWebsiteNavigation officialWebsiteNavigation = new OfficialWebsiteNavigation();
        officialWebsiteNavigation.setLanguage(LocaleContextHolder.getLocale().getLanguage());
        R<List<OfficialWebsiteNavigation>> officialWebsiteNavigation1 = officialWebsiteNavigationService.getOfficialWebsiteNavigation(officialWebsiteNavigation);
        List<OfficialWebsiteNavigation> listNavigation = officialWebsiteNavigation1.getData();
        view.addObject("navigationList", listNavigation);

        String address = resultUtils.getMsg(LanguageCodeConstants.address);
        String workday = resultUtils.getMsg(LanguageCodeConstants.workday);
        view.addObject("address", address);
        view.addObject("workday", workday);
        initLanguage(request, view);
        initNav(request, view);
        return view;
    }

    private void initLanguage(HttpServletRequest request, ModelAndView view) {
        String language = "<a href=\"/en\">English</a>";
        String lang = request.getParameter("lang");
        if (StrUtil.isNotEmpty(lang) && lang.toLowerCase().equals("en")) {
            language = "<a href=\"/\">中文</a>";
        }
        view.addObject("language", language);
    }


    private void initNav(HttpServletRequest request, ModelAndView view) {
        String language = request.getParameter("lang");
        OfficialWebsiteNavigation officialWebsiteNavigation = new OfficialWebsiteNavigation();
        officialWebsiteNavigation.setLanguage(LocaleContextHolder.getLocale().getLanguage());
        R<List<OfficialWebsiteNavigation>> officialWebsiteNavigation1 = officialWebsiteNavigationService.getOfficialWebsiteNavigation(officialWebsiteNavigation);
        List<OfficialWebsiteNavigation> listOne = officialWebsiteNavigation1.getData();
        if (CollectionUtil.isEmpty(listOne)) {
            return;
        }
        StringBuilder sb = new StringBuilder();
        for (OfficialWebsiteNavigation one : listOne) {
            boolean oneExistsChild = CollectionUtil.isNotEmpty(one.getChildList());
            String oneExistDropDown = oneExistsChild ? "class=\"drop-down\"" : "";
            sb.append("<li " + oneExistDropDown + ">");
            sb.append("<a href=\"" + urlUtil.getJumpUrl(one.getLinkUrl(), language) + "\">" + one.getTitle() + "</a>");
            initTwoNav(one, sb, language);
            sb.append("</li>");
        }
        view.addObject("navStr", sb.toString());
    }

    private void initTwoNav(OfficialWebsiteNavigation one, StringBuilder sb, String language) {
        List<OfficialWebsiteNavigation> twoChildList = one.getChildList();
        //有二级菜单
        boolean twoExistsDropDown = CollectionUtil.isNotEmpty(twoChildList);
        if (!twoExistsDropDown) {
            return;
        }
        sb.append("<ul>");
        for (OfficialWebsiteNavigation two : twoChildList) {
            List<OfficialWebsiteNavigation> threeChildList = two.getChildList();
            boolean threeExistsChild = CollectionUtil.isNotEmpty(threeChildList);
            String threeExistDropDown = threeExistsChild ? "class=\"drop-down\"" : "";
            String twoUrl = StrUtil.isNotEmpty(two.getLinkUrl()) ? two.getLinkUrl() : "javascript:void(0)";
            sb.append("<li " + threeExistDropDown + ">");
            sb.append("<a href=\"" + urlUtil.getJumpUrl(twoUrl, language) + "\">" + two.getTitle() + "</a>");
            initThreeNav(sb, threeExistsChild, threeChildList, language);
            sb.append("</li>");
        }
        sb.append("</ul>");
    }

    private void initThreeNav(StringBuilder sb, boolean threeExistsChild, List<OfficialWebsiteNavigation> threeChildList, String language) {
        if (!threeExistsChild) {
            return;
        }
        sb.append("<ul>");
        for (OfficialWebsiteNavigation three : threeChildList) {
            sb.append("<li><a href=\"" + urlUtil.getJumpUrl(three.getLinkUrl(), language) + "\">" + three.getTitle() + "</a></li>");
        }
        sb.append("</ul>");
    }

    public boolean isMobile(HttpServletRequest request) {
        boolean isMobile = false;
        //去获取是否为手机端
        String userAgent = request.getHeader("User-Agent");
        UserAgent ua = UserAgentUtil.parse(userAgent);
        //TODO 这里我认为如果是微信浏览器也是手机
        if (ua.isMobile() || userAgent.toLowerCase().contains("micromessenger")) {
            isMobile = true;
        }
        return isMobile;
    }

//    public String getJumpUrl(String url, String language) {
//        if (StrUtil.isEmpty(language) || language.toLowerCase().equals("zh")) {
//            return url;
//        }
//        if (url.toLowerCase().equals("/")) {
//            url = "";
//        }
//        if (url.toLowerCase().equals("javascript:void(0)")){
//            return url;
//        }
//        return "/en" + url;
//    }

    public ModelAndView baseViewAjax(String title, String viewPath) {
        ModelAndView view = new ModelAndView(viewPath);
        view.addObject("webSite", config.getWebSite());
        view.addObject("staticVersion", config.getStaticVersion());
        view.addObject("staticSite", config.getStaticSite());
        view.addObject("imgUrl", config.getImgUrl());
        return view;
    }

    /**
     * 响应请求分页数据
     */
    @SuppressWarnings({"rawtypes", "unchecked"})
    protected TableDataInfo getDataTable(IPage pageData) {
        TableDataInfo rspData = new TableDataInfo();
        rspData.setCode(1);
        rspData.setRows(pageData.getRecords());
        rspData.setTotal(pageData.getTotal());
        return rspData;
    }

    /**
     * 响应返回结果
     *
     * @param rows 影响行数
     * @return 操作结果
     */
    protected AjaxResult toAjax(int rows) {
        return rows > 0 ? success() : error();
    }

    /**
     * 响应返回结果
     *
     * @param result 结果
     * @return 操作结果
     */
    protected AjaxResult toAjax(boolean result) {
        return result ? success() : error();
    }

    /**
     * 返回成功
     */
    public AjaxResult success() {
        return AjaxResult.success();
    }

    /**
     * 返回失败消息
     */
    public AjaxResult error() {
        return AjaxResult.error();
    }

    /**
     * 返回成功消息
     */
    public AjaxResult success(String message) {
        return AjaxResult.success(message);
    }

    /**
     * 返回失败消息
     */
    public AjaxResult error(String message) {
        return AjaxResult.error(message);
    }
//
//    /**
//     * 返回错误码消息
//     */
//    public AjaxResult error(AjaxResult.Type type, String message) {
//        return new AjaxResult(type, message);
//    }

}
