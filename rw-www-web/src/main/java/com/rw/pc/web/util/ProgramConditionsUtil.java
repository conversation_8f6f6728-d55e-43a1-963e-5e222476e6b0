package com.rw.pc.web.util;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.rw.common.core.domain.R;
import com.rw.common.core.utils.ResultUtils;
import com.rw.common.redis.service.RedisService;
import com.rw.pc.web.model.ProductListWhereModel;
import com.rw.pc.web.model.ProgramListWhereModel;
import com.rw.product.model.entity.PAttr;
import com.rw.product.model.entity.SLabel;
import com.rw.product.service.IPAttrService;
import com.rw.product.service.ISLabelService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 方案筛选条件
 */
@Service
public class ProgramConditionsUtil {
    /**
     * @Fields rm : Redis的业务 数据输入输出
     */
    @Autowired
    private RedisService rm;

    @Autowired
    private ISLabelService labelService;

    @Autowired
    ResultUtils resultUtils;

    /**
     * @Fields bundle : 方案功能参数文件
     */
    private static final ResourceBundle PROGRAMME_LABEL = ResourceBundle.getBundle("label");


    public List<ProgramListWhereModel> getProgrammeLabel(Integer classify) {


        List<ProgramListWhereModel> list=new ArrayList<>();

        String[] fields;

        if (classify == 5) {
            fields = PROGRAMME_LABEL.getString(1 + ".label").split(",");
        } else {
            fields = PROGRAMME_LABEL.getString(classify + ".label").split(",");
        }

        for (String field : fields) {
            if("brand".equals(field)){
                continue;
            }

            SLabel sLabel = new SLabel();
            sLabel.setLabelClass(classify);
            if (classify == 5) {
                sLabel.setClasssifyId(Integer.parseInt(PROGRAMME_LABEL.getString(1 + "." + field + ".label")));
            } else {
                sLabel.setClasssifyId(Integer.parseInt(PROGRAMME_LABEL.getString(classify + "." + field + ".label")));
            }
            List<SLabel> labels = this.getLabelList(sLabel);
            if (CollUtil.isNotEmpty(labels)) {
                ProgramListWhereModel model = new ProgramListWhereModel();
                model.setLabelName(field);
                model.setLabelList(labels);
                list.add(model);
            }
        }


        if (CollUtil.isNotEmpty(list)) {
            for (ProgramListWhereModel model : list) {
                String title = model.getLabelName();

                try {
                    title = resultUtils.getMsg(model.getLabelName());
                } catch (Exception e) {
                    System.out.println(11);
                }
                model.setTitle(title);
            }
        }



        return list;
    }



    /**
     * 获取属性
     *
     * @param label
     * @return
     */
    private List<SLabel> getLabelList(SLabel label) {
        label.setLanguage(LocaleContextHolder.getLocale().getLanguage());
        R r = labelService.getLabelList(label);
        return BeanUtil.copyToList((List<SLabel>) r.getData(), SLabel.class);
    }


    /**
     * 获取分类的品牌ID
     * @param classify
     * @return
     */
    public int getBrandId(int classify) {
        int brandId=0;
        try {
            if (classify == 5) {
                brandId=Integer.parseInt(PROGRAMME_LABEL.getString(1 + ".brand.label"));
            } else {
                brandId=Integer.parseInt(PROGRAMME_LABEL.getString(classify + ".brand.label"));
            }
        } catch (Exception e) {
            //如果报错，说明没有声明这个属性的ID，就是没有这个品牌
        }
        return brandId;
    }


}
