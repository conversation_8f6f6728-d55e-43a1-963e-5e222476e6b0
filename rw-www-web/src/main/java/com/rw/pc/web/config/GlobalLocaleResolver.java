package com.rw.pc.web.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.util.StringUtils;
import org.springframework.web.servlet.LocaleResolver;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Locale;

/**
 * GlobalLocaleResolver
 *
 * <AUTHOR>  2024-03-28
 * https://github.com/dkbnull/SpringBootDemo
 */
@Configuration
public class GlobalLocaleResolver implements LocaleResolver {

    /**
     * 解析请求
     *
     * @param request
     * @return
     */
    @Override
    public Locale resolveLocale(HttpServletRequest request) {
        //默认地区
        Locale locale = request.getLocale();
        //获取请求中的语言参数
        String language = request.getParameter("lang");
        if (!StringUtils.isEmpty(language)) {
            if (language.toLowerCase().equals("zh")) {
                //中文
                locale = new Locale("zh", "CN");
            } else if (language.toLowerCase().equals("en")) {
                //英文
                locale = new Locale("en", "US");
            } else {
                //默认中文
                locale = new Locale("zh", "CN");
            }
            //zh_CN 国家_地区
//            String[] values = language.split("_");
//            locale = new Locale(values[0], values[1]);
        }

        return locale;
    }

    @Override
    public void setLocale(HttpServletRequest request, HttpServletResponse response, Locale locale) {

    }

    @Bean
    public LocaleResolver localeResolver() {
        return new GlobalLocaleResolver();
    }
}
