package com.rw.pc.web.constant;

import com.alibaba.nacos.shaded.org.checkerframework.common.returnsreceiver.qual.This;
import jdk.nashorn.internal.scripts.JD;
import org.apache.commons.math3.analysis.function.Floor;
import org.apache.commons.math3.analysis.function.Power;
import org.apache.commons.math3.stat.descriptive.summary.Product;
import org.apache.http.impl.conn.Wire;
import sun.net.www.content.image.png;

public interface LanguageCodeConstants {
    String SYSTEM_EXCEPTION = "0";
    String INVALID_PARAMETER = "000000002";
    String USER_IS_NOT_LOGGED_IN = "*********";
    String USER_HAS_NO_PERMISSION = "*********";
    String SYSTEM_TYPE_ILLEGAL = "*********";


    //---------------------------------公用 start---------------------------------------
    String homepage="000001";//首页
    String address="000002";//深圳市龙华区观澜街道大富社区平安路60号康淮工业园
    String workday="000003";//工作日 9:00-18:00
    String news_and_information="000004";//新闻资讯
    String contact_us="000005";//联系我们
    String company_address="000006";//北京市海淀区上地十街1号院5号楼17层1706
    String company_name_tip="000007";//公司名称
    String company_name="000008";//北京联物瑞达信息技术有限公司
    String tax_registration_number_tip="000009";//纳税登记号
    String name_of_the_deposit_bank_tip="000010";//开户行名称
    String name_of_the_deposit_bank="000011";//上海浦东银行有限公司北京望京支行
    String bank_account_code_tip="000012";//银行账号
    String copy_the_invoicing_information="000013";//复制开票信息
    String invoicing_information="000014";//公司名称：北京联物瑞达信息技术有限公司  纳税登记号：91110108MA01Q6Y29A  地址：北京市海淀区上地十街1号院5号楼17层1706  电话：010-********  开户行名称：上海浦东银行有限公司北京望京支行  银行账号：91340078801400000832
    String copy_success="000015";//开票信息已复制，请粘贴！
    String jd_franchised_store="000016";//京东专营店
    String jd_self_operated_store="000017";//京东自营店
    String success_case="000018";//成功案例
    String reset_filter="000019";//重置筛选
    String all="000020";//全部
    String product_list_count_tip="000021";//共找到 {0} 个产品
    String no_data_available="000022";//暂无数据
    String product_series_other_model="000023";//同系列其它型号
    String product_application_examples="000024";//方案应用案例
    String boxlist_count_tip="000025";//共找到 {0} 个方案
    String download_center="000026";//下载中心
    String download_list_count_tip="000027";//共找到 {0} 个下载资料
    String document_download="000028";//资料下载
    String electrical_parameters="000029";//电气参数
    String physical_parameters="000030";//物理参数
    String detailed_parameters="000031";//详细参数
    String application_scheme="000032";//应用方案
    String product_overview="000033";//产品综述


    String brand="brand";//品牌
    String series="series";//系列
    String capacity="capacity";//容量
    String voltagesort="voltagesort";//输入输出
    String version="version";//版本
    String batterytype="batterytype";//原理分类
    String batteryform="batteryform";//电池形式
    String voltage="voltage";//电压
    String hourlyrate="hourlyrate";//小时率
    String principle="principle";//原理
    String precision="precision";//精度





    //---------------------------------公用 end---------------------------------------



//-------------------------------首页 start----------------------------------
    String index_slides_1_label="100001";
    String index_slides_1_title="100002";
    String index_slides_1_desc="100003";

    String index_slides_2_label="100011";
    String index_slides_2_title="100012";
    String index_slides_2_desc="100013";


    String index_category_title="110000";
    String index_category_1_title="110001";
    String index_category_1_desc="110002";

    String index_category_2_title="110011";
    String index_category_2_desc="110012";

    String index_category_3_title="110021";
    String index_category_3_desc="110022";

    String index_category_4_title="110031";
    String index_category_4_desc="110032";

    String index_category_5_title="110041";
    String index_category_5_desc="110042";


    String index_scheme_title="120000";
    String index_scheme_1_title="120001";
    String index_scheme_2_title="120011";
    String view_more="120020";
    String view_the_list="120021";



    String index_scheme_list_title="*********";
    String index_scheme_list_capacity="*********";
    String index_scheme_list_capacity_value="*********";
    String index_scheme_list_battery="*********";
    String index_scheme_list_battery_value="*********";
    String index_scheme_list_battery_cabinet="*********";
    String index_scheme_list_battery_cabinet_value="*********";


    String index_company_title="130000";
    String index_company_desc="130001";


    String index_company_news="140000";


//-------------------------------首页 end----------------------------------



//-------------------------------产品中心 start----------------------------------



    String product_title="200000";
    String product_category_1_title="210000";

    String product_category_1_list_1_title="211001";
    String product_category_1_list_1_desc="211002";

    String product_category_1_list_2_title="212001";
    String product_category_1_list_2_desc="212002";

    String product_category_1_list_3_title="213001";
    String product_category_1_list_3_desc="213002";

    String product_category_1_list_4_title="214001";
    String product_category_1_list_4_desc="214002";

    String product_category_1_list_5_title="215001";
    String product_category_1_list_5_desc="215002";





    String product_category_2_title="220000";

    String product_category_2_list_1_title="221001";
    String product_category_2_list_1_desc="221002";

    String product_category_2_list_2_title="222001";
    String product_category_2_list_2_desc="222002";

    String product_category_2_list_3_title="223001";
    String product_category_2_list_3_desc="223002";

    String product_category_2_list_4_title="224001";
    String product_category_2_list_4_desc="224002";




    String product_category_3_title="230000";

    String product_category_3_list_1_title="231001";
    String product_category_3_list_1_desc="231002";

    String product_category_3_list_2_title="232001";
    String product_category_3_list_2_desc="232002";

    String product_category_3_list_3_title="233001";
    String product_category_3_list_3_desc="233002";



    String product_category_4_title="240000";

    String product_category_4_list_1_title="241001";
    String product_category_4_list_1_desc="241002";

    String product_category_4_list_2_title="242001";
    String product_category_4_list_2_desc="242002";

    String product_category_4_list_3_title="243001";
    String product_category_4_list_3_desc="243002";

    String product_category_4_list_4_title="244001";
    String product_category_4_list_4_desc="244002";



    String product_category_5_title="250000";

    String product_category_5_list_1_title="251001";
    String product_category_5_list_1_desc="251002";

    String product_category_5_list_2_title="252001";
    String product_category_5_list_2_desc="252002";






//-------------------------------产品中心 end----------------------------------



//-------------------------------产品列表 start----------------------------------


    String product_usp_power_supply="300000";//UPS电源
    String product_battery="300001";//电池
    String product_outdoor_unit="300002";//室外机
    String product_indoor_unit="300003";//室内机
    String product_eps_power_supply="300004";//EPS电源
    String product_sts="300005";//STS
    String product_pdu="300006";//PDU
    String product_voltage_stabilizing_power_supply="300007";//稳压电源
    String product_inverter="300008";//逆变器
    String product_battery_cabinet="300009";//电池柜
    String product_battery_wire="300010";//电池线



    String product_model="3000011";//型号
    String product_series="3000012";//系列
    String product_capacity="3000013";//容量
    String product_input_and_output_voltage="3000014";//输入输出电压
    String product_size="3000015";//尺寸
    String product_weight ="3000016";//重量kg
    String product_voltage="3000017";//电压
    String product_refrigerating_capacity="3000018";//制冷量
    String product_heating_capacity="3000019";//制热量
    String product_air_conditioning_type="3000020";//空调类型
    String product_cooling_method="3000021";//冷却方式
    String product_air_supply_mode="3000022";//送风方式
    String product_type="3000023";//类型
    String product_grade="3000024";//级数
    String product_number_of_holes="3000025";//孔数
    String product_principle="3000026";//原理
    String product_accuracy="3000027";//精度
    String product_input_voltage="3000028";//输入电压
    String product_output_voltage="3000029";//输出电压
    String product_specification="3000030";//规格




//-------------------------------产品列表 end----------------------------------




//-------------------------------方案库 start----------------------------------


    String boxlist_system_solution="5000000";//系统方案
    String boxlist_power_distribution_scheme="5000001";//配电方案
    String boxlist_ups_scheme="5000002";//UPS方案
    String boxlist_air_conditioning_scheme="5000003";//空调方案



    String boxlist_distribution_box="50000010";//配电柜
    String boxlist_distribution_box_desc="50000011";//描述
    String boxlist_system_solution_btn_text="50000012";//1w+ 方案库

    String boxlist_ups_system="5000020";//UPS系统
    String boxlist_ups_system_desc="5000021";//描述
    String boxlist_ups_system_btn_text="5000022";//1w+ 方案库

    String boxlist_precision_air_conditioning="5000030";//机房精密空调
    String boxlist_precision_air_conditioning_desc="5000031";//描述
    String boxlist_precision_air_conditioning_btn_text="5000032";//100w+ 方案库


    String boxlist_scheme_name="5100001";//方案名称
    String boxlist_category="5100002";//类别
    String boxlist_view_the_checklist="5100003";//查看清单


    String boxlist_briefly_describe="5200001";//简述
    String boxlist_product_category="5200002";//产品类别
    String boxlist_product_name="5200003";//产品名称
    String boxlist_quantity="5200004";//数量
    String boxlist_description="5200005";//此配置方案采用恒功率法计算，电池放电截止电压1.67/cell



//-------------------------------方案库 end----------------------------------




//---------------------------------------下载中心start--------------------------------------

    String download_search="5300001";//请输入资料名称
    String download_table_file_name="5300002";//文件名称
    String download_table_update_time="5300003";//更新时间
    String download_table_file_size="5300004";//文件大小
    String download_table_operation="5300005";//操作
    String download_btn="5300006";//下载


//---------------------------------------下载中心end--------------------------------------




//---------------------------------------产品详情start--------------------------------------

    String product_detail_net_weight="6100001";//净重
    String product_detail_gross_weight="6100002";//毛重
    String product_detail_size="6100003";//尺寸
    String product_detail_product_capacity="6100004";//产品容量
    String product_detail_type="6100005";//类型
    String product_detail_input_and_output="6100006";//输入输出
    String product_detail_direct_current_voltage="6100007";//直流电压
    String product_detail_product_power="6100008";//产品功率
    String product_detail_principle_classification="6100009";//原理分类
    String product_detail_battery_form="6100010";//电池形式
    String product_detail_voltage="6100011";//电压
    String product_detail_input_voltage="6100012";//输入电压
    String product_detail_output_voltage="6100013";//输出电压
    String product_detail_principle="6100014";//原理
    String product_detail_precision="6100015";//精度
    String product_detail_series="6100016";//级数
    String product_detail_number_of_holes="6100017";//孔数
    String product_detail_cooling_capacity="6100018";//制冷量
    String product_detail_heating_capacity="6100019";//制热量
    String product_detail_air_conditioning_type="6100020";//空调类型
    String product_detail_air_supply_mode="6100021";//送风方式
    String product_detail_cooling_type="6100022";//冷却类型
    String product_detail_power_consumption_type="6100023";//用电类型
    String product_detail_structure="6100024";//结构



//---------------------------------------产品详情end--------------------------------------


}
