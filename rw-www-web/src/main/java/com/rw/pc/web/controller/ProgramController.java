package com.rw.pc.web.controller;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rw.common.core.domain.R;
import com.rw.common.core.utils.PageResultUtils;
import com.rw.common.core.utils.ResultUtils;
import com.rw.common.service.INewsCenterService;
import com.rw.common.service.ISeoService;
import com.rw.pc.web.config.AuthorConfig;
import com.rw.pc.web.constant.LanguageCodeConstants;
import com.rw.pc.web.model.ProgramListWhereModel;
import com.rw.pc.web.model.ProgramReq;
import com.rw.pc.web.util.ProductConditionsUtil;
import com.rw.pc.web.util.ProgramConditionsUtil;
import com.rw.pc.web.util.RwPageUtil;
import com.rw.pc.web.util.RwUtil;
import com.rw.product.model.constant.ProductMainClassConstants;
import com.rw.product.model.entity.*;
import com.rw.product.service.*;
import lombok.AllArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 用户信息
 *
 * <AUTHOR>
 */
@AllArgsConstructor
@Controller
@RequestMapping("/program")
public class ProgramController extends BaseController {


    @Autowired
    ResultUtils resultUtils;

    @Autowired
    private INewsCenterService iNewsCenterService;

    @Autowired
    private ISeoService seoService;


    @Autowired
    private AuthorConfig authorConfig;

    @Autowired
    private ProductConditionsUtil productConditionsUtil;

    @Autowired
    private ProgramConditionsUtil programConditionsUtil;

    @Autowired
    private IPAirService airService;

    @Autowired
    private IPEpsService epsService;

    @Autowired
    private IPPemonitorService pemonitorService;

    @Autowired
    private IPProductService productService;

    @Autowired
    private IPRoomService roomService;

    @Autowired
    private IPSingleService singleService;

    @Autowired
    private IPUpsService upsService;

    @Autowired
    private ISBoxlistService boxlistService;
    @Autowired
    RwPageUtil rwPageUtil;
    @Autowired
    RwUtil rwUtil;

    /**
     * 方案库
     */
    @GetMapping("program")
    public ModelAndView program(HttpServletRequest request, HttpServletResponse response) {
        ModelAndView view = baseView(request, "系统方案", "program");
        view.addObject("keywords", "");
        view.addObject("descriptions", "");


        String homepage = resultUtils.getMsg(LanguageCodeConstants.homepage);
        String boxlist_system_solution = resultUtils.getMsg(LanguageCodeConstants.boxlist_system_solution);
        String boxlist_distribution_box = resultUtils.getMsg(LanguageCodeConstants.boxlist_distribution_box);
        String boxlist_distribution_box_desc = resultUtils.getMsg(LanguageCodeConstants.boxlist_distribution_box_desc);
        String boxlist_system_solution_btn_text = resultUtils.getMsg(LanguageCodeConstants.boxlist_system_solution_btn_text);
        String boxlist_ups_system = resultUtils.getMsg(LanguageCodeConstants.boxlist_ups_system);
        String boxlist_ups_system_desc = resultUtils.getMsg(LanguageCodeConstants.boxlist_ups_system_desc);
        String boxlist_ups_system_btn_text = resultUtils.getMsg(LanguageCodeConstants.boxlist_ups_system_btn_text);
        String boxlist_precision_air_conditioning = resultUtils.getMsg(LanguageCodeConstants.boxlist_precision_air_conditioning);
        String boxlist_precision_air_conditioning_desc = resultUtils.getMsg(LanguageCodeConstants.boxlist_precision_air_conditioning_desc);
        String boxlist_precision_air_conditioning_btn_text = resultUtils.getMsg(LanguageCodeConstants.boxlist_precision_air_conditioning_btn_text);


        view.addObject("homepage", homepage);
        view.addObject("boxlist_system_solution", boxlist_system_solution);

        view.addObject("boxlist_distribution_box", boxlist_distribution_box);
        view.addObject("boxlist_distribution_box_desc", boxlist_distribution_box_desc);
        view.addObject("boxlist_system_solution_btn_text", boxlist_system_solution_btn_text);

        view.addObject("boxlist_ups_system", boxlist_ups_system);
        view.addObject("boxlist_ups_system_desc", boxlist_ups_system_desc);
        view.addObject("boxlist_ups_system_btn_text", boxlist_ups_system_btn_text);

        view.addObject("boxlist_precision_air_conditioning", boxlist_precision_air_conditioning);
        view.addObject("boxlist_precision_air_conditioning_desc", boxlist_precision_air_conditioning_desc);
        view.addObject("boxlist_precision_air_conditioning_btn_text", boxlist_precision_air_conditioning_btn_text);

        return view;
    }

    /**
     * 方案库列表
     */
    @GetMapping("programList")
    public ModelAndView programList(ProgramReq req, HttpServletRequest request, HttpServletResponse response) {
        ModelAndView view = baseView(request, "方案列表", "programList");
        view.addObject("keywords", "");
        view.addObject("descriptions", "");
        view.addObject("classify", Convert.toInt(req.getClassify()));


        //获取对应classify方案的品牌ID，方案只查询对应品牌的
        String brandId = rwUtil.getBoxlistBrand(Convert.toInt(req.getClassify()));
        if(StrUtil.isNotBlank(brandId)){
            if (StrUtil.isNotBlank(req.getLabels())) {
                List<String> labelList = Stream.of(req.getLabels().split(",")).collect(Collectors.toList());
                labelList.add(Convert.toStr(brandId));
                req.setLabels(StrUtil.join(",",labelList));
            }else{
                req.setLabels(Convert.toStr(brandId));
            }
        }

        //获取筛选参数
        List<ProgramListWhereModel> programParameters = programConditionsUtil.getProgrammeLabel(Convert.toInt(req.getClassify()));
        view.addObject("programParameters", programParameters);


        SBoxlist modelReq = new SBoxlist();
        modelReq.setPageNum(Convert.toInt(req.getPageNo()));
        modelReq.setPageSize(10);
        modelReq.setLanguage(LocaleContextHolder.getLocale().getLanguage());
        modelReq.setClassify(Convert.toLong(req.getClassify()));
        modelReq.setLabelId(req.getLabels());
        PageResultUtils<SBoxlist> pageList = boxlistService.page(modelReq);


        view.addObject("programme", pageList.getList());
        view.addObject("pages", rwPageUtil.getRwPageHTML(pageList, request));
        view.addObject("totalCount", pageList.getTotalCount());

        String homepage = resultUtils.getMsg(LanguageCodeConstants.homepage);
        String boxlist_system_solution = resultUtils.getMsg(LanguageCodeConstants.boxlist_system_solution);
        String boxlist_power_distribution_scheme = resultUtils.getMsg(LanguageCodeConstants.boxlist_power_distribution_scheme);
        String boxlist_ups_scheme = resultUtils.getMsg(LanguageCodeConstants.boxlist_ups_scheme);
        String boxlist_air_conditioning_scheme = resultUtils.getMsg(LanguageCodeConstants.boxlist_air_conditioning_scheme);


        String boxlist_distribution_box = resultUtils.getMsg(LanguageCodeConstants.boxlist_distribution_box);
        String boxlist_distribution_box_desc = resultUtils.getMsg(LanguageCodeConstants.boxlist_distribution_box_desc);
        String boxlist_system_solution_btn_text = resultUtils.getMsg(LanguageCodeConstants.boxlist_system_solution_btn_text);
        String boxlist_ups_system = resultUtils.getMsg(LanguageCodeConstants.boxlist_ups_system);
        String boxlist_ups_system_desc = resultUtils.getMsg(LanguageCodeConstants.boxlist_ups_system_desc);
        String boxlist_ups_system_btn_text = resultUtils.getMsg(LanguageCodeConstants.boxlist_ups_system_btn_text);
        String boxlist_precision_air_conditioning = resultUtils.getMsg(LanguageCodeConstants.boxlist_precision_air_conditioning);
        String boxlist_precision_air_conditioning_desc = resultUtils.getMsg(LanguageCodeConstants.boxlist_precision_air_conditioning_desc);
        String boxlist_precision_air_conditioning_btn_text = resultUtils.getMsg(LanguageCodeConstants.boxlist_precision_air_conditioning_btn_text);


        String reset_filter = resultUtils.getMsg(LanguageCodeConstants.reset_filter);
        String all = resultUtils.getMsg(LanguageCodeConstants.all);
        String boxlist_count_tip = resultUtils.getMsg(LanguageCodeConstants.boxlist_count_tip);

        String no_data_available = resultUtils.getMsg(LanguageCodeConstants.no_data_available);


        String boxlist_scheme_name = resultUtils.getMsg(LanguageCodeConstants.boxlist_scheme_name);
        String boxlist_category = resultUtils.getMsg(LanguageCodeConstants.boxlist_category);
        String boxlist_view_the_checklist = resultUtils.getMsg(LanguageCodeConstants.boxlist_view_the_checklist);


        view.addObject("homepage", homepage);
        view.addObject("boxlist_system_solution", boxlist_system_solution);
        view.addObject("boxlist_power_distribution_scheme", boxlist_power_distribution_scheme);
        view.addObject("boxlist_ups_scheme", boxlist_ups_scheme);
        view.addObject("boxlist_air_conditioning_scheme", boxlist_air_conditioning_scheme);


        view.addObject("boxlist_distribution_box", boxlist_distribution_box);
        view.addObject("boxlist_distribution_box_desc", boxlist_distribution_box_desc);
        view.addObject("boxlist_system_solution_btn_text", boxlist_system_solution_btn_text);

        view.addObject("boxlist_ups_system", boxlist_ups_system);
        view.addObject("boxlist_ups_system_desc", boxlist_ups_system_desc);
        view.addObject("boxlist_ups_system_btn_text", boxlist_ups_system_btn_text);

        view.addObject("boxlist_precision_air_conditioning", boxlist_precision_air_conditioning);
        view.addObject("boxlist_precision_air_conditioning_desc", boxlist_precision_air_conditioning_desc);
        view.addObject("boxlist_precision_air_conditioning_btn_text", boxlist_precision_air_conditioning_btn_text);

        view.addObject("reset_filter", reset_filter);
        view.addObject("all", all);
        view.addObject("boxlist_count_tip", boxlist_count_tip);
        view.addObject("no_data_available", no_data_available);


        view.addObject("boxlist_scheme_name", boxlist_scheme_name);
        view.addObject("boxlist_category", boxlist_category);
        view.addObject("boxlist_view_the_checklist", boxlist_view_the_checklist);

        return view;
    }


    /**
     * 方案库详情
     */
    @GetMapping("programInfo")
    public ModelAndView programInfo(ProgramReq req, HttpServletRequest request, HttpServletResponse response) {
        ModelAndView view = baseView(request, "方案详情", "programInfo");
        view.addObject("keywords", "");
        view.addObject("descriptions", "");
        view.addObject("classify", Convert.toInt(req.getClassify()));


        SBoxlist box = new SBoxlist();
        box.setLanguage(LocaleContextHolder.getLocale().getLanguage());
        box.setListNo(Convert.toLong(req.getListno()));
        box.setClassify(Convert.toLong(req.getClassify()));
        R info = boxlistService.getInfoById(box);
        if (info != null && info.getData() != null) {
            box = JSONUtil.toBean(JSONUtil.toJsonStr(info.getData()), SBoxlist.class);
            if (CollUtil.isNotEmpty(box.getDetailList())) {

                for (SBoxlistDetail detail : box.getDetailList()) {

                    if (ProductMainClassConstants.air.contains(Convert.toStr(detail.getMainClass()))) {
                        PAir modelReq = new PAir();
                        modelReq.setLanguage(LocaleContextHolder.getLocale().getLanguage());
                        modelReq.setId(detail.getProductId());
                        modelReq.setMainClass(Convert.toInt(detail.getMainClass()));
                        R detailInfo = airService.getInfoById(modelReq);
                        if (detailInfo != null && detailInfo.getData() != null) {
                            modelReq = BeanUtil.toBean(detailInfo.getData(), PAir.class);
                            detail.setProductTitle(modelReq.getTitle());
                        }
                    } else if (ProductMainClassConstants.eps.contains(Convert.toStr(detail.getMainClass()))) {

                        PEps modelReq = new PEps();
                        modelReq.setLanguage(LocaleContextHolder.getLocale().getLanguage());
                        modelReq.setId(detail.getProductId());
                        modelReq.setMainClass(Convert.toInt(detail.getMainClass()));
                        R detailInfo = epsService.getInfoById(modelReq);
                        if (detailInfo != null && detailInfo.getData() != null) {
                            modelReq = BeanUtil.toBean(detailInfo.getData(), PEps.class);
                            detail.setProductTitle(modelReq.getTitle());
                        }

                    } else if (ProductMainClassConstants.pemonitor.contains(Convert.toStr(detail.getMainClass()))) {

                        //PPemonitor modelReq = new PPemonitor();
                        //modelReq.setPageNum(Convert.toInt(req.getPageNo()));
                        //modelReq.setPageSize(10);
                        //modelReq.setLanguage(LocaleContextHolder.getLocale().getLanguage());
                        //modelReq.setMainClass(Convert.toInt(req.getMainclass()));
                        //modelReq.setBrand(brandId);
                        //r = pemonitorService.page(modelReq);

                    } else if (ProductMainClassConstants.product.contains(Convert.toStr(detail.getMainClass()))) {

                    } else if (ProductMainClassConstants.room.contains(Convert.toStr(detail.getMainClass()))) {


                        PRoom modelReq = new PRoom();
                        modelReq.setLanguage(LocaleContextHolder.getLocale().getLanguage());
                        modelReq.setId(detail.getProductId());
                        modelReq.setMainClass(Convert.toInt(detail.getMainClass()));
                        R detailInfo = roomService.getInfoById(modelReq);
                        if (detailInfo != null && detailInfo.getData() != null) {
                            modelReq = BeanUtil.toBean(detailInfo.getData(), PRoom.class);
                            detail.setProductTitle(modelReq.getTitle());
                        }


                    } else if (ProductMainClassConstants.single.contains(Convert.toStr(detail.getMainClass()))) {
                        PSingle modelReq = new PSingle();
                        modelReq.setLanguage(LocaleContextHolder.getLocale().getLanguage());
                        modelReq.setId(detail.getProductId());
                        modelReq.setMainClass(Convert.toInt(detail.getMainClass()));
                        R detailInfo = singleService.getInfoById(modelReq);
                        if (detailInfo != null && detailInfo.getData() != null) {
                            modelReq = BeanUtil.toBean(detailInfo.getData(), PSingle.class);
                            detail.setProductTitle(modelReq.getTitle());
                        }

                    } else if (ProductMainClassConstants.ups.contains(Convert.toStr(detail.getMainClass()))) {
                        PUps modelReq = new PUps();
                        modelReq.setLanguage(LocaleContextHolder.getLocale().getLanguage());
                        modelReq.setId(detail.getProductId());
                        modelReq.setMainClass(Convert.toInt(detail.getMainClass()));
                        R detailInfo = upsService.getInfoById(modelReq);
                        if (detailInfo != null && detailInfo.getData() != null) {
                            modelReq = BeanUtil.toBean(detailInfo.getData(), PUps.class);
                            detail.setProductTitle(modelReq.getTitle());
                        }
                    }

                }

            }
        }


        view.addObject("programme", box);


        String homepage = resultUtils.getMsg(LanguageCodeConstants.homepage);
        String boxlist_system_solution = resultUtils.getMsg(LanguageCodeConstants.boxlist_system_solution);
        String boxlist_distribution_box = resultUtils.getMsg(LanguageCodeConstants.boxlist_distribution_box);
        String boxlist_distribution_box_desc = resultUtils.getMsg(LanguageCodeConstants.boxlist_distribution_box_desc);
        String boxlist_system_solution_btn_text = resultUtils.getMsg(LanguageCodeConstants.boxlist_system_solution_btn_text);
        String boxlist_ups_system = resultUtils.getMsg(LanguageCodeConstants.boxlist_ups_system);
        String boxlist_ups_system_desc = resultUtils.getMsg(LanguageCodeConstants.boxlist_ups_system_desc);
        String boxlist_ups_system_btn_text = resultUtils.getMsg(LanguageCodeConstants.boxlist_ups_system_btn_text);
        String boxlist_precision_air_conditioning = resultUtils.getMsg(LanguageCodeConstants.boxlist_precision_air_conditioning);
        String boxlist_precision_air_conditioning_desc = resultUtils.getMsg(LanguageCodeConstants.boxlist_precision_air_conditioning_desc);
        String boxlist_precision_air_conditioning_btn_text = resultUtils.getMsg(LanguageCodeConstants.boxlist_precision_air_conditioning_btn_text);


        String boxlist_briefly_describe = resultUtils.getMsg(LanguageCodeConstants.boxlist_briefly_describe);
        String boxlist_product_category = resultUtils.getMsg(LanguageCodeConstants.boxlist_product_category);
        String boxlist_product_name = resultUtils.getMsg(LanguageCodeConstants.boxlist_product_name);
        String boxlist_quantity = resultUtils.getMsg(LanguageCodeConstants.boxlist_quantity);
        String boxlist_description = resultUtils.getMsg(LanguageCodeConstants.boxlist_description);


        view.addObject("homepage", homepage);
        view.addObject("boxlist_system_solution", boxlist_system_solution);

        view.addObject("boxlist_distribution_box", boxlist_distribution_box);
        view.addObject("boxlist_distribution_box_desc", boxlist_distribution_box_desc);
        view.addObject("boxlist_system_solution_btn_text", boxlist_system_solution_btn_text);

        view.addObject("boxlist_ups_system", boxlist_ups_system);
        view.addObject("boxlist_ups_system_desc", boxlist_ups_system_desc);
        view.addObject("boxlist_ups_system_btn_text", boxlist_ups_system_btn_text);

        view.addObject("boxlist_precision_air_conditioning", boxlist_precision_air_conditioning);
        view.addObject("boxlist_precision_air_conditioning_desc", boxlist_precision_air_conditioning_desc);
        view.addObject("boxlist_precision_air_conditioning_btn_text", boxlist_precision_air_conditioning_btn_text);

        view.addObject("boxlist_briefly_describe", boxlist_briefly_describe);
        view.addObject("boxlist_product_category", boxlist_product_category);
        view.addObject("boxlist_product_name", boxlist_product_name);
        view.addObject("boxlist_quantity", boxlist_quantity);
        view.addObject("boxlist_description", boxlist_description);


        return view;
    }


}
