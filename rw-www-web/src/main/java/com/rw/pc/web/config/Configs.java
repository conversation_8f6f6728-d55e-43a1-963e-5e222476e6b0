package com.rw.pc.web.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

@Component
@RefreshScope
@Data
public class Configs {

    @Value("${webSite}")
    private String webSite;

    @Value("${staticSite}")
    private String staticSite;

    @Value("${staticVersion:1}")
    private String staticVersion;

    @Value("${defaultPageSize:20}")
    private String defaultPageSize;

    @Value("${imgUrl}")
    private String imgUrl;

    @Value("${isDebug:0}")
    private Integer isDebug;


}

