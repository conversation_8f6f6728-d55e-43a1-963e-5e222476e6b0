package ${packageName}.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import java.util.Arrays;
import java.util.List;
#foreach ($column in $columns)
#if($column.javaField == 'createTime' || $column.javaField == 'updateTime')
import com.rw.common.core.utils.DateUtils;
#break
#end
#end
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
#if($table.sub)
import java.util.ArrayList;
import com.rw.common.core.utils.StringUtils;
import org.springframework.transaction.annotation.Transactional;
import ${packageName}.entity.${subClassName};
#end
import ${packageName}.mapper.${ClassName}Mapper;
import ${packageName}.model.entity.${ClassName};
import ${packageName}.service.I${ClassName}Service;
import ${packageName}.service.UserService;
import com.rw.common.core.utils.poi.ExcelUtil;
import com.rw.common.core.web.page.TableDataInfo;
import javax.servlet.http.HttpServletResponse;
import com.rw.common.core.web.domain.AjaxResult;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.PathVariable;
import com.rw.common.core.web.controller.BaseController;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;
/**
 * ${functionName}Service业务层处理
 * 
 * <AUTHOR>
 * @date ${datetime}
 */
@Slf4j
@RestController
@AllArgsConstructor
public class ${ClassName}ServiceImpl extends BaseController implements I${ClassName}Service
{
    @Autowired
    UserService userService;
    @Autowired
    ${ClassName}Mapper ${className}Mapper;

    /**
     * 查询${functionName}列表
     */
    @Override
    #if($table.crud || $table.sub)
    public TableDataInfo list(${ClassName} ${className})
    {
        startPage();
        QueryWrapper<${ClassName}> queryWrapper = new QueryWrapper<>();
        queryWrapper.setEntity(${className});
        queryWrapper.orderByDesc("${pkColumn.javaField}");
        List<${ClassName}> list =${className}Mapper.selectList(queryWrapper);
        return getDataTable(list);
    }
    #end

##    /**
##     * 导出${functionName}列表
##     */
##    @Override
##    public void export(HttpServletResponse response, ${ClassName} ${className})
##    {
##        startPage();
##        QueryWrapper<${ClassName}> queryWrapper = new QueryWrapper<>();
##        queryWrapper.setEntity(${className});
##        queryWrapper.orderByDesc("${pkColumn.javaField}");
##        List<${ClassName}> list =${className}Mapper.selectList(queryWrapper);
##        ExcelUtil<${ClassName}> util = new ExcelUtil<${ClassName}>(${ClassName}.class);
##        util.exportExcel(response, list, "${functionName}数据");
##    }

    /**
     * 获取${functionName}详细信息
     */
    @Override
    public AjaxResult getInfo(@PathVariable("${pkColumn.javaField}") ${pkColumn.javaType} ${pkColumn.javaField})
    {
        return AjaxResult.success(${className}Mapper.selectById(${pkColumn.javaField}));
    }

    /**
     * 新增${functionName}
     */
    @Override
    public AjaxResult add(@RequestBody ${ClassName} ${className})
    {
        ${className}.setCreateId(userService.getUserId());
        ${className}.setCreateBy(userService.getNickname());
        return toAjax(${className}Mapper.insert(${className}));
    }

    /**
     * 修改${functionName}
     */
    @Override
    public AjaxResult edit(@RequestBody ${ClassName} ${className})
    {
        ${className}.setUpdateId(userService.getUserId());
        ${className}.setUpdateBy(userService.getNickname());
        return toAjax(${className}Mapper.updateById(${className}));
    }

    /**
     * 删除${functionName}
     */
    @Override
    public AjaxResult remove(@PathVariable ${pkColumn.javaType}[] ${pkColumn.javaField}s)
    {
        return toAjax(${className}Mapper.deleteBatchIds(Arrays.asList(${pkColumn.javaField}s)));
    }
}
