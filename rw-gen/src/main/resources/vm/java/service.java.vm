package ${packageName}.service;

import ${packageName}.model.entity.${ClassName};
import ${packageName}.model.Constant;
##import com.rw.common.model.R;
##import com.rw.common.model.request.CheckIpReq;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import com.rw.common.log.annotation.Log;
import com.rw.common.log.enums.BusinessType;
##import annotation.com.rw.common.security.RequiresPermissions;
##import com.rw.common.core.utils.poi.ExcelUtil;
import com.rw.common.core.web.page.TableDataInfo;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import com.rw.common.core.web.domain.AjaxResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * ${functionName}接口
 * 
 * <AUTHOR>
 * @date ${datetime}
 */
@Api(tags = "${functionName}:${ClassName}")
@FeignClient(Constant.APPLICATION_NAME)
public interface I${ClassName}Service {

    @ApiOperation(value = "查询${functionName}列表", response = AjaxResult.class)
##    @RequiresPermissions("${permissionPrefix}:list")
    @GetMapping("${businessName}/list")
    #if($table.crud || $table.sub)
    TableDataInfo list(${ClassName} ${className});
    #elseif($table.tree)
        public AjaxResult list(${ClassName} ${className});
    #end

##    @ApiOperation(value = "导出${functionName}列表")
##    @RequiresPermissions("${permissionPrefix}:export")
##    @Log(title = "${functionName}", businessType = BusinessType.EXPORT)
##    @PostMapping("${businessName}/export")
##    void export(HttpServletResponse response, ${ClassName} ${className});

    @ApiOperation(value = "获取${functionName}详细信息", response = AjaxResult.class)
##    @RequiresPermissions("${permissionPrefix}:query")
    @GetMapping(value = "${businessName}/{${pkColumn.javaField}}")
    AjaxResult getInfo(@PathVariable(value = "${pkColumn.javaField}") ${pkColumn.javaType} ${pkColumn.javaField});

    @ApiOperation(value = "新增${functionName}", response = AjaxResult.class)
##    @RequiresPermissions("${permissionPrefix}:add")
    @Log(title = "${functionName}", businessType = BusinessType.INSERT)
    @PostMapping(value = "${businessName}")
    AjaxResult add(@RequestBody ${ClassName} ${className});

    @ApiOperation(value = "修改${functionName}", response = AjaxResult.class)
##    @RequiresPermissions("${permissionPrefix}:edit")
    @Log(title = "${functionName}", businessType = BusinessType.UPDATE)
    @PutMapping(value = "${businessName}")
    AjaxResult edit(@RequestBody ${ClassName} ${className});

    @ApiOperation(value = "删除${functionName}", response = AjaxResult.class)
##    @RequiresPermissions("${permissionPrefix}:remove")
    @Log(title = "${functionName}", businessType = BusinessType.DELETE)
    @DeleteMapping("${businessName}/{${pkColumn.javaField}s}")
    AjaxResult remove(@PathVariable(value = "${pkColumn.javaField}s") ${pkColumn.javaType}[] ${pkColumn.javaField}s);
}
