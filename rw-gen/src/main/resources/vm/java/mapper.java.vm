package ${packageName}.mapper;

import java.util.List;
import ${packageName}.model.entity.${ClassName};
#if($table.sub)
import ${packageName}.model.entity.${subClassName};
#end
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.springframework.stereotype.Repository;
/**
 * ${functionName}Mapper接口
 * 
 * <AUTHOR>
 * @date ${datetime}
 */
@Repository
public interface ${ClassName}Mapper extends BaseMapper<${ClassName}>
{

}
