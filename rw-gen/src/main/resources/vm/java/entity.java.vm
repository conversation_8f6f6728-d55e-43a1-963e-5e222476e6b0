package ${packageName}.entity;

#foreach ($import in $importList)
import ${import};
#end
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.rw.common.core.annotation.Excel;
#if($table.crud || $table.sub)
import com.rw.common.core.web.domain.BaseEntity;
#elseif($table.tree)
import com.rw.common.core.web.domain.TreeEntity;
#end
import lombok.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.Date;
import com.rw.common.core.web.domain.BaseEntity;
/**
 * ${functionName}对象 ${tableName}
 *
 * <AUTHOR>
 * @date ${datetime}
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "${functionName}")
public class ${ClassName} extends BaseEntity {
#foreach ($column in $columns)
    #set($comment=$column.columnComment)
    #if($column.columnName == $pkColumn.javaField)
    @TableId
    #set($comment='内部标识')
    #end
    #if($column.javaType == 'Date')
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
##    @Excel(name = "${comment}", width = 30, dateFormat = "yyyy-MM-dd")
    #elseif($column.columnName == 'del_flag')
    @TableLogic
    #else
##    @Excel(name = "${comment}")
    #end
    @ApiModelProperty(value = "${comment}")
    private $column.javaType $column.javaField;
#end

}
