package com.rw.file.controller;

import com.rw.common.core.domain.R;
import com.rw.common.core.utils.file.FileUtils;
import com.rw.file.service.ISysFileService;
import com.rw.system.api.domain.SysFile;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;

/**
 * 文件请求处理
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/file")
public class SysFileController {


    private static final Logger log = LoggerFactory.getLogger(SysFileController.class);

    @Autowired
    private ISysFileService sysFileService;

//    private ISysFileService minioSysFileServiceImpl = new MinioSysFileServiceImpl();
//    @Autowired
//    private MinioSysFileServiceImpl minioSysFileServiceImpl;

    /**
     * 文件上传请求
     */
    @PostMapping("upload")
    public R<SysFile> upload(MultipartFile file,String dir) {
        try {
            // 上传并返回访问地址
            String url = sysFileService.uploadFile(file, dir);
            SysFile sysFile = new SysFile();
            sysFile.setName(FileUtils.getName(url));
            sysFile.setUrl(url);
            return R.ok(sysFile);
        } catch (Exception e) {
            log.error("上传文件失败", e);
            return R.fail(e.getMessage());
        }
    }


    /**
     * 文件上传请求
     */
    @PostMapping("uploadCommon")
    public R<SysFile> uploadCommon(MultipartFile file, HttpServletRequest request) {
        try {
            log.info("uploadCommon:{}", request.getParameter("type"));
            // 上传并返回访问地址
            String url = sysFileService.uploadCommonFile(file, request);
            SysFile sysFile = new SysFile();
            sysFile.setName(FileUtils.getName(url));
            sysFile.setUrl(url);
            return R.ok(sysFile);
        } catch (Exception e) {
            log.error("上传文件失败", e);
            return R.fail(e.getMessage());
        }
    }

    ///**
    // * 文件上传请求
    // */
    //@PostMapping("uploadCos")
    //public R<SysFile> uploadCos(MultipartFile file, HttpServletRequest request) {
    //    try {
    //        // 上传并返回访问地址
    //        String url = sysFileService.uploadCos(file, request);
    //        SysFile sysFile = new SysFile();
    //        sysFile.setName(FileUtils.getName(url));
    //        sysFile.setUrl(url);
    //        return R.ok(sysFile);
    //    } catch (Exception e) {
    //        log.error("上传文件失败", e);
    //        return R.fail(e.getMessage());
    //    }
    //}
}