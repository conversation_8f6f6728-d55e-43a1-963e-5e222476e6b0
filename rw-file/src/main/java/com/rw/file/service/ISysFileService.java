package com.rw.file.service;

import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;

/**
 * 文件上传接口
 * 
 * <AUTHOR>
 */
public interface ISysFileService
{
    /**
     * 文件上传接口
     * 
     * @param file 上传的文件
     * @return 访问地址
     * @throws Exception
     */
    String uploadFile(MultipartFile file,String dir) throws Exception;

    /**
     * 文件上传接口
     *
     * @param file 上传的文件
     * @return 访问地址
     * @throws Exception
     */
    String uploadCommonFile(MultipartFile file, HttpServletRequest request) throws Exception;


    /**
     * 文件上传腾讯cos接口
     *
     * @param file 上传的文件
     * @return 访问地址
     * @throws Exception
     */
    //String uploadCos(MultipartFile file, HttpServletRequest request) throws Exception;
}
