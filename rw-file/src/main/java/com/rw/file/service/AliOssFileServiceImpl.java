package com.rw.file.service;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import com.rw.file.utils.AliyunOssUploadUtils;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;

/**
 * Minio 文件存储
 *
 * <AUTHOR>
 */
@Service
public class AliOssFileServiceImpl implements ISysFileService {
    //@Autowired
    //TencentCosFileUtils tencentCosFileUtils;
    /**
     * 本地文件上传接口
     *
     * @param file 上传的文件
     * @return 访问地址
     * @throws Exception
     */
    @Override
    public String uploadFile(MultipartFile file,String dir) throws Exception {
        return AliyunOssUploadUtils.uploadFile(file, dir,0);
    }
    @Override
    public String uploadCommonFile(MultipartFile file, HttpServletRequest request) throws Exception {
        String dir = "";
        Integer isOrigionFileName= Convert.toInt(request.getParameter("isOrigionFileName"),0);
        if (request != null && StrUtil.isNotEmpty(request.getParameter("type"))) {
            dir = request.getParameter("type");
        }
//        if (request != null && StrUtil.isNotEmpty()) {
//            isOrigionFileName = request.getParameter("isOrigionFileName");
//        }
        return AliyunOssUploadUtils.uploadFile(file, dir,isOrigionFileName);
    }

//    @Override
//    public String uploadCos(MultipartFile file, HttpServletRequest request) throws Exception {
//        String dir = "";
//        Integer isOrigionFileName= Convert.toInt(request.getParameter("isOrigionFileName"),0);
//        if (request != null && StrUtil.isNotEmpty(request.getParameter("type"))) {
//            dir = request.getParameter("type");
//        }
////        if (request != null && StrUtil.isNotEmpty(request.getParameter("isOrigionFileName"))) {
////            isOrigionFileName = request.getParameter("isOrigionFileName");
////        }
//        return tencentCosFileUtils.cosUploadFile(file, dir,isOrigionFileName);
//    }
}
