package com.rw.file.utils;

import cn.hutool.core.lang.UUID;
import cn.hutool.core.util.StrUtil;
import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.rw.common.core.utils.DateUtils;
import com.rw.common.core.utils.StringUtils;
import com.rw.file.config.AliyunOssConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;

/**
 * <AUTHOR>
 * @description 阿里云对象存储上传工具类
 * @date 2022/06/16 14:21:12
 */
@Component
public class AliyunOssUploadUtils {

    private static AliyunOssConfig aliyunOssConfig;


    /**
     * 使用构造方法注入配置信息
     */
    @Autowired
    public AliyunOssUploadUtils(AliyunOssConfig aliyunOssConfig) {
        AliyunOssUploadUtils.aliyunOssConfig = aliyunOssConfig;
    }

    /**
     * 上传文件
     *
     * @param file
     * @param path 文件目录
     * @return
     * @throws Exception
     */
    public static String uploadFile(MultipartFile file, String path, Integer isOrigionFileName) throws Exception {

        // 生成 OSSClient
        OSS ossClient = new OSSClientBuilder().build(aliyunOssConfig.getEndpoint(), aliyunOssConfig.getAccessKeyId(), aliyunOssConfig.getAccessKeySecret());
        // 原始文件名称
        // String originalFilename = file.getOriginalFilename();

        // 编码文件名
        String filePathName = FileUploadUtils.extractFilename(file);
        if (isOrigionFileName == 1) {
            filePathName = FileUploadUtils.extractOrigionFilename(file);
        }
        // 文件路径名称
        if (StrUtil.isNotEmpty(path)) {
            filePathName = aliyunOssConfig.getFilehost() + "/" + path + "/" + filePathName;
        } else {
            filePathName = aliyunOssConfig.getFilehost() + "/" + filePathName;
        }
        try {
            ossClient.putObject(aliyunOssConfig.getBucketName(), filePathName, file.getInputStream());
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (ossClient != null) {
                ossClient.shutdown();
            }
        }
        return aliyunOssConfig.getUrl() + "/" + filePathName;
    }


    public static String uploadInputStream(InputStream content, String path, String contentSuffix) {

        // 生成 OSSClient
        OSS ossClient = new OSSClientBuilder().build(aliyunOssConfig.getEndpoint(), aliyunOssConfig.getAccessKeyId(), aliyunOssConfig.getAccessKeySecret());
        // 原始文件名称
        // String originalFilename = file.getOriginalFilename();

        // 编码文件名
        String filePathName = StringUtils.format("{}/{}.{}", DateUtils.datePath(),
                UUID.fastUUID(), contentSuffix);

        // 文件路径名称
        if (StrUtil.isNotEmpty(path)) {
            filePathName = aliyunOssConfig.getFilehost() + "/" + path + "/" + filePathName;
        } else {
            filePathName = aliyunOssConfig.getFilehost() + "/" + filePathName;
        }
        try {
            ossClient.putObject(aliyunOssConfig.getBucketName(), filePathName, content);

        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (ossClient != null) {
                ossClient.shutdown();
            }
        }
        return aliyunOssConfig.getUrl() + "/" + filePathName;
    }
}


