//package com.rw.file.utils;
//
//import com.rw.common.core.utils.StringUtils;
//import com.rw.common.core.utils.file.MimeTypeUtils;
//import com.rw.common.core.utils.uuid.IdUtils;
//import com.rw.file.config.TencentCosConfig;
//import com.qcloud.cos.COSClient;
//import com.qcloud.cos.ClientConfig;
//import com.qcloud.cos.auth.BasicCOSCredentials;
//import com.qcloud.cos.auth.COSCredentials;
//import com.qcloud.cos.model.PutObjectRequest;
//import com.qcloud.cos.model.PutObjectResult;
//import com.qcloud.cos.region.Region;
//import org.apache.commons.io.FilenameUtils;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Component;
//import org.springframework.web.multipart.MultipartFile;
//
//import java.io.File;
//import java.io.FileOutputStream;
//import java.io.InputStream;
//import java.io.OutputStream;
//
///**
// * @program: cos
// * @description: 腾讯云COS上传文件工具类
// * @author: Sun
// * @create: 2020-11-19 15:30
// **/
//@Component
//public class TencentCosFileUtils {
//
//    @Autowired
//    TencentCosConfig tencentCosConfig;
//
//
//    /**
//     * 方法名称:获取存储桶客户端
//     *
//     * @return : com.qcloud.cos.COSClient
//     * <AUTHOR> Sun
//     * @date : 2020/11/19
//     */
//    public COSClient getCosClient() {
//        // 1 初始化用户身份信息（secretId, secretKey）。
//        COSCredentials cred = new BasicCOSCredentials(tencentCosConfig.getCosSecretId(), tencentCosConfig.getCosSecretKey());
//        // 2 设置 bucket 的区域, COS 地域的简称请参照 https://cloud.tencent.com/document/product/436/6224
//        // clientConfig 中包含了设置 region, https(默认 http), 超时, 代理等 set 方法, 使用可参见源码或者常见问题 Java SDK 部分。
//        Region region = new Region(tencentCosConfig.getCosRegionName());
//        ClientConfig clientConfig = new ClientConfig(region);
//        // 3 生成 cos 客户端。
//        return new COSClient(cred, clientConfig);
//    }
//
//    /**
//     * 方法名称:获取对象键（Key）是对象在存储桶中的唯一标识。
//     * <p>
//     * 功能描述:
//     * 〈
//     * 获取对象键（Key）是对象在存储桶中的唯一标识。
//     * 桶内没有文件架的概念
//     * 例如，在对象的访问域名
//     * https://examplebucket-1250000000.cos.ap-guangzhou.myqcloud.com/images/picture.jpg 中，
//     * 对象键为 images/picture.jpg，
//     * 对象键就是访问域名后面的所有路径URL  并且唯一
//     * 〉
//     *
//     * @param file 1
//     * @param path 2
//     * @return : java.lang.String
//     * <AUTHOR> Sun
//     * @date : 2020/11/19
//     */
//    public String getCosFileKey(MultipartFile file, String path, Integer isOrigionFileName) {
//        String filePathName = FileUploadUtils.extractFilename(file);
//        if (isOrigionFileName == 1) {
//            filePathName = FileUploadUtils.extractOrigionFilename(file);
//        }
//        if (StringUtils.isNotNull(path)) {
//            //模拟路径：/campus/res/sysimg/ + circle/schoo_id/ + xxx.后缀
////            return this.getCosResorcePath() + path + getUniqueFilename(file);
//            return tencentCosConfig.getCosResorcePath() + path + filePathName;
//        }
//        //模拟路径：/campus/res/sysimg/ + xxx.后缀
////        return this.getCosResorcePath() + getUniqueFilename(file);
//        return tencentCosConfig.getCosResorcePath() + filePathName;
//    }
//
//
//    /**
//     * 设置：获取唯一文件名字
//     */
//    private static String getUniqueFilename(MultipartFile file) {
//        String fileName = file.getOriginalFilename();
//        String extension = getExtension(file);
//        fileName = IdUtils.fastUUID() + "." + extension;
//        return fileName;
//    }
//
//
//    /**
//     * 获取文件名的后缀
//     *
//     * @param file 表单文件
//     * @return 后缀名
//     */
//    private static String getExtension(MultipartFile file) {
//        String extension = FilenameUtils.getExtension(file.getOriginalFilename());
//        if (StringUtils.isEmpty(extension)) {
//            extension = MimeTypeUtils.getExtension(file.getContentType());
//        }
//        return extension;
//    }
//
//
//    /**
//     * 方法名称:MultipartFile 文件类型转换成File 类型
//     * <p>
//     * 功能描述:
//     * 〈〉
//     *
//     * @param multipartFile 1
//     * @return : java.io.File
//     * <AUTHOR> Sun
//     * @date : 2020/11/19
//     */
//    public static File transferToFile(MultipartFile multipartFile) throws Exception {
//        File toFile = null;
//        if (multipartFile.equals("") || multipartFile.getSize() <= 0) {
//            multipartFile = null;
//        } else {
//            InputStream ins = null;
//            ins = multipartFile.getInputStream();
//            toFile = new File(multipartFile.getOriginalFilename());
//            inputStreamToFile(ins, toFile);
//            ins.close();
//        }
//        return toFile;
//    }
//
//    //获取流文件
//    private static void inputStreamToFile(InputStream ins, File file) {
//        try {
//            OutputStream os = new FileOutputStream(file);
//            int bytesRead = 0;
//            byte[] buffer = new byte[8192];
//            while ((bytesRead = ins.read(buffer, 0, 8192)) != -1) {
//                os.write(buffer, 0, bytesRead);
//            }
//            os.close();
//            ins.close();
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//    }
//
//    /**
//     * 方法名称:腾讯COS上传文件方法
//     * <p>
//     * 功能描述:
//     * 〈腾讯COS上传文件方法〉
//     *
//     * @param multipartFile 文件名称
//     * @param path          文件路径/  分类路径 将照片进行分类
//     * @return : java.lang.String
//     * <AUTHOR> Sun
//     * @date : 2020/11/19
//     */
//    public String cosUploadFile(MultipartFile multipartFile, String path, Integer isOrigionFileName) {
//        String cosFileKey = getCosFileKey(multipartFile, path, isOrigionFileName);
//        //1.获取存储桶客户端
//        COSClient cosClient = getCosClient();
//        //2.获取对象key
////        String cosFileKey = TencentCosConfig.getCosFileKey(multipartFile,path);
//        try {
//            //3.将multipartFile转换成FILE类型
//            File localFile = transferToFile(multipartFile);
//            //4.创建存储对象的请求
//            PutObjectRequest putObjectRequest = new PutObjectRequest(tencentCosConfig.getCosBucketName(), cosFileKey, localFile);
//            //5.执行上传并返回结果信息
//            PutObjectResult putObjectResult = cosClient.putObject(putObjectRequest);
//        } catch (Exception e) {
//            System.out.println("-----上传文件出错-----" + e.getMessage());
//            e.printStackTrace();
//        } finally {
//            cosClient.shutdown();
//        }
//        return tencentCosConfig.getCosDomainUrl() + cosFileKey;
//    }
//
//
//    /**
//     * 方法名称:删除COS上的文件
//     * 功能描述:
//     * 〈〉
//     *
//     * @param filePathUrl 1
//     * @return : void
//     * <AUTHOR> Sun
//     * @date : 2020/11/19
//     */
//    public void cosDeleteFile(String filePathUrl) {
//        //1.获取存储桶客户端
//        COSClient cosClient = getCosClient();
//        try {
//            //2.指定要删除的 对象桶 和 对象key
//            String[] srr = filePathUrl.split(tencentCosConfig.getCosDomainUrl());
//            String key = srr[1];
//            cosClient.deleteObject(tencentCosConfig.getCosBucketName(), key);
//        } catch (Exception e) {
//            System.out.println("-----上传文件出错-----" + e.getMessage());
//            e.printStackTrace();
//        } finally {
//            cosClient.shutdown();
//        }
//    }
//}
//
