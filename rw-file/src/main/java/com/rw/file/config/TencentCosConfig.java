package com.rw.file.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

/**
 * @program: cos
 * @description: 存储桶信息配置类
 * @author: Sun
 * @create: 2020-11-19 15:32
 **/
@Component
@RefreshScope
@Data
public class TencentCosConfig {

    ////    //存储桶名称
    //@Value("${tencentcos.cosBucketName}")
    //private String cosBucketName;
    ////    //存储桶region名称
    //@Value("${tencentcos.cosRegionName}")
    //public String cosRegionName;
    ////    //存储桶密钥id
    //@Value("${tencentcos.cosSecretId}")
    //public String cosSecretId;
    ////    //存储桶密钥ley
    //@Value("${tencentcos.cosSecretKey}")
    //public String cosSecretKey;
    ////    //存储桶对象Key 前缀
    //@Value("${tencentcos.cosResorcePath}")
    //public String cosResorcePath;
    ////    //存储桶访问域名
    //@Value("${tencentcos.cosDomainUrl}")
    //public String cosDomainUrl;

    //    //存储桶名称
//    public static final String COS_BUCKET_NAME = "mintstechfile-1251122177";
//    //存储桶region名称
//    public static final String COS_REGION_NAME = "ap-beijing";
//    //存储桶密钥id
//    public static final String COS_SECRET_ID = "AKIDOfeMvVtFQN5iAONBm9HAKFvMcmiPr43q";
//    //存储桶密钥ley
//    public static final String COS_SECRET_KEY = "eHjBPO8dtuhEWgcJuJTO4WlnbBXfaGiA";
//    //存储桶对象Key 前缀
//    public static final String COS_RESORCE_PATH = "test/";
//    //存储桶访问域名
//    public static final String COS_DOMAIN_URL = "https://mintstechfile-1251122177.cos.ap-beijing.myqcloud.com/";

}
