<configuration scan="true" scanPeriod="60 seconds">
    <property name="log.path" value="logs"/>
    <springProperty scop="context" name="applicationName" source="spring.application.name" defaultValue=""/>

    <!-- Level: FATAL 0  ERROR 3  WARN 4  INFO 6  DEBUG 7 -->
    <springProfile name="dev">
        <!--        <property name="log.path" value="/workspace/log/${applicationName}"/>-->
        <logger name="com.rw" level="DEBUG"/>
    </springProfile>
    <springProfile name="test">
        <logger name="com.rw" level="INFO"/>
    </springProfile>
    <springProfile name="prod">
        <!--        <property name="log.path" value="/workspace/log/${applicationName}"/>-->
        <logger name="com.rw" level="INFO"/>
    </springProfile>

    <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${log.path}/log.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${log.path}/%d{yyyy-MM, aux}/error.%d{yyyy-MM-dd}.%i.log.gz</fileNamePattern>
            <maxFileSize>50MB</maxFileSize>
            <maxHistory>30</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>%date [%thread] %-5level [%logger{50}] %file:%line - %msg%n</pattern>
        </encoder>
    </appender>
    <!-- Console output -->
    <!-- 彩色日志格式 -->
    <property name="CONSOLE_LOG_PATTERN"
              value="${CONSOLE_LOG_PATTERN:-%clr(%d{yyyy-MM-dd HH:mm:ss.SSS}){faint} %clr(${LOG_LEVEL_PATTERN:-%5p}) %clr(${PID:- }){magenta} %clr(---){faint} %clr([%15.15t]){faint} %clr(%-40.40logger{39}){cyan} %clr(:){faint} %m%n${LOG_EXCEPTION_CONVERSION_WORD:-%wEx}}"/>
    <!-- 彩色日志依赖的渲染类 -->
    <conversionRule conversionWord="clr" converterClass="org.springframework.boot.logging.logback.ColorConverter"/>
    <conversionRule conversionWord="wex"
                    converterClass="org.springframework.boot.logging.logback.WhitespaceThrowableProxyConverter"/>
    <conversionRule conversionWord="wEx"
                    converterClass="org.springframework.boot.logging.logback.ExtendedWhitespaceThrowableProxyConverter"/>
    <!-- Console output -->
    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <!-- encoder defaults to ch.qos.logback.classic.encoder.PatternLayoutEncoder -->
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>${CONSOLE_LOG_PATTERN}</pattern>
            <charset>UTF-8</charset> <!-- 此处设置字符集 -->
        </encoder>
    </appender>

    <!--阿里云日志服务配置 测试环境-->
    <!--为了防止进程退出时，内存中的数据丢失，请加上此选项-->
    <shutdownHook class="ch.qos.logback.core.hook.DelayingShutdownHook"/>
    <appender name="ALIYUN_TEST" class="com.aliyun.openservices.log.logback.LoghubAppender">
        <!--必选项-->
        <!-- 账号及网络配置 -->
        <endpoint>cn-beijing-intranet.log.aliyuncs.com</endpoint>
        <accessKeyId>LTAIfiCvQTIkKSAh</accessKeyId>
        <accessKeySecret>IYzEAET27Dd83tgU7LFw4kHbQqkvwV</accessKeySecret>

        <!-- sls 项目配置 -->
        <project>rw-log-java</project>
        <logStore>test</logStore>
        <!--必选项 (end)-->

        <!-- 可选项 -->
        <topic>${applicationName}</topic>

        <!-- 可选项 详见 '参数说明'-->
        <totalSizeInBytes>10485760</totalSizeInBytes>
        <maxBlockMs>0</maxBlockMs>
        <ioThreadCount>8</ioThreadCount>
        <batchSizeThresholdInBytes>524288</batchSizeThresholdInBytes>
        <batchCountThreshold>4096</batchCountThreshold>
        <lingerMs>2000</lingerMs>
        <retries>1</retries>
        <baseRetryBackoffMs>100</baseRetryBackoffMs>
        <maxRetryBackoffMs>50000</maxRetryBackoffMs>
        <!-- 可选项 通过配置 encoder 的 pattern 自定义 log 的格式 -->
        <encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">
            <layout class="org.apache.skywalking.apm.toolkit.log.logback.v1.x.TraceIdPatternLogbackLayout">
                <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} %-5level [%thread] %logger{0}: [%tid] %msg%n</pattern>
            </layout>
        </encoder>
        <!-- 可选项 设置 time 字段呈现的格式 -->
        <timeFormat>yyyy-MM-dd'T'HH:mmZ</timeFormat>
        <!-- 可选项 设置 time 字段呈现的时区 -->
        <timeZone>Asia/Shanghai</timeZone>
    </appender>

    <!--阿里云日志服务配置 正式环境-->
    <appender name="ALIYUN_PROD" class="com.aliyun.openservices.log.logback.LoghubAppender">
        <!--必选项-->
        <!-- 账号及网络配置 -->
        <endpoint>cn-beijing-intranet.log.aliyuncs.com</endpoint>
        <accessKeyId>LTAIfiCvQTIkKSAh</accessKeyId>
        <accessKeySecret>IYzEAET27Dd83tgU7LFw4kHbQqkvwV</accessKeySecret>

        <!-- sls 项目配置 -->
        <project>rw-log-java</project>
        <logStore>prod</logStore>
        <!--必选项 (end)-->

        <!-- 可选项 -->
        <topic>${applicationName}</topic>

        <!-- 可选项 详见 '参数说明'-->
        <totalSizeInBytes>10485760</totalSizeInBytes>
        <maxBlockMs>0</maxBlockMs>
        <ioThreadCount>8</ioThreadCount>
        <batchSizeThresholdInBytes>524288</batchSizeThresholdInBytes>
        <batchCountThreshold>4096</batchCountThreshold>
        <lingerMs>2000</lingerMs>
        <retries>1</retries>
        <baseRetryBackoffMs>100</baseRetryBackoffMs>
        <maxRetryBackoffMs>50000</maxRetryBackoffMs>
        <!-- 可选项 通过配置 encoder 的 pattern 自定义 log 的格式 -->
        <encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">
            <layout class="org.apache.skywalking.apm.toolkit.log.logback.v1.x.TraceIdPatternLogbackLayout">
                <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} %-5level [%thread] %logger{0}: [%tid] %msg%n</pattern>
            </layout>
        </encoder>
        <!-- 可选项 设置 time 字段呈现的格式 -->
        <timeFormat>yyyy-MM-dd'T'HH:mmZ</timeFormat>
        <!-- 可选项 设置 time 字段呈现的时区 -->
        <timeZone>Asia/Shanghai</timeZone>
    </appender>

    <root level="INFO">
        <appender-ref ref="STDOUT"/>
        <!--        <appender-ref ref="FILE"/>-->
        <!--        <appender-ref ref="ALIYUN_TEST"/>-->
        <springProfile name="dev">
            <appender-ref ref="FILE"/>
        </springProfile>
        <springProfile name="test">
            <appender-ref ref="ALIYUN_TEST"/>
        </springProfile>
        <springProfile name="prod">
            <appender-ref ref="ALIYUN_PROD"/>
        </springProfile>
    </root>


</configuration>
