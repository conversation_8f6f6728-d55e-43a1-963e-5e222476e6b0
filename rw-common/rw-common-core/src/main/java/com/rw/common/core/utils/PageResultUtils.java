package com.rw.common.core.utils;

import com.baomidou.mybatisplus.core.metadata.IPage;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 分页工具类
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 20221121
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PageResultUtils<T> implements Serializable {
    private static final long serialVersionUID = 1L;
    //总记录数
    private int totalCount;
    //每页记录数
    private int pageSize;
    //总页数
    private int totalPage;
    //当前页数
    private int currPage;
    //列表数据
    private List<T> list;

    /**
     * 分页
     *
     * @param list       列表数据
     * @param totalCount 总记录数
     * @param pageSize   每页记录数
     * @param currPage   当前页数
     */
    public PageResultUtils(List<T> list, int totalCount, int pageSize, int currPage) {
        this.list = list;
        this.totalCount = totalCount;
        this.pageSize = pageSize;
        this.currPage = currPage;
        this.totalPage = (int) Math.ceil((double) totalCount / pageSize);
    }

    public PageResultUtils(IPage page) {
        this.list = page.getRecords();
        this.totalCount = (int) page.getTotal();
        this.pageSize =(int) page.getSize();
        this.currPage =(int) page.getCurrent();
        this.totalPage =(int) page.getPages();
    }


}
