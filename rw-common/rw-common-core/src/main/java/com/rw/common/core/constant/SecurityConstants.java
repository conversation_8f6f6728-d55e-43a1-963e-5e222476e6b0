package com.rw.common.core.constant;

/**
 * 权限相关通用常量
 * 
 * <AUTHOR>
 */
public class SecurityConstants
{
    /**
     * 用户ID字段
     */
    public static final String DETAILS_USER_ID = "user_id";

    /**
     * 用户名字段
     */
    public static final String DETAILS_USERNAME = "username";

    /**
     * 授权信息字段
     */
    public static final String AUTHORIZATION_HEADER = "authorization";

    /**
     * 请求来源
     */
    public static final String FROM_SOURCE = "from-source";

    /**
     * 内部请求
     */
    public static final String INNER = "inner";

    /**
     * 用户标识
     */
    public static final String USER_KEY = "user_key";

    /**
     * 登录用户
     */
    public static final String LOGIN_USER = "login_user";


    /**
     * 前端用户标识
     */
    public static final String USER_AUTH_API_KEY = "user_auth_api_key";
    /**
     * 前端用户标识
     */
    public static final String H5_USER_AUTH_API_KEY = "h5_user_auth_api_key";
    /**
     * 前端登录用户
     */
    public static final String LOGIN_AUTH_API_USER = "login_auth_api_user";



    /**
     * 商户用户标识
     */
    public static final String SH_USER_KEY = "sh_user_key";

//    public static final String H5_USER_KEY = "h5_user_key";

    /**
     * 商户登录用户
     */
    public static final String SH_LOGIN_USER = "sh_login_user";


    /**
     * 商户前端用户标识
     */
    public static final String SH_USER_AUTH_API_KEY = "sh_user_auth_api_key";


    /**
     * 商户前端登录用户
     */
    public static final String SH_LOGIN_AUTH_API_USER = "sh_login_auth_api_user";



}
