package com.rw.common.core.utils;


import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.rw.common.core.domain.R;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Component;

import java.util.Locale;

/**
 * @Author: douhaichao
 * @Date: 2021/7/20/020 22:14
 * @Desc: 构建通用返回工具类 包含统一国际化处理
 * 由于历史原因 兼容String 与 int 类型两种code
 * @See: 改类需要在SpringBoot启动时被扫包
 */
@Component
@Slf4j
public class ResultUtils {

    @Autowired
    private MessageSource messageSource;

    public String getMsg(String code) {
        if (messageSource != null) {
            return messageSource.getMessage(code, null, LocaleContextHolder.getLocale());
        }
        return "";
    }

    /**
     * 接收字符串类型的code
     *
     * @param code
     * @return
     */
    public R returnFailByCode(String code) {
        R r = new R();
        r.setCode(parseStrCodeToInt(code));
        if (messageSource != null) {
            r.setMsg(messageSource.getMessage(code, null, LocaleContextHolder.getLocale()));
        }
        return r;
    }

    /**
     * 接收字符串类型的code
     *
     * @param code
     * @return
     */
    public R returnFailByCodeWithData(String code, Object date) {
        R r = new R();
        r.setCode(parseStrCodeToInt(code));
        r.setData(date);
        if (messageSource != null) {
            r.setMsg(messageSource.getMessage(code, null, LocaleContextHolder.getLocale()));
        }
        return r;
    }

    /**
     * 接收数值类型的code
     *
     * @param code
     * @return
     */
    public R returnFailByCode(Integer code) {
        R r = new R();
        r.setCode(code);
        if (messageSource != null) {
            r.setMsg(messageSource.getMessage(String.valueOf(code), null, LocaleContextHolder.getLocale()));
        }
        return r;
    }

    /**
     * 接收错误码与多个需要替换
     *
     * @param code
     * @param objects
     * @return
     */
    public R returnFailCodeAndFormatMsg(String code, Object... objects) {
        R r = new R();
        r.setCode(Integer.parseInt(code));
        if (messageSource != null) {
            Locale locale = LocaleContextHolder.getLocale();
            String errorMsg = messageSource.getMessage(code, null, locale);
            if (StrUtil.isNotBlank(errorMsg)) {
                errorMsg = String.format(locale, errorMsg, objects);
            }
            r.setMsg(errorMsg);
        }
        return r;
    }

    /**
     * 转换有可能超长的字符串错误码
     *
     * @param code
     * @return
     */
    public static Integer parseStrCodeToInt(String code) {
        Integer errorCode = -1;
        try {
            errorCode = Integer.parseInt(code);
        } catch (NumberFormatException e) {
            log.error("转换错误");
            //错误大于10位数 则只取前9位数
            if (StrUtil.isNotBlank(code) && code.length() >= 10) {
                code = code.substring(0, 8);
                errorCode = Integer.valueOf(code);
            }
        } catch (Exception e) {
            //TODO 异常处理
        }
        return errorCode;
    }

    /**
     * @return
     * @description: 返回翻译后的内容
     * @author: douhaichao
     */
    public String getMessage(String code, Object... objects) {
        if (messageSource != null) {
            Locale locale = LocaleContextHolder.getLocale();
            String errorMsg = messageSource.getMessage(code, null, locale);
            if (StrUtil.isNotBlank(errorMsg) && ObjectUtil.isNotEmpty(objects)) {
                errorMsg = String.format(locale, errorMsg, objects);
            }
            return errorMsg;
        }
        return code;
    }

}
