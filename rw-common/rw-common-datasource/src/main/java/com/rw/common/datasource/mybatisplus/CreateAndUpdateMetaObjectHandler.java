package com.rw.common.datasource.mybatisplus;

import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.rw.common.security.utils.SecurityUtils;
import org.apache.ibatis.reflection.MetaObject;
import org.springframework.util.ObjectUtils;

import java.util.Date;

/**
 * MP注入处理器
 *
 **/
public class CreateAndUpdateMetaObjectHandler implements MetaObjectHandler {
    
    @Override
    public void insertFill(MetaObject metaObject) {
        //根据属性名字设置要填充的值
        if (metaObject.hasGetter("createTime")) {
            if (ObjectUtils.isEmpty(metaObject.getValue("createTime"))) {
                this.setFieldValByName("createTime", new Date(), metaObject);
            }
        }
        if (metaObject.hasGetter("createBy")) {
            if (ObjectUtils.isEmpty(metaObject.getValue("createBy"))) {
                this.setFieldValByName("createBy", SecurityUtils.getNickname(), metaObject);
            }
        }
        if (metaObject.hasGetter("createId")) {
            if (ObjectUtils.isEmpty(metaObject.getValue("createId")) || Integer.parseInt(metaObject.getValue("createId").toString()) == 0) {
                this.setFieldValByName("createId", SecurityUtils.getUserId(), metaObject);
            }
        }
    }
    
    @Override
    public void updateFill(MetaObject metaObject) {
        if (metaObject.hasGetter("updateBy")) {
            if (ObjectUtils.isEmpty(metaObject.getValue("updateBy"))) {
                this.setFieldValByName("updateBy", SecurityUtils.getNickname(), metaObject);
            }
        }
        if (metaObject.hasGetter("updateTime")) {
            if (ObjectUtils.isEmpty(metaObject.getValue("updateTime"))) {
                this.setFieldValByName("updateTime", new Date(), metaObject);
            }
        }
        if (metaObject.hasGetter("updateId")) {
            if (ObjectUtils.isEmpty(metaObject.getValue("updateId")) || Integer.parseInt(metaObject.getValue("updateId").toString())== 0) {
                this.setFieldValByName("updateId", SecurityUtils.getUserId(), metaObject);
            }
        }
    }
}
