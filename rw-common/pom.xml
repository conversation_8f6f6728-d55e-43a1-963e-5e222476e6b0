<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.rw.pom</groupId>
        <artifactId>rw-version-tag</artifactId>
        <version>1.0.0-SNAPSHOT</version>
        <relativePath>../pom/pom.xml</relativePath>
    </parent>
    <properties>
    </properties>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>rw-common</artifactId>
    <groupId>com.rw</groupId>
    <version>1.0.0-SNAPSHOT</version>
    <packaging>pom</packaging>
    <modules>
        <module>rw-api-system</module>
        <module>rw-common-core</module>
        <module>rw-common-datascope</module>
        <module>rw-common-datasource</module>
        <module>rw-common-log</module>
        <module>rw-common-redis</module>
        <module>rw-common-security</module>
        <module>rw-common-swagger</module>
    </modules>
    <dependencies>

    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
            </plugin>
        </plugins>
    </build>
</project>