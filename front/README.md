## 开发

```bash
# 克隆项目
git clone https://codeup.aliyun.com/626683c1c2b7347ce520f0bb/front/bos.git

# 进入项目目录
cd bos

# 安装依赖
npm install

# 建议不要直接使用 cnpm 安装依赖，会有各种诡异的 bug。可以通过如下操作解决 npm 下载速度慢的问题
npm install --registry=https://registry.npmmirror.com

# 启动服务
npm run dev
```

浏览器访问 http://localhost:80

## 发布

```bash
# 构建测试环境
npm run build:stage

# 构建生产环境
npm run build:prod
```
### 前端获取枚举下拉值接口
    GET http://zhongfa.yn.asqy.net/prod-api/bu/common/getEnumList?className=枚举名称  
*例如：*

    GET http://localhost:15000/bu/common/getEnumList?className=ReceiveCheckStatusEnum

*返回值*

`[
{
"name": "未入库",
"id": 0
},
{
"name": "已入库",
"id": 1
}
]`

## 枚举对照表

| 枚举名称 | 含义                                 |
|------|------------------------------------|
| ReceiveTypeEnum  | 入库单 类型  1采购2调拨3送检返回4平台库存退货5返库6维修返库 |
| ReceiveStatusEnum  | 收货单 状态                             |
| ReceiveCheckStatusEnum  | 入库单 状态                             |
| OutOrderTypeEnum  | 出库单 类型                             |
| KeyParamCheckStatusEnum | 关键料关键参数检测 状态 0进行中 1已完成 2已取消        |
| ReserveOrderStatusEnum | 出库 要料 申请单 状态 0未完成 1已完成             |
|  CheckTypeEnum    | 检测类型  2代检 1 自检                     |
| CheckResultEnum | 检测结果 1合格 2不合格                      |
| WarehouseTypeEnum | 仓库类型 1陆地仓2平台仓3井仓                   |
| ReturnOrderStatusEnum | 返料单状态 枚举 0未完成 1已完成                 |
|ReturnOrderGoodsStatusEnum | 返料单明细状态枚举 0待处理1已入库2已退回3报障          |