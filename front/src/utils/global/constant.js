
export default {
  install(vue){



    /**-----------------------------------------产品----------------------------------------*/


    /**UPS*/
    vue.prototype.ups_ups = 1;
    /**电池*/
    vue.prototype.ups_battery= 2;
    /**电池柜*/
    vue.prototype.ups_cabinet= 68;
    /**电池线*/
    vue.prototype.ups_line= 69;


    /**EPS*/
    vue.prototype.eps_eps= 40;


    /**STS*/
    vue.prototype.room_sts= 50;
    /**PDU*/
    vue.prototype.room_pdu= 51;


    /**稳压器*/
    vue.prototype.single_voltage_regulator= 52;
    /**逆变器*/
    vue.prototype.single_inverter= 53;


    /**空调室内机*/
    vue.prototype.ari_indoor= 6;
    /**空调室外机*/
    vue.prototype.ari_outdoor= 5;
    /**气液管管理*/
    vue.prototype.ari_gas_liquid_tube= 24;
    /**水管管理*/
    vue.prototype.ari_water_pipe= 25;
    /**制冷剂管理*/
    vue.prototype.ari_refrigerant= 27;
    /**空调配件管理*/
    vue.prototype.ari_conditioning_accessories= 201;




    /**Ups配件*/
    vue.prototype.product_ups_accessory= 200;
    /**断路器*/
    vue.prototype.product_breaker= 4;
    /**柜体*/
    vue.prototype.product_cabinet= 15;
    /**仪表*/
    vue.prototype.product_instrument= 16;
    /**按钮和指示灯*/
    vue.prototype.product_buttons_and_indicators= 17;
    /**互感器*/
    vue.prototype.product_transformer_instrument= 18;
    /**浪涌保护*/
    vue.prototype.product_surge_protection= 19;
    /**铜排*/
    vue.prototype.product_copper_bar= 20;
    /**插座*/
    vue.prototype.product_socket= 21;
    /**配件*/
    vue.prototype.product_accessory= 22;
    /**ATS*/
    vue.prototype.product_ats= 23;
    /**摄像机管理*/
    vue.prototype.product_camera= 30;
    /**录像机管理*/
    vue.prototype.product_video_recorder= 31;
    /**交换机管理*/
    vue.prototype.product_network_switch= 32;
    /**传输类管理*/
    vue.prototype.product_transmission_type= 33;
    /**隔离开关*/
    vue.prototype.product_disconnector= 80;
    /**负荷开关*/
    vue.prototype.product_load_switch= 81;
    /**接触器*/
    vue.prototype.product_contactor= 82;
    /**熔断器*/
    vue.prototype.product_fuse= 83;
    /**继电器*/
    vue.prototype.product_relay= 84;
    /**断路器附件*/
    vue.prototype.product_circuit_breaker_accessories= 85;
    /**安防配件管理*/
    vue.prototype.product_security_accessories= 202;



    /**监控中心*/
    vue.prototype.pemonitor_monitoring_center= 70;
    /**配电系统*/
    vue.prototype.pemonitor_power_distribution_system= 71;
    /**UPS系统*/
    vue.prototype.pemonitor_ups_system= 72;
    /**空调系统*/
    vue.prototype.pemonitor_air_system= 73;
    /**漏水系统*/
    vue.prototype.pemonitor_leak_system= 74;
    /**温湿度检测*/
    vue.prototype.pemonitor_temperature_and_humidity_detection= 75;
    /**视频监控*/
    vue.prototype.pemonitor_video_surveillance= 76;
    /**门禁系统*/
    vue.prototype.pemonitor_access_control_system= 77;
    /**消防系统*/
    vue.prototype.pemonitor_fire_protection_system= 78;









/**-----------------------------新闻 -------------------------------------*/
    /**成功案例*/
    vue.prototype.success_stories=0;

    /**瑞物云百科*/
    vue.prototype.rw_wiki=2;

    /**瑞物官网新闻*/
    vue.prototype.official_website_news=3;




    /**-----------------------------导航 -------------------------------------*/
    /**瑞物官网*/
    vue.prototype.official_website_navigation=1;




    /**--------------------------方案----------------------------------*/
    /**配电柜*/
    vue.prototype.classif_power_distribution_cabinet=1
    /**UPS*/
    vue.prototype.classif_ups=2
    /**空调*/
    vue.prototype.classif_air=3
    /**安防*/
    vue.prototype.classif_security =4
    /**一级配电柜*/
    vue.prototype.classif_one_power_distribution_cabinet=5
    /**EPS*/
    vue.prototype.classif_eps=6
    /**动环*/
    vue.prototype.classif_dynamic_environment=7
    /**机房能源配置*/
    vue.prototype.classif_energy_allocation_in_computer_room=8


  }
}
