<template>
  <!-- 查看藏品详情 -->
  <div>
    <el-dialog title="藏品详情" :visible.sync="open" width="80%" append-to-body :before-close="cancel">
      <el-descriptions v-if="open">
        <el-descriptions-item label="藏品编号：">{{ opusattrInfoObj.codeShow }}</el-descriptions-item>
        <el-descriptions-item label="藏品名称：">{{ opusattrInfoObj.opusName }}</el-descriptions-item>
        <el-descriptions-item label="创作家：">{{ opusattrInfoObj.creatorName }}</el-descriptions-item>
        <el-descriptions-item label="收藏状态：">{{ opusattrInfoObj.statusRemark }}</el-descriptions-item>
        <el-descriptions-item label="区块链：">{{ opusattrInfoObj.blockChainPlatformRemark }}</el-descriptions-item>
        <el-descriptions-item label="区块链ID：">{{ opusattrInfoObj.nftId }}</el-descriptions-item>
        <el-descriptions-item label="上链状态：">{{ opusattrInfoObj.nftId > 0?'已上链':'未上链' }}</el-descriptions-item>
        <el-descriptions-item label="合约地址：">{{ opusattrInfoObj.contractAddress }}</el-descriptions-item>
      </el-descriptions>
      <p v-if="open" class="title">藏品流转记录</p>
      <el-table v-if="open" v-loading="loadingAlert2" :data="alertList2">
        <el-table-column label="持有人" align="center" prop="ownerMns" />
        <el-table-column label="交易哈希" align="center" prop="confluxAddr" />
        <el-table-column label="获得方式" align="center" prop="businessTypeRemark" />
        <el-table-column label="状态" align="center" prop="statusRemark" />
        <el-table-column label="记录时间" align="center" prop="confluxCompleteTime" />
      </el-table>
      <pagination v-show="alertTotal2>0" :total="alertTotal2" :page.sync="alertQueryParams2.pageNum"
        :limit.sync="alertQueryParams2.pageSize" @pagination="getListAlert2" />
    </el-dialog>
  </div>
</template>
<script>
  import {
    opusattrFlowList,
    opusattrInfo
  } from '@/api/productapi/opusAttr'

  export default {
    name: 'OpusAttr',
    props: {
      /* 开启 */
      open: {
        type: Boolean,
        default: false
      },
      id: {
        type: Number,
        default: 0
      }
    },
    data() {
      return {
        // 藏品详情
        opusattrInfoObj: {},
        loadingAlert2: true,
        alertTotal2: 0, // 藏品详情弹窗流转记录列表总条数
        // 编号藏品表格数据
        alertList2: [],
        // 弹出层标题
        // 查询参数
        alertQueryParams2: {
          pageNum: 1,
          pageSize: 10,
          opusAttrId: null
        }

      }
    },
    created() {
      opusattrInfo({
        id: this.id
      }).then(response => {
        this.opusattrInfoObj = response.data
      })
      this.getListAlert2(this.id)
    },
    methods: {
      // 取消按钮
      cancel() {
        this.$emit('closeDialog')
      },
      fmtStatus(num) {
        return ['待发放', '收藏中', '售卖中', '转赠中', '保留中'][num]
      },

      getListAlert2(id) {
        this.loadingAlert2 = true
        this.alertQueryParams2.opusAttrId = id
        opusattrFlowList(this.alertQueryParams2).then(response => {
          this.alertList2 = response.rows
          this.alertTotal2 = response.total
          this.loadingAlert2 = false
        })
      }
    }
  }

</script>
<style scoped lang="scss">
  .title {
    font-size: 16px;
    font-weight: bold;
    color: #333;
  }

</style>
