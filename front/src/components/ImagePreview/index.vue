<template>

  <div v-if="!showMultiple">
    <el-image :src="`${realSrc}`" fit="cover" :style="`width:${realWidth};height:${realHeight};`" :preview-src-list="realSrcList">
      <div slot="error" class="image-slot">
        <i class="el-icon-picture-outline" />
      </div>
    </el-image>
  </div>

  <div v-else-if="showMultiple">
    <el-image v-for="(imgUrl,index) in realSrc" :src="`${imgUrl}`" :initial-index="`${index}`" fit="cover" :style="`width:${realWidth};height:${realHeight};margin-right:${realSpacing}`" :preview-src-list="realSrcList">
      <div slot="error" class="image-slot">
        <i class="el-icon-picture-outline" />
      </div>
    </el-image>
  </div>

</template>

<script>
  export default {
    name: 'ImagePreview',
    props: {
      src: {
        type: String,
        required: true
      },
      width: {
        type: [Number, String],
        default: ''
      },
      height: {
        type: [Number, String],
        default: ''
      },
      showMultiple: {
        type: Boolean,
        default: false
      },
      spacing:{
        type:[Number,String],
        default:0
      }
    },
    computed: {
      realSrc() {
        let real_src = this.src.split(',')[0]
        if(this.showMultiple){
          real_src = this.src.split(',');
        }
        return real_src
      },
      realSrcList() {
        const real_src_list = this.src.split(',')
        const srcList = []
        real_src_list.forEach(item => {
          return srcList.push(item)
        })
        return srcList
      },
      realWidth() {
        return typeof this.width === 'string' ? this.width : `${this.width}px`
      },
      realHeight() {
        return typeof this.height === 'string' ? this.height : `${this.height}px`
      },
      realSpacing() {
        return typeof this.spacing === 'string' ? this.spacing : `${this.spacing}px`
      }
    }
  }

</script>

<style lang="scss" scoped>
  .el-image {
    border-radius: 5px;
    background-color: #ebeef5;
    box-shadow: 0 0 5px 1px #ccc;

    ::v-deep .el-image__inner {
      transition: all 0.3s;
      cursor: pointer;

      &:hover {
        transform: scale(1.2);
      }
    }

    ::v-deep .image-slot {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 100%;
      height: 100%;
      color: #909399;
      font-size: 30px;
    }
  }

</style>
