<template>
  <el-select
    v-model="initValue"
    placeholder="请选择部门"
    style="width: 100%"
    :disabled="disabled"
    clearable
    filterable
  >
    <el-option
      v-for="dict in dict.type.department_list"
      :key="dict.value"
      :label="dict.label"
      :value="dict.value"
    ></el-option>
  </el-select>
</template>

<script>
export default {
  dicts: ["department_list"],
  props: {
    value: [String, Number],
    disabled: Boolean,
  },
  computed: {
    initValue: {
      get() {
        return !this.value
          ? null
          : typeof this.value === "string"
          ? this.value
          : this.value + "";
      },
      set(val) {
        this.$emit("input", val);
      },
    },
  },
};
</script>

<style lang="scss" scoped></style>
