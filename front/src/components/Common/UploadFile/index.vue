<template>
  <div>
    <div v-if="value" class="wrap">
      <el-tooltip class="item" effect="dark" :content="value" placement="top">
        <!-- <div class="rows-1" :style="{ width: width }">{{ value }}</div> -->
        <!-- <el-button type="primary" @click="handleDownload(value)"
          >下载<i class="el-icon-download el-icon--right"></i
        ></el-button> -->
        <el-button-group>
          <el-button
            type="primary"
            size="mini"
            icon="el-icon-download"
            @click="handleDownload(value)"
            >下载</el-button
          >
          <el-button
            v-if="!disabled"
            type="primary"
            size="mini"
            icon="el-icon-delete"
            @click="handleClear"
          ></el-button>
        </el-button-group>
      </el-tooltip>
      <!-- <span class="el-icon-close" @click="handleClear"></span> -->
    </div>
    <template v-else>
      <span v-if="disabled">---</span>
      <el-upload
        v-else
        :data="params"
        :disabled="disabled"
        :headers="headers"
        :action="uploadFileUrl"
        :show-file-list="false"
        :on-success="uploadSuccess"
        :before-upload="beforeUpload"
        :on-error="onError"
      >
        <el-button size="mini" type="primary" :loading="loading">{{
          btnText
        }}</el-button>
      </el-upload>
    </template>
  </div>
</template>

<script>
import { getToken } from "@/utils/auth";

const fileType = {
  xlsx: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
  jpeg: "image/jpeg",
  png: "image/png",
};
export default {
  props: {
    width: {
      type: String,
      default: "100%",
    },
    value: {
      type: String,
    },
    btnText: {
      type: String,
      default: "点击上传",
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    url: {
      type: String,
      default: "file/uploadCommon",
    },
    params: {
      type: Object,
      default() {
        return { path: "head_img" };
      },
    },
    accept: {
      type: [String, Array],
      validator: function (value) {
        // 这个值必须匹配下列字符串中的一个
        return Object.keys(fileType).indexOf(value) !== -1;
      },
    },
  },
  data() {
    return {
      loading: false,
      headers: {
        Authorization: "Bearer " + getToken(),
      },
    };
  },
  computed: {
    uploadFileUrl() {
      return process.env.VUE_APP_BASE_API + this.url;
    },
  },
  methods: {
    handleDownload(path) {
      if (path) {
        window.open(path);
      }
    },
    beforeUpload(file) {
      let isRightType = true;
      let msg = this.accept;
      if (this.accept) {
        if (typeof this.accept === "string") {
          isRightType = fileType[this.accept] === file.type;
        } else {
          isRightType = this.accept.some(
            (item) => fileType[item] === file.type
          );
          msg = this.accept.join("/");
        }
      }

      if (!isRightType) {
        this.$message.error(`请上传${msg}的文件!`);
      } else {
        this.loading = true;
      }

      return isRightType;
    },
    onError() {
      this.loading = false;
      this.$message.error("上传失败");
    },
    uploadSuccess(res) {
      console.log("res", res);
      this.loading = false;

      if (res.code === 200) {
        this.$message.success("上传成功");
        this.$emit("success", res);
        this.$emit("input", res.data.url);
      } else {
        this.$message.error(res.msg || "上传失败");
      }
    },
    handleClear() {
      this.$emit("input", "");
    },
  },
};
</script>

<style lang="scss" scoped>
.wrap {
  display: flex;
  align-items: center;
}
.el-icon-close {
  cursor: pointer;
}
</style>