<template>
  <el-select
    ref="selectRef"
    v-model="initValue"
    placeholder="请选择平台"
    style="width: 100%"
    :disabled="disabled"
    clearable
    filterable
  >
    <el-option
      v-for="dict in list"
      :key="dict.id"
      :label="dict.platformName"
      :value="dict.id"
    ></el-option>
  </el-select>
</template>

<script>
import { listManagement } from "@/api/base/management";
export default {
  props: {
    value: [String, Number],
    disabled: Boolean,
  },
  data() {
    return {
      list: [],
    };
  },
  computed: {
    initValue: {
      get() {
        return this.value;
      },
      set(val) {
        if (val) {
          const res = this.list.find((e) => e.id === val);
          this.$emit("getObject", res);
        } else {
          this.$emit("getObject", {});
        }
        this.$emit("input", val);
      },
    },
  },
  methods: {
    getList() {
      listManagement().then((response) => {
        this.list = response.rows;
      });
    },
  },
  created() {
    this.getList();
  },
};
</script>

<style lang="scss" scoped></style>
