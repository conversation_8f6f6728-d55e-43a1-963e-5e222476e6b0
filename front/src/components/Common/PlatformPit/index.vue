<template>
  <treeselect
    style="display: flex"
    :value="selectValue"
    :options="list"
    :normalizer="normalizer"
    :disable-branch-nodes="true"
    :appendToBody="true"
    :disabled="disabled"
    @input="updateValue"
    z-index="2500"
    placeholder="选择物井号"
  >
  </treeselect>
</template>

<script>
import Emitter from "element-ui/src/mixins/emitter";
import { getPlatformPitTree } from "@/api/base/pit";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
export default {
  components: {
    Treeselect,
  },
  mixins: [Emitter],
  props: {
    value: {
      type: [Object],
      default: () => ({}),
    },
    disabled: Boolean,
    // warehouseList: {
    //   type: Array,
    // },
  },
  data() {
    return {
      list: [],
      allList: [],
    };
  },
  computed: {
    selectValue() {
      return this.value.pitWarehouseId ? "b" + this.value.pitWarehouseId : null;
    },
  },

  created() {
    this.getList();
  },

  methods: {
    updateValue(value) {
      if (value) {
        let row = this.list.reduce((prev, next) => {
          if (next.children) {
            let val = next.children.find((item) => item.id === value);
            if (val) {
              let platformWarehouseId = next.id ? next.id.slice(1) : "";
              let pitWarehouseId = val.id ? val.id.slice(1) : "";
              prev = {
                platformWarehouseId,
                platformWarehouseName: next.name,
                pitWarehouseId,
                pitWarehouseName: val.name,
                pitDocumentNo: val.documentNo,
              };
            }
          }

          return prev;
        }, null);
        if (row) {
          console.log("row", row);
          this.$emit("input", row);
          this.dispatch("ElFormItem", "el.form.change", [row]);
        }
      } else {
        this.$emit("input", {});
        this.dispatch("ElFormItem", "el.form.change", [{}]);
      }
    },
    getFormatData(data) {
      return data.map((item) => {
        let obj = {
          id: "p" + item.id,
          name: item.platformName,
          documentNo: item.documentNo,
        };
        if (item.listPit) {
          obj.children = item.listPit.map((row) => ({
            id: "b" + row.id,
            name: row.pitName,
            documentNo: row.documentNo,
          }));
        }
        return obj;
      });
    },
    /** 查询仓库管理列表 */
    getList() {
      getPlatformPitTree().then((res) => {
        if (res.code === 200 && res.data) {
          this.list = this.getFormatData(res.data);
        }
      });
    },
    normalizer(node) {
      if (node.isLeaf == "Y" && !node.children) {
        node["isDisabled"] = true;
      }

      if (node.children && !node.children.length) {
        delete node.children;
      }

      return {
        id: node.id,
        label: node.name,
        children: node.children,
      };
    },
  },
};
</script>
