<template>
  <el-select
    v-model="initValue"
    placeholder="请选择物料类型"
    style="width: 100%"
    :disabled="disabled"
    filterable
  >
    <el-option
      v-for="dict in dict.type.bus_material_type"
      :key="dict.value"
      :label="dict.label"
      :value="dict.value"
    ></el-option>
  </el-select>
</template>

<script>
export default {
  dicts: ["bus_material_type"],
  props: {
    value: [String, Number],
    disabled: Boolean,
  },
  computed: {
    initValue: {
      get() {
        return this.value;
      },
      set(val) {
        this.$emit("input", val);
      },
    },
  },
};
</script>

<style lang="scss" scoped></style>
