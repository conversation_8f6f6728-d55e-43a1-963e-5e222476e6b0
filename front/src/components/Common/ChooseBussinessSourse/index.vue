<template>
  <el-select
    v-model="initValue"
    placeholder="请选择作业井"
    style="width: 100%"
    :disabled="disabled"
    clearable
    filterable
  >
    <el-option
      v-for="dict in list"
      :key="dict.id"
      :label="dict.pitName"
      :value="dict.id+''"
    ></el-option>
  </el-select>
</template>

<script>
import { listPit } from "@/api/base/pit";
export default {
  props: {
    value: [String, Number],
    disabled: Boolean,
  },
  data() {
    return {
      list: [],
    };
  },
  computed: {
    initValue: {
      get() {
        return !this.value
          ? null
          : typeof this.value === "string"
          ? this.value
          : this.value + "";
      },
      set(val) {
        console.log('this.list',this.list,val)
        if (val) {
          const res = this.list.find((e) => e.id === val*1);
          console.log('res',res)
          this.$emit("getObject", res);
        }
        this.$emit("input", val);
      },
    },
  },
  methods: {
    getList() {
      listPit().then((response) => {
        this.list = response.rows;
      });
    },
  },
  created() {
    this.getList();
  },
};
</script>

<style lang="scss" scoped></style>
