<template>
  <el-dialog
    title="关键参数检测项"
    width="60%"
    :visible.sync="visible"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    append-to-body
    @opened="getList"
    @close="handleClose"
  >
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button @click="handleClose" size="mini">取消</el-button>
      </el-col>
      <el-col :span="1.5" v-if="!disabled">
        <el-button type="primary" @click="handleAdd" size="mini"
          >确定</el-button
        >
      </el-col>
    </el-row>
    <el-table
      :data="data"
      ref="multipleTable"
      v-loading="loading"
      @selection-change="selectChange"
    >
      <el-table-column label="序号" type="index" width="50"> </el-table-column>
      <el-table-column
        label="检查项"
        min-width="120"
        align="center"
        prop="label"
      />

      <el-table-column label="备注" align="center" prop="remark">
        <template slot-scope="scope">
          <click-form
            v-model="scope.row.remark"
            :disabled="disabled"
          ></click-form>
        </template>
      </el-table-column>
      <el-table-column
        type="selection"
        :selectable="() => !disabled"
        width="55"
        align="center"
        label-class-name="DisabledSelection"
      />
    </el-table>
  </el-dialog>
</template>

<script>
export default {
  dicts: ["param_key_list"],
  props: {
    value: Boolean,
    selection: {
      type: Array,
      default: () => [],
    },
    disabled: {
      type: Boolean,
      default() {
        return false;
      },
    },
  },
  data() {
    return {
      loading: false,
      data: [],
      selectedList: [],
    };
  },
  computed: {
    visible: {
      get() {
        return this.value;
      },
      set(val) {
        this.$emit("input", val);
      },
    },
  },

  methods: {
    handleAdd() {
      let { selectedList } = this;
      if (selectedList.length === 0) {
        this.$message.error("请选择物料");
        return;
      }
      this.$emit("handleAdd", selectedList);
      this.handleClose();
    },
    selectChange(selection) {
      this.selectedList = selection;
    },

    handleClose() {
      this.visible = false;
    },

    getList() {
      console.log("this", this.selection);
      this.$refs.multipleTable.clearSelection();
      this.data = this.dict.type.param_key_list;
      this.data.forEach((item) => {
        let row = this.selection.find(
          (select) =>
            select.checkItemId + "" === item.value ||
            select.project === item.value
        );
        if (row) {
          this.$set(item, "remark", row.checkItemDesc || row.remark);
          this.$nextTick(() => {
            this.$refs.multipleTable.toggleRowSelection(item);
          });
        } else {
          this.$set(item, "remark", "");
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
::v-deep .DisabledSelection .cell .el-checkbox__inner {
  display: none;
  position: relative;
}
::v-deep .DisabledSelection .cell:before {
  content: "已检查";
  position: absolute;
  right: 11px;
}
</style>
