<template>
  <div>
    <el-select
      ref="selectRef"
      v-model="initValue"
      placeholder="请选择基地"
      style="width: 100%"
      :disabled="disabled"
      clearable
      filterable
    >
      <el-option
        v-for="dict in dict.type.base_main"
        :key="dict.value"
        :label="dict.label"
        :value="dict.value * 1"
      ></el-option>
    </el-select>
  </div>
</template>

<script>
export default {
  dicts: ["base_main"],
  props: {
    value: [String, Number],
    disabled: Boolean,
  },
  computed: {
    initValue: {
      get() {
        return this.value ? this.value * 1 : null;
      },
      set(val) {
        if (val) {
          const res = this.dict.type.base_main.find((e) => e.value * 1 === val);
          this.$emit("getObject", res);
        }
        this.$emit("input", val);
      },
    },
  },
};
</script>

<style lang="scss" scoped></style>
