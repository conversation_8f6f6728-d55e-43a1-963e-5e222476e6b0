<template>
  <el-dialog
    title="请选择物料"
    width="90%"
    :visible.sync="visible"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    append-to-body
    @open="handleOpen"
    @close="handleClose"
  >
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      label-width="68px"
    >
      <el-form-item label="关键字搜索" label-width="200" prop="keyword">
        <el-input
          v-model="queryParams.keyword"
          placeholder="请输入物料编码、名称"
          style="width: 400px"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
        <el-button type="primary" @click="jumpGoods"
          >在物料管理中添加物料</el-button
        >
        <el-button @click="handleClose">取消</el-button>

        <el-button type="primary" @click="handleAdd">添加</el-button>
      </el-form-item>
    </el-form>
    <el-table
      :data="data"
      v-loading="loading"
      ref="multipleTable"
      @select="selectChange"
      @select-all="selectChange"
    >
      <el-table-column
        label="已检查"
        type="selection"
        width="55"
        align="center"
      />
      <el-table-column label="序号" type="index" width="50" align="center">
      </el-table-column>
      <el-table-column
        label="物料编码"
        min-width="120"
        align="center"
        prop="documentNo"
      />
      <el-table-column
        label="序列号(厂商）"
        min-width="120"
        align="center"
        prop="serialNumber"
      />
      <el-table-column
        label="物料名称"
        min-width="120"
        show-overflow-tooltip
        align="center"
        prop="goodsName"
      />
      <el-table-column
        label="描述"
        min-width="120"
        show-overflow-tooltip
        align="center"
        prop="specifications"
      />
      <el-table-column label="类型" align="center" prop="goodsType">
        <template slot-scope="{ row }">
          <dict-tag
            v-model="row.goodsType"
            :options="dict.type.bus_material_type"
          />
        </template>
      </el-table-column>
      <!-- <el-table-column label="单位" align="center" prop="unit" /> -->
      <el-table-column label="单位" align="center" prop="unit">
        <template slot-scope="{ row }">
          <dict-tag v-model="row.unit" :options="dict.type.bus_material_unit" />
        </template>
      </el-table-column>
      <el-table-column label="尺寸" align="center" prop="size" />
      <el-table-column label="长度" align="center" prop="lengths" />
      <el-table-column label="材质" align="center" prop="texture" />
      <el-table-column label="扣型" align="center" prop="buckleType" />
      <el-table-column label="外径" align="center" prop="externalDiameter" />
      <el-table-column
        label="温度级别"
        align="center"
        prop="temperatureLevel"
      />
      <el-table-column label="压力级别" align="center" prop="pressureLevel" />
      <el-table-column
        label="备注"
        min-width="120"
        show-overflow-tooltip
        align="center"
        prop="type"
      />
    </el-table>
    <pagination
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- <span slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取消</el-button>

      <el-button type="primary" @click="handleAdd">添加</el-button>
    </span> -->
  </el-dialog>
</template>

<script>
import { listGoods } from "@/api/base/goods";
export default {
  dicts: ["bus_material_type", "bus_material_unit"],
  props: {
    value: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      loading: false,
      visible: false,
      total: 0,
      data: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        keyword: null,
      },
      selectedList: [],
    };
  },
  watch: {
    value(value) {
      this.visible = value;
    },
    visible(value) {
      this.$emit("input", value);
    },
  },
  methods: {
    jumpGoods() {
      this.$emit("input", false);
      this.$router.push("/bussinessSourse/goods");
    },
    handleAdd() {
      let { selectedList } = this;
      if (selectedList.length === 0) {
        this.$message.error("请选择物料");
        return;
      }
      this.$emit("handleAdd", this.selectedList);
      this.$emit("input", false);
    },
    selectChange(selection) {
      console.log("selection", selection);
      this.selectedList = selection;
    },
    handleOpen() {
      this.queryParams.pageNum = 1;
      this.getList(true);
    },
    handleClose() {
      this.$emit("input", false);
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    setSelectedStatus() {
      console.log(" this.$refs.multipleTable", this.$refs.multipleTable);
      this.$refs.multipleTable.clearSelection();
      this.data.forEach((item) => {
        let isSelect = this.selectedList.some(
          (select) => select.id === item.id
        );
        console.log("this.selectedList", this.selectedList);
        if (isSelect) {
          this.$nextTick(() => {
            this.$refs.multipleTable.toggleRowSelection(item);
          });
        }
      });
    },
    getList(isInit) {
      this.loading = true;
      listGoods(this.queryParams).then((res) => {
        console.log("res", res);
        this.loading = false;
        if (isInit) {
          this.selectedList = [];
        }

        if (res.code === 200) {
          this.data = res.rows;
          this.total = res.total;
        } else {
          this.data = [];
          this.total = 0;
        }
        this.setSelectedStatus();
      });
    },
  },
};
</script>

<style lang="scss" scoped>
</style>
