<template>
  <treeselect
    :value="selectValue"
    :options="list"
    :normalizer="normalizer"
    :disable-branch-nodes="true"
    :appendToBody="true"
    :disabled="disabled"
    @input="updateValue"
    z-index="2500"
    placeholder="选择物料分类"
  >
  </treeselect>
</template>

<script>
import Emitter from "element-ui/src/mixins/emitter";
import { getCategory } from "@/api/base/goods";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
export default {
  components: {
    Treeselect,
  },
  mixins: [Emitter],
  props: {
    value: {
      type: [Object, String, Number],
      default: () => ({}),
    },
    categoryList: {
      type: Array,
    },
    disabled: Boolean,
  },
  data() {
    return {
      list: [],
    };
  },
  computed: {
    selectValue() {
      return this.valueType ? this.value.categoryId : this.value;
    },
    valueType() {
      return Object.prototype.toString.call(this.value) === "[object Object]";
    },
  },

  created() {
    if (!this.categoryList) {
      this.getList();
    } else {
      this.initList();
    }
  },

  methods: {
    initList() {
      this.list = this.categoryList;
    },
    findRow(categoryId) {
      let item;
      const findItem = (list) => {
        if (!item && list) {
          list.forEach((li) => {
            if (li.id === categoryId) {
              item = li;
            } else {
              findItem(li.children);
            }
          });
        }
      };
      findItem(this.list);
      return item;
    },
    updateValue(categoryId) {
      console.log("warehouseId,distence", categoryId);

      if (this.valueType) {
        let row = {};
        if (categoryId) {
          let item = this.findRow(categoryId);
          row = {
            categoryId: item.id,
            categoryName: item.name,
          };
        }
        this.$emit("input", row);
        this.dispatch("ElFormItem", "el.form.change", [row]);
      } else {
        this.$emit("input", categoryId);
        //  this.dispatch("ElFormItem", "el.form.change", [row]);
      }
    },
    /** 查询仓库管理列表 */
    getList() {
      getCategory().then((res) => {
        this.list = res.code === 200 ? res.data : [];
      });
    },
    normalizer(node) {
      if (node.isLeaf == "Y" && !node.children) {
        node["isDisabled"] = true;
      }

      if (node.children && !node.children.length) {
        delete node.children;
      }

      return {
        id: node.id,
        label: node.name,
        children: node.children,
      };
    },
  },
};
</script>

<style lang="scss" scoped>
::v-deep .vue-treeselect__control {
  height: 28px;

  .vue-treeselect__single-value {
    line-height: 28px;
    font-size: 12px;
    color: #606266;
  }

  .vue-treeselect__placeholder {
    line-height: 28px;
  }
}
// .vue-treeselect__control {
//   display: flex;
//   align-items: center;
// }
</style>
