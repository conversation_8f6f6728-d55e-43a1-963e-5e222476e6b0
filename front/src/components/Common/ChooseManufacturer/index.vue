<template>
  <el-select
    :value="value.manufacturerName"
    filterable
    clearable
    reserve-keyword
    placeholder="请输入关键词"
    style="width:100%;"
    @change="selectChange"
  >
    <el-option
      v-for="item in options"
      :key="item.id"
      :label="item.fullName"
      :value="item.id"
    >
    </el-option>
  </el-select>
</template>

<script>
import { listSupplier } from "@/api/base/supplier";
export default {
  props: {
    value: {
      type: [Object],
      default(){
        return {}
      }
    },
  },
  data() {
    return {
      loading: false,
      options: [],
    };
  },
  created() {
    console.log(this.value);
    this.remoteMethod();
  },
  methods: {
    selectChange(val) {
      let row = this.options.find((item) => item.id === val);
      let item = {
        manufacturerId: '',
        manufacturerName: '',
      };
      if (row) {
        item = {
          manufacturerId: row.id,
          manufacturerName: row.fullName,
        };
      }
      console.log("changechange", item);
      this.$emit("input", item);
    },
    remoteMethod(query, type) {
      // if (query !== "") {
      this.loading = true;
      let params = {
        pageSize: 1000,
        //  keyword: query
      };

      listSupplier(params)
        .then((res) => {
          this.loading = false;
          console.log("listSupplier", res);
          if (res.code === 200 && res.rows) {
            this.options = res.rows;
          } else {
            this.options = [];
          }
        })
        .catch((err) => {
          this.loading = false;
          this.options = [];
        });
      // } else {
      //   this.options = [];
      // }
    },
  },
};
</script>

<style lang="scss" scoped>
</style>