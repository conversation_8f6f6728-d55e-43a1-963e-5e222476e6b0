<template>
  <div>
    <el-input v-if="type === 'input'" v-model="initValue" size="mini" :disabled="disabled" :placeholder="placeholder"
      clearable />

    <el-select v-if="type === 'select'" v-model="initValue" size="mini" :disabled="disabled" :placeholder="placeholder"
      clearable filterable @change="selectChange">
      <el-option v-for="item in list" :key="item.value" :label="item.label" :value="item.value"></el-option>
    </el-select>
  </div>
</template>

<script>
export default {
  props: {
    value: [String, Number, Boolean],
    type: {
      type: String,
      default: "input",
    },
    list: {
      type: Array,
      default: () => [],
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    placeholder: String
  },
  computed: {
    initValue: {
      get() {
        return this.value;
      },
      set(val) {
        this.$emit("input", val);
      },
    },
  },
  methods: {
    selectChange(val) {
      this.$emit("change", val);
    },
  },
};
</script>

<style lang="scss" scoped>
</style>
