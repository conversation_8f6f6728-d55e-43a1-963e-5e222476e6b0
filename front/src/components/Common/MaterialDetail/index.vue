<template>
  <el-dialog
    title="物料详情"
    width="600"
    :visible.sync="visible"
    append-to-body
    @close="handleClose"
    @open="getList"
  >
    <el-descriptions title="基本信息">
      <el-descriptions-item label="物料名称" span="3">{{
        row.goodsName
      }}</el-descriptions-item>
      <el-descriptions-item label="序列号(厂商)" span="3">{{
        row.serialNumber
      }}</el-descriptions-item>

      <el-descriptions-item label="描述">{{
        row.specifications
      }}</el-descriptions-item>
      <el-descriptions-item label="类型">
        <dict-tag
          :options="dict.type.bus_material_type"
          :value="row.goodsType"
        />
        <!-- {{ row.goodsType }} -->
      </el-descriptions-item>
      <el-descriptions-item label="单位">
        <dict-tag v-model="row.unit" :options="dict.type.bus_material_unit"
      /></el-descriptions-item>

      <el-descriptions-item label="材质">{{
        row.texture
      }}</el-descriptions-item>
      <el-descriptions-item label="扣型">{{
        row.buckleType
      }}</el-descriptions-item>
    </el-descriptions>
    <el-descriptions title="度量信息">
      <el-descriptions-item label="尺寸">{{ row.unit }}</el-descriptions-item>
      <el-descriptions-item label="长度">{{
        row.lengths
      }}</el-descriptions-item>
      <el-descriptions-item label="外径">{{
        row.externalDiameter
      }}</el-descriptions-item>
      <el-descriptions-item label="温度级别">{{
        row.temperatureLevel
      }}</el-descriptions-item>
      <el-descriptions-item label="压力级别" span="2">{{
        row.pressureLevel
      }}</el-descriptions-item>
      <el-descriptions-item label="备注">{{ row.type }}</el-descriptions-item>
    </el-descriptions>

    <span slot="footer" class="dialog-footer">
      <el-button @click="handleClose">关闭</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { getGoods } from "@/api/base/goods";
export default {
  dicts: ["bus_material_type", "bus_material_unit"],
  props: {
    value: {
      type: Boolean,
      default: false,
    },
    id: {
      type: [String, Number],
    },
  },
  data() {
    return {
      loading: false,
      visible: false,
      row: {},
    };
  },
  watch: {
    value(value) {
      this.visible = value;
    },
    visible(value) {
      this.$emit("input", value);
    },
  },
  methods: {
    handleClose() {
      this.$emit("input", false);
    },
    getList() {
      this.loading = true;
      getGoods(this.id).then((res) => {
        this.loading = false;
        this.row = res.code === 200 && res.data ? res.data : {};
      });
    },
  },
};
</script>

<style lang="scss" scoped>
</style>