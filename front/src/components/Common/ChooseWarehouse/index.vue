<template>
  <el-select
    v-model="initValue"
    placeholder="请选择仓库"
    style="width: 100%"
    :disabled="disabled"
    clearable
    filterable
  >
    <el-option
      v-for="dict in list"
      :key="dict.id"
      :label="dict.warehouseName"
      :value="dict.id"
    ></el-option>
  </el-select>
</template>

<script>
import { listWarehouse } from "@/api/base/warehouse";
export default {
  props: {
    value: [String, Number],
    disabled: Boolean,
  },
  data() {
    return {
      list: [],
    };
  },
  computed: {
    initValue: {
      get() {
        return this.value;
      },
      set(val) {
        if (val) {
          const res = this.list.find((e) => e.id === val);
          this.$emit("getObject", res);
        }
        this.$emit("input", val);
      },
    },
  },
  methods: {
    getList() {
      listWarehouse().then((response) => {
        this.list = response.rows;
      });
    },
  },
  created() {
    this.getList();
  },
};
</script>

<style lang="scss" scoped></style>
