<template>
  <treeselect
    :disabled="disabled"
    :value="selectValue"
    :options="list"
    :normalizer="normalizer"
    :disable-branch-nodes="true"
    :appendToBody="true"
    @input="updateValue"
    z-index="2500"
    placeholder="请选择"
  >
  </treeselect>
</template>

<script>
import { listWarehouse } from "@/api/base/warehouse";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
export default {
  components: {
    Treeselect,
  },
  props: {
    value: {
      type: [Object],
      default: () => ({}),
    },
    warehouseList: {
      type: Array,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      list: [],
      allList: [],
    };
  },
  computed: {
    selectValue() {
      return this.value.warehouseId ? "w-" + this.value.warehouseId : null;
    },
  },

  created() {
    if (!this.warehouseList) {
      this.getList();
    } else {
      this.initList();
    }
  },

  methods: {
    initList() {
      this.allList = this.warehouseList;
      this.list = this.getFormatData(this.warehouseList);
      this.setDefaultVal();
    },
    updateValue(value) {
      console.log("instanceId", value);
      let warehouseId = value ? value.slice(2) : "";
      console.log("warehouseId", warehouseId);
      if (warehouseId) {
        let row = this.allList.find((item) => item.id === warehouseId * 1);
        console.log("row", row);
        if (row) {
          console.log("row", row);
          this.$emit("input", {
            ...this.value,
            warehouseName: row.warehouseName,
            warehouseId: row.id,
            terminusId: row.terminusId,
            terminusName: row.terminusName,
          });
        }
      }
    },
    getFormatData(list) {
      return list.reduce((prev, next) => {
        let isExist = prev.some((item) => item.id === "b-" + next.terminusId);
        if (!isExist) {
          let row = {
            pid: 0,
            id: "b-" + next.terminusId,
            name: next.terminusName,
          };
          row.children = list.reduce((prev1, next1) => {
            if ("b-" + next1.terminusId === row.id) {
              prev1.push({
                pid: row.id,
                id: "w-" + next1.id,
                name: next1.warehouseName,
              });
            }
            return prev1;
          }, []);
          prev.push(row);
        }
        return prev;
      }, []);
    },
    setDefaultVal() {
      console.log("this.value.warehouseId", this.list);
      if (!this.value.warehouseId) {
        let val = this.list[0]?.children[0]?.id;
        console.log("val", val);
        this.updateValue(val);
      }
    },
    /** 查询仓库管理列表 */
    getList() {
      listWarehouse({
        pageSize: 10000,
      }).then((res) => {
        if (res.code === 200 && res.rows) {
          this.allList = res.rows;
          this.list = this.getFormatData(res.rows);
          this.setDefaultVal();
        }
      });
    },
    normalizer(node) {
      if (node.isLeaf == "Y" && !node.children) {
        node["isDisabled"] = true;
      }

      if (node.children && !node.children.length) {
        delete node.children;
      }

      return {
        id: node.id,
        label: node.name,
        children: node.children,
      };
    },
  },
};
</script>

<style lang="scss" scoped>
::v-deep .vue-treeselect__control {
  height: 28px;

  .vue-treeselect__single-value {
    line-height: 28px;
    font-size: 12px;
    color: #606266;
  }

  .vue-treeselect__placeholder {
    line-height: 28px;
  }
}
</style>