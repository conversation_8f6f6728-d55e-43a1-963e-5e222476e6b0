<template>
  <nobr>
    <template v-for="(item, index) in options">
      <template v-if="values.includes(item.value)">
       {{ item.label }}
      </template>
    </template>
  </nobr>
</template>

<script>
export default {
  name: "DictTag2",
  props: {
    options: {
      type: Array,
      default: null,
    },
    value: [Number, String, Array],
  },
  computed: {
    values() {
      if (this.value !== null && typeof this.value !== 'undefined') {
        return Array.isArray(this.value) ? this.value : [String(this.value)];
      } else {
        return [];
      }
    },
  },
};
</script>

