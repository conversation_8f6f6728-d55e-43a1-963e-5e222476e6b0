import request from '@/utils/request'

// 查询新闻中心列表
export function listCenter(query) {
  return request({
    url: '/commonapi/news/list',
    method: 'get',
    params: query
  })
}

// 查询新闻中心详细
export function getCenter(id) {
  return request({
    url: '/commonapi/news/' + id,
    method: 'get'
  })
}

// 新增新闻中心
export function addCenter(data) {
  return request({
    url: '/commonapi/news',
    method: 'post',
    data: data
  })
}

// 修改新闻中心
export function updateCenter(data) {
  return request({
    url: '/commonapi/news',
    method: 'put',
    data: data
  })
}

// 删除新闻中心
export function delCenter(id) {
  return request({
    url: '/commonapi/news/' + id,
    method: 'delete'
  })
}
