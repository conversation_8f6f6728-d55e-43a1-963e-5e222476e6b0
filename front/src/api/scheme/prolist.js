import request from '@/utils/request'

// 查询列表
export function listProlist(query) {
  return request({
    url: '/productapi/prolist/list',
    method: 'get',
    params: query
  })
}

// 查询详细
export function getProlist(id) {
  return request({
    url: '/productapi/prolist/' + id,
    method: 'get'
  })
}

// 新增
export function addProlist(data) {
  return request({
    url: '/productapi/prolist',
    method: 'post',
    data: data
  })
}

// 修改
export function updateProlist(data) {
  return request({
    url: '/productapi/prolist',
    method: 'put',
    data: data
  })
}

// 删除
export function delProlist(id) {
  return request({
    url: '/productapi/prolist/' + id,
    method: 'delete'
  })
}
