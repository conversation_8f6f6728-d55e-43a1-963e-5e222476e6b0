import request from '@/utils/request'

// 查询列表
export function listBoxlist(query) {
  return request({
    url: '/productapi/boxlist/list',
    method: 'get',
    params: query
  })
}

// 查询详细
export function getBoxlist(id) {
  return request({
    url: '/productapi/boxlist/' + id,
    method: 'get'
  })
}

// 新增
export function addBoxlist(data) {
  return request({
    url: '/productapi/boxlist',
    method: 'post',
    data: data
  })
}

// 修改
export function updateBoxlist(data) {
  return request({
    url: '/productapi/boxlist',
    method: 'put',
    data: data
  })
}

// 删除
export function delBoxlist(id) {
  return request({
    url: '/productapi/boxlist/' + id,
    method: 'delete'
  })
}

// 审核通过
export function passed(id) {
  return request({
    url: '/productapi/boxlist/passed?id=' + id,
    method: 'delete'
  })
}

// 审核驳回
export function overrule(id) {
  return request({
    url: '/productapi/boxlist/overrule?id=' + id,
    method: 'delete'
  })
}
