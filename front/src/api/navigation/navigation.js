import request from '@/utils/request'

// 查询属性列表
export function list(query) {
  return request({
    url: '/commonapi/navigation/list',
    method: 'get',
    params: query
  })
}


// 新增
export function addAttr(data) {
  return request({
    url: '/commonapi/navigation',
    method: 'post',
    data: data
  })
}

// 修改
export function updateAttr(data) {
  return request({
    url: '/commonapi/navigation',
    method: 'put',
    data: data
  })
}




// 删除
export function delAttr(menuId) {
  return request({
    url: '/commonapi/navigation/' + menuId,
    method: 'delete'
  })
}





// 查询详细
export function getAttr(id) {
  return request({
    url: '/commonapi/navigation/' + id,
    method: 'get'
  })
}







