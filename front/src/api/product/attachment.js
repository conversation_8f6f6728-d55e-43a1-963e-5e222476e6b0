import request from '@/utils/request'

// 查询系列附件列表
export function listAttachment(query) {
  return request({
    url: '/productapi/attachment/list',
    method: 'get',
    params: query
  })
}

// 查询系列附件详细
export function getAttachment(id) {
  return request({
    url: '/productapi/attachment/' + id,
    method: 'get'
  })
}

// 新增系列附件
export function addAttachment(data) {
  return request({
    url: '/productapi/attachment',
    method: 'post',
    data: data
  })
}

// 修改系列附件
export function updateAttachment(data) {
  return request({
    url: '/productapi/attachment',
    method: 'put',
    data: data
  })
}

// 删除系列附件
export function delAttachment(id) {
  return request({
    url: '/productapi/attachment/' + id,
    method: 'delete'
  })
}
