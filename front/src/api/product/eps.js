import request from '@/utils/request'

// 查询列表
export function listEps(query) {
  return request({
    url: '/productapi/eps/list',
    method: 'get',
    params: query
  })
}

// 查询详细
export function getEps(id) {
  return request({
    url: '/productapi/eps/' + id,
    method: 'get'
  })
}

// 新增
export function addEps(data) {
  return request({
    url: '/productapi/eps',
    method: 'post',
    data: data
  })
}

// 修改
export function updateEps(data) {
  return request({
    url: '/productapi/eps',
    method: 'put',
    data: data
  })
}

// 删除
export function delEps(id) {
  return request({
    url: '/productapi/eps/' + id,
    method: 'delete'
  })
}

// 修改isshow状态
export function updateIsShow(data) {
  return request({
    url: '/productapi/eps/updateIsShow',
    method: 'post',
    data: data
  })
}
