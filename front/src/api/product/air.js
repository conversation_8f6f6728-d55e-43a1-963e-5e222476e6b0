import request from '@/utils/request'

// 查询列表
export function listAir(query) {
  return request({
    url: '/productapi/air/list',
    method: 'get',
    params: query
  })
}

// 查询详细
export function getAir(id) {
  return request({
    url: '/productapi/air/' + id,
    method: 'get'
  })
}

// 新增
export function addAir(data) {
  return request({
    url: '/productapi/air',
    method: 'post',
    data: data
  })
}

// 修改
export function updateAir(data) {
  return request({
    url: '/productapi/air',
    method: 'put',
    data: data
  })
}

// 删除
export function delAir(id) {
  return request({
    url: '/productapi/air/' + id,
    method: 'delete'
  })
}

// 修改isshow状态
export function updateIsShow(data) {
  return request({
    url: '/productapi/air/updateIsShow',
    method: 'post',
    data: data
  })
}
