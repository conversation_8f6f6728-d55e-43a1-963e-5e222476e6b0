import request from '@/utils/request'

// 查询属性列表
export function list(query) {
  return request({
    url: '/productapi/attr/list',
    method: 'get',
    params: query
  })
}

// 查询属性列表
export function listAll(query) {
  return request({
    url: '/productapi/attr/listAll',
    method: 'get',
    params: query
  })
}


// 新增
export function addAttr(data) {
  return request({
    url: '/productapi/attr',
    method: 'post',
    data: data
  })
}

// 修改
export function updateAttr(data) {
  return request({
    url: '/productapi/attr',
    method: 'put',
    data: data
  })
}




// 删除
export function delAttr(menuId) {
  return request({
    url: '/productapi/attr/' + menuId,
    method: 'delete'
  })
}





// 查询详细
export function getAttr(id) {
  return request({
    url: '/productapi/attr/' + id,
    method: 'get'
  })
}




// 根据mainClass获取所有属性并分组
export function getAllAttrGroupByMainClass(mainClass) {
  return request({
    url: '/productapi/attr/getAllAttrGroupByMainClass/' + mainClass,
    method: 'get'
  })
}



// 根据父ID获取所有属性
export function getAttrByFatherId(fatherId) {
  return request({
    url: '/productapi/attr/getAttrByFatherId/' + fatherId,
    method: 'get'
  })
}




// 根据父ID获取配件相关的所有属性 如UPS配件、配件、空调配件、安防配件      父ID为类别
export function getAccessoryRelatedAttrByFatherId(fatherId) {
  return request({
    url: '/productapi/attr/getAccessoryRelatedAttrByFatherId/' + fatherId,
    method: 'get'
  })
}








