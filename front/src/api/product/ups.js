import request from '@/utils/request'

// 查询UPS信息 包含 UPS管理 电池管理 电池柜管理 电池线管理列表
export function listUps(query) {
  return request({
    url: '/productapi/ups/list',
    method: 'get',
    params: query
  })
}

// 查询UPS信息 包含 UPS管理 电池管理 电池柜管理 电池线管理详细
export function getUps(id) {
  return request({
    url: '/productapi/ups/' + id,
    method: 'get'
  })
}

// 新增UPS信息 包含 UPS管理 电池管理 电池柜管理 电池线管理
export function addUps(data) {
  return request({
    url: '/productapi/ups',
    method: 'post',
    data: data
  })
}

// 修改UPS信息 包含 UPS管理 电池管理 电池柜管理 电池线管理
export function updateUps(data) {
  return request({
    url: '/productapi/ups',
    method: 'put',
    data: data
  })
}

// 删除UPS信息 包含 UPS管理 电池管理 电池柜管理 电池线管理
export function delUps(id) {
  return request({
    url: '/productapi/ups/' + id,
    method: 'delete'
  })
}


// 修改isshow状态
export function updateIsShow(data) {
  return request({
    url: '/productapi/ups/updateIsShow',
    method: 'post',
    data: data
  })
}
