import request from '@/utils/request'

// 查询列表
export function listSingle(query) {
  return request({
    url: '/productapi/single/list',
    method: 'get',
    params: query
  })
}

// 查询详细
export function getSingle(id) {
  return request({
    url: '/productapi/single/' + id,
    method: 'get'
  })
}

// 新增
export function addSingle(data) {
  return request({
    url: '/productapi/single',
    method: 'post',
    data: data
  })
}

// 修改
export function updateSingle(data) {
  return request({
    url: '/productapi/single',
    method: 'put',
    data: data
  })
}

// 删除
export function delSingle(id) {
  return request({
    url: '/productapi/single/' + id,
    method: 'delete'
  })
}


// 修改isshow状态
export function updateIsShow(data) {
  return request({
    url: '/productapi/single/updateIsShow',
    method: 'post',
    data: data
  })
}
