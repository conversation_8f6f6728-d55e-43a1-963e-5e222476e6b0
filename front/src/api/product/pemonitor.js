import request from '@/utils/request'

// 查询列表
export function listPemonitor(query) {
  return request({
    url: '/productapi/pemonitor/list',
    method: 'get',
    params: query
  })
}

// 查询详细
export function getPemonitor(id) {
  return request({
    url: '/productapi/pemonitor/' + id,
    method: 'get'
  })
}

// 新增
export function addPemonitor(data) {
  return request({
    url: '/productapi/pemonitor',
    method: 'post',
    data: data
  })
}

// 修改
export function updatePemonitor(data) {
  return request({
    url: '/productapi/pemonitor',
    method: 'put',
    data: data
  })
}

// 删除
export function delPemonitor(id) {
  return request({
    url: '/productapi/pemonitor/' + id,
    method: 'delete'
  })
}

// 修改isshow状态
export function updateIsShow(data) {
  return request({
    url: '/productapi/pemonitor/updateIsShow',
    method: 'post',
    data: data
  })
}
