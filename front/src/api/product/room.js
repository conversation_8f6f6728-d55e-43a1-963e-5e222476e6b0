import request from '@/utils/request'

// 查询列表
export function listRoom(query) {
  return request({
    url: '/productapi/room/list',
    method: 'get',
    params: query
  })
}

// 查询详细
export function getRoom(id) {
  return request({
    url: '/productapi/room/' + id,
    method: 'get'
  })
}

// 新增
export function addRoom(data) {
  return request({
    url: '/productapi/room',
    method: 'post',
    data: data
  })
}

// 修改
export function updateRoom(data) {
  return request({
    url: '/productapi/room',
    method: 'put',
    data: data
  })
}

// 删除
export function delRoom(id) {
  return request({
    url: '/productapi/room/' + id,
    method: 'delete'
  })
}

// 修改isshow状态
export function updateIsShow(data) {
  return request({
    url: '/productapi/room/updateIsShow',
    method: 'post',
    data: data
  })
}
