<template>
  <div class="app-container">
    <div id="editor" />
  </div>
</template>

<script>
  import E from 'wangeditor'
  var editor
  export default {
    name: 'Editor',
    data() {
      return {
        form: {
          content: 'editordemo'
        }
      }
    },
    created() {
      this.getEditor()
    },
    methods: {
      // 加载富文本
      getEditor() {
        this.$nextTick(() => {
          editor = new E('#editor')
          editor.config.uploadImgMaxSize = 15 * 1024 * 1024 // 2M
          editor.config.uploadImgMaxLength = 1
          editor.config.showLinkImg = false
          editor.create()
          editor.txt.html(this.form.content)
        })
      }

      // /** 导出按钮操作 */
      // handleExport() {
      //   this.download('commonapi/agreement/export', {
      //     ...this.queryParams
      //   }, `agreement_${new Date().getTime()}.xlsx`)
      // }
    }
  }

</script>
