<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">

      <el-form-item label="名称" prop="title">
        <el-input
          v-model="queryParams.title"
          placeholder="请输入名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item label="编号" prop="listNo">
        <el-input
          v-model="queryParams.listNo"
          placeholder="请输入编号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item label="品牌">
        <el-select v-model="queryParams.labelIdArray[0]" placeholder="品牌" clearable>
          <el-option v-for="item in allOption.brandLabel" :key="item.id" :label="item.name" :value="item.id" />
        </el-select>
      </el-form-item>

      <el-form-item label="容量">
        <el-select v-model="queryParams.labelIdArray[1]" placeholder="容量" clearable>
          <el-option v-for="item in allOption.capLabel" :key="item.id" :label="item.name" :value="item.id" />
        </el-select>
      </el-form-item>

      <el-form-item label="级别">
        <el-select v-model="queryParams.labelIdArray[2]" placeholder="级别" clearable>
          <el-option v-for="item in allOption.categoryLabel" :key="item.id" :label="item.name" :value="item.id" />
        </el-select>
      </el-form-item>

      <el-form-item label="回路个数">
        <el-select v-model="queryParams.labelIdArray[3]" placeholder="回路个数" clearable>
          <el-option v-for="item in allOption.waysLabel" :key="item.id" :label="item.name" :value="item.id" />
        </el-select>
      </el-form-item>

      <el-form-item label="特色">
        <el-select v-model="queryParams.labelIdArray[4]" placeholder="特色" clearable>
          <el-option v-for="item in allOption.otherLabel" :key="item.id" :label="item.name" :value="item.id" />
        </el-select>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['system:ups:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
        >修改</el-button>
      </el-col>
<!--      <el-col :span="1.5">-->
<!--        <el-button-->
<!--          type="danger"-->
<!--          plain-->
<!--          icon="el-icon-delete"-->
<!--          size="mini"-->
<!--          :disabled="multiple"-->
<!--          @click="handleDelete"-->
<!--          v-hasPermi="['system:ups:remove']"-->
<!--        >删除</el-button>-->
<!--      </el-col>-->
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="upsList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="编号" align="center" prop="listNo" />
      <el-table-column label="名称" align="center" prop="title" />
      <el-table-column label="作者" align="center" prop="author" />
      <el-table-column label="添加时间" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
          >修改</el-button>
<!--          <el-button-->
<!--            size="mini"-->
<!--            type="text"-->
<!--            icon="el-icon-delete"-->
<!--            @click="handleDelete(scope.row)"-->
<!--            v-hasPermi="['system:ups:remove']"-->
<!--          >删除</el-button>-->
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改 -->
    <el-dialog :title="title" :visible.sync="open" width="1200px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">

        <el-row>
          <el-col :span="24">
            <el-form-item label="柜体：">
              <div>
                <el-button
                  size="mini"
                  type="text"
                  icon="el-icon-plus"
                  @click="handleFormAdd(product_cabinet)"

                >添加</el-button>
              </div>
              <el-table :data="formProductCabinetFilter" :show-header="false">
                <el-table-column label="图片" align="center" prop="imgPath">
                  <template slot-scope="scope">
                    <ImagePreview :src="scope.row.imgPath" :width="60" :height="60"></ImagePreview>
                  </template>
                </el-table-column>
                <el-table-column label="名称" align="center" prop="productTitle" />
                <el-table-column label="价格" align="center" prop="price">
                  <template slot-scope="scope">
                    <span style="color: red;">￥{{scope.row.price}}</span><br/>
                    <span>总价￥{{scope.row.totalPrice}}</span>

                  </template>
                </el-table-column>
                <el-table-column label="数量" align="center" prop="num" width="180">
                  <template slot-scope="scope">
                    <el-input-number  v-model="scope.row.num" :min="1" label="数量" @change="productNumChange(scope.row)"></el-input-number>
                  </template>
                </el-table-column>
                <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
                  <template slot-scope="scope">
                    <el-button
                      size="mini"
                      type="text"
                      icon="el-icon-delete"
                      @click="handleFormDelete(scope.row)"

                    >删除</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="断路器：">
              <div>
              <el-button
                size="mini"
                type="text"
                icon="el-icon-plus"
                @click="handleFormAdd(product_breaker)"

              >添加</el-button>
            </div>
              <el-table :data="formProductBreakerFilter" :show-header="false">
                <el-table-column label="图片" align="center" prop="imgPath">
                  <template slot-scope="scope">
                    <ImagePreview :src="scope.row.imgPath" :width="60" :height="60"></ImagePreview>
                  </template>
                </el-table-column>
                <el-table-column label="名称" align="center" prop="productTitle" />
                <el-table-column label="价格" align="center" prop="price">
                  <template slot-scope="scope">
                    <span style="color: red;">￥{{scope.row.price}}</span><br/>
                    <span>总价￥{{scope.row.totalPrice}}</span>

                  </template>
                </el-table-column>
                <el-table-column label="数量" align="center" prop="num" width="180">
                  <template slot-scope="scope">
                    <el-input-number  v-model="scope.row.num" :min="1" label="数量" @change="productNumChange(scope.row)"></el-input-number>
                  </template>
                </el-table-column>
                <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
                  <template slot-scope="scope">
                    <el-button
                      size="mini"
                      type="text"
                      icon="el-icon-delete"
                      @click="handleFormDelete(scope.row)"

                    >删除</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="ATS开关：">
              <div>
              <el-button
                size="mini"
                type="text"
                icon="el-icon-plus"
                @click="handleFormAdd(product_ats)"

              >添加</el-button>
            </div>
              <el-table :data="formProductATSFilter" :show-header="false">
                <el-table-column label="图片" align="center" prop="imgPath">
                  <template slot-scope="scope">
                    <ImagePreview :src="scope.row.imgPath" :width="60" :height="60"></ImagePreview>
                  </template>
                </el-table-column>
                <el-table-column label="名称" align="center" prop="productTitle" />
                <el-table-column label="价格" align="center" prop="price">
                  <template slot-scope="scope">
                    <span style="color: red;">￥{{scope.row.price}}</span><br/>
                    <span>总价￥{{scope.row.totalPrice}}</span>

                  </template>
                </el-table-column>
                <el-table-column label="数量" align="center" prop="num" width="180">
                  <template slot-scope="scope">
                    <el-input-number  v-model="scope.row.num" :min="1" label="数量" @change="productNumChange(scope.row)"></el-input-number>
                  </template>
                </el-table-column>
                <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
                  <template slot-scope="scope">
                    <el-button
                      size="mini"
                      type="text"
                      icon="el-icon-delete"
                      @click="handleFormDelete(scope.row)"

                    >删除</el-button>
                  </template>
                </el-table-column>
              </el-table>


            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="互感器：">
              <div>
                <el-button
                  size="mini"
                  type="text"
                  icon="el-icon-plus"
                  @click="handleFormAdd(product_transformer_instrument)"

                >添加</el-button>
              </div>
              <el-table :data="formProductTransformerInstrumentFilter" :show-header="false">
                <el-table-column label="图片" align="center" prop="imgPath">
                  <template slot-scope="scope">
                    <ImagePreview :src="scope.row.imgPath" :width="60" :height="60"></ImagePreview>
                  </template>
                </el-table-column>
                <el-table-column label="名称" align="center" prop="productTitle" />
                <el-table-column label="价格" align="center" prop="price">
                  <template slot-scope="scope">
                    <span style="color: red;">￥{{scope.row.price}}</span><br/>
                    <span>总价￥{{scope.row.totalPrice}}</span>

                  </template>
                </el-table-column>
                <el-table-column label="数量" align="center" prop="num" width="180">
                  <template slot-scope="scope">
                    <el-input-number  v-model="scope.row.num" :min="1" label="数量" @change="productNumChange(scope.row)"></el-input-number>
                  </template>
                </el-table-column>
                <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
                  <template slot-scope="scope">
                    <el-button
                      size="mini"
                      type="text"
                      icon="el-icon-delete"
                      @click="handleFormDelete(scope.row)"

                    >删除</el-button>
                  </template>
                </el-table-column>
              </el-table>

            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="按钮与指示灯：">
              <div>
                <el-button
                  size="mini"
                  type="text"
                  icon="el-icon-plus"
                  @click="handleFormAdd(product_buttons_and_indicators)"

                >添加</el-button>
              </div>
              <el-table :data="formProductButtonsAndIndicatorsFilter" :show-header="false">
                <el-table-column label="图片" align="center" prop="imgPath">
                  <template slot-scope="scope">
                    <ImagePreview :src="scope.row.imgPath" :width="60" :height="60"></ImagePreview>
                  </template>
                </el-table-column>
                <el-table-column label="名称" align="center" prop="productTitle" />
                <el-table-column label="价格" align="center" prop="price">
                  <template slot-scope="scope">
                    <span style="color: red;">￥{{scope.row.price}}</span><br/>
                    <span>总价￥{{scope.row.totalPrice}}</span>

                  </template>
                </el-table-column>
                <el-table-column label="数量" align="center" prop="num" width="180">
                  <template slot-scope="scope">
                    <el-input-number  v-model="scope.row.num" :min="1" label="数量" @change="productNumChange(scope.row)"></el-input-number>
                  </template>
                </el-table-column>
                <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
                  <template slot-scope="scope">
                    <el-button
                      size="mini"
                      type="text"
                      icon="el-icon-delete"
                      @click="handleFormDelete(scope.row)"

                    >删除</el-button>
                  </template>
                </el-table-column>
              </el-table>


            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="仪表：">
              <div>
                <el-button
                  size="mini"
                  type="text"
                  icon="el-icon-plus"
                  @click="handleFormAdd(product_instrument)"

                >添加</el-button>
              </div>
              <el-table :data="formProductInstrumentFilter" :show-header="false">
                <el-table-column label="图片" align="center" prop="imgPath">
                  <template slot-scope="scope">
                    <ImagePreview :src="scope.row.imgPath" :width="60" :height="60"></ImagePreview>
                  </template>
                </el-table-column>
                <el-table-column label="名称" align="center" prop="productTitle" />
                <el-table-column label="价格" align="center" prop="price">
                  <template slot-scope="scope">
                    <span style="color: red;">￥{{scope.row.price}}</span><br/>
                    <span>总价￥{{scope.row.totalPrice}}</span>

                  </template>
                </el-table-column>
                <el-table-column label="数量" align="center" prop="num" width="180">
                  <template slot-scope="scope">
                    <el-input-number  v-model="scope.row.num" :min="1" label="数量" @change="productNumChange(scope.row)"></el-input-number>
                  </template>
                </el-table-column>
                <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
                  <template slot-scope="scope">
                    <el-button
                      size="mini"
                      type="text"
                      icon="el-icon-delete"
                      @click="handleFormDelete(scope.row)"

                    >删除</el-button>
                  </template>
                </el-table-column>
              </el-table>


            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="浪涌保护：">
              <div>
                <el-button
                  size="mini"
                  type="text"
                  icon="el-icon-plus"
                  @click="handleFormAdd(product_surge_protection)"

                >添加</el-button>
              </div>
              <el-table :data="formProductSurgeProtectionFilter" :show-header="false">
                <el-table-column label="图片" align="center" prop="imgPath">
                  <template slot-scope="scope">
                    <ImagePreview :src="scope.row.imgPath" :width="60" :height="60"></ImagePreview>
                  </template>
                </el-table-column>
                <el-table-column label="名称" align="center" prop="productTitle" />
                <el-table-column label="价格" align="center" prop="price">
                  <template slot-scope="scope">
                    <span style="color: red;">￥{{scope.row.price}}</span><br/>
                    <span>总价￥{{scope.row.totalPrice}}</span>

                  </template>
                </el-table-column>
                <el-table-column label="数量" align="center" prop="num" width="180">
                  <template slot-scope="scope">
                    <el-input-number  v-model="scope.row.num" :min="1" label="数量" @change="productNumChange(scope.row)"></el-input-number>
                  </template>
                </el-table-column>
                <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
                  <template slot-scope="scope">
                    <el-button
                      size="mini"
                      type="text"
                      icon="el-icon-delete"
                      @click="handleFormDelete(scope.row)"

                    >删除</el-button>
                  </template>
                </el-table-column>
              </el-table>


            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="插座：">
              <div>
                <el-button
                  size="mini"
                  type="text"
                  icon="el-icon-plus"
                  @click="handleFormAdd(product_socket)"

                >添加</el-button>
              </div>
              <el-table :data="formProductSocketFilter" :show-header="false">
                <el-table-column label="图片" align="center" prop="imgPath">
                  <template slot-scope="scope">
                    <ImagePreview :src="scope.row.imgPath" :width="60" :height="60"></ImagePreview>
                  </template>
                </el-table-column>
                <el-table-column label="名称" align="center" prop="productTitle" />
                <el-table-column label="价格" align="center" prop="price">
                  <template slot-scope="scope">
                    <span style="color: red;">￥{{scope.row.price}}</span><br/>
                    <span>总价￥{{scope.row.totalPrice}}</span>

                  </template>
                </el-table-column>
                <el-table-column label="数量" align="center" prop="num" width="180">
                  <template slot-scope="scope">
                    <el-input-number  v-model="scope.row.num" :min="1" label="数量" @change="productNumChange(scope.row)"></el-input-number>
                  </template>
                </el-table-column>
                <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
                  <template slot-scope="scope">
                    <el-button
                      size="mini"
                      type="text"
                      icon="el-icon-delete"
                      @click="handleFormDelete(scope.row)"

                    >删除</el-button>
                  </template>
                </el-table-column>
              </el-table>


            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="铜排：">
              <div>
                <el-button
                  size="mini"
                  type="text"
                  icon="el-icon-plus"
                  @click="handleFormAdd(product_copper_bar)"

                >添加</el-button>
              </div>
              <el-table :data="formProductCopperBarFilter" :show-header="false">
                <el-table-column label="图片" align="center" prop="imgPath">
                  <template slot-scope="scope">
                    <ImagePreview :src="scope.row.imgPath" :width="60" :height="60"></ImagePreview>
                  </template>
                </el-table-column>
                <el-table-column label="名称" align="center" prop="productTitle" />
                <el-table-column label="价格" align="center" prop="price">
                  <template slot-scope="scope">
                    <span style="color: red;">￥{{scope.row.price}}</span><br/>
                    <span>总价￥{{scope.row.totalPrice}}</span>

                  </template>
                </el-table-column>
                <el-table-column label="数量" align="center" prop="num" width="180">
                  <template slot-scope="scope">
                    <el-input-number  v-model="scope.row.num" :min="1" label="数量" @change="productNumChange(scope.row)"></el-input-number>
                  </template>
                </el-table-column>
                <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
                  <template slot-scope="scope">
                    <el-button
                      size="mini"
                      type="text"
                      icon="el-icon-delete"
                      @click="handleFormDelete(scope.row)"

                    >删除</el-button>
                  </template>
                </el-table-column>
              </el-table>


            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="配件：">
              <div>
                <el-button
                  size="mini"
                  type="text"
                  icon="el-icon-plus"
                  @click="handleFormAdd(product_accessory)"

                >添加</el-button>
              </div>
              <el-table :data="formProductAccessoryFilter" :show-header="false">
                <el-table-column label="图片" align="center" prop="imgPath">
                  <template slot-scope="scope">
                    <ImagePreview :src="scope.row.imgPath" :width="60" :height="60"></ImagePreview>
                  </template>
                </el-table-column>
                <el-table-column label="名称" align="center" prop="productTitle" />
                <el-table-column label="价格" align="center" prop="price">
                  <template slot-scope="scope">
                    <span style="color: red;">￥{{scope.row.price}}</span><br/>
                    <span>总价￥{{scope.row.totalPrice}}</span>

                  </template>
                </el-table-column>
                <el-table-column label="数量" align="center" prop="num" width="180">
                  <template slot-scope="scope">
                    <el-input-number  v-model="scope.row.num" :min="1" label="数量" @change="productNumChange(scope.row)"></el-input-number>
                  </template>
                </el-table-column>
                <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
                  <template slot-scope="scope">
                    <el-button
                      size="mini"
                      type="text"
                      icon="el-icon-delete"
                      @click="handleFormDelete(scope.row)"

                    >删除</el-button>
                  </template>
                </el-table-column>
              </el-table>


            </el-form-item>
          </el-col>
        </el-row>



        <el-row>
          <el-col :span="12">
            <el-form-item label="名称" prop="title">
              <el-input v-model="form.title" placeholder="请输入名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="名称(英文)" prop="titleEn">
              <el-input v-model="form.titleEn" placeholder="请输入名称(英文)" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="12">
            <el-form-item label="副标题" prop="subTitle">
              <el-input v-model="form.subTitle" placeholder="请输入副标题" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="副标题(英文)" prop="subTitleEn">
              <el-input v-model="form.subTitleEn" placeholder="请输入副标题(英文)" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="12">
            <el-form-item label="组装费" prop="mprice">
              <el-input v-model="form.mprice" placeholder="请输入组装费" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="辅料价格" prop="acprice">
              <el-input v-model="form.acprice" placeholder="请输入辅料价格" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="12">
            <el-form-item label="利润" prop="profit">
              <el-input v-model="form.profit" placeholder="请输入利润" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="税金" prop="tax">
              <el-input v-model="form.tax" placeholder="请输入税金" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="12">
            <el-form-item label="就位费" prop="handlingFee">
              <el-input v-model="form.handlingFee" placeholder="请输入就位费" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="安装费" prop="install">
              <el-input v-model="form.install" placeholder="请输入安装费" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="12">
            <el-form-item label="调试费" prop="debugFee">
              <el-input v-model="form.debugFee" placeholder="请输入调试费" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="运费" prop="freight">
              <el-input v-model="form.freight" placeholder="请输入运费" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="12">
            <el-form-item label="加钣金费用" prop="sheetMetalCost">
              <el-input v-model="form.sheetMetalCost" placeholder="请输入加钣金费用" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="面板费用" prop="panelCost">
              <el-input v-model="form.panelCost" placeholder="请输入面板费用" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="12">
            <el-form-item label="包装费" prop="packingFee">
              <el-input v-model="form.packingFee" placeholder="请输入包装费" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24">
            <el-form-item label="CAD上传" prop="cadPath">
              <FileUpload :limit="1" v-model="form.cadPath" :fileType="['dwg','dws','dwt','dxf']"></FileUpload>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24">
            <el-form-item label="PDF上传" prop="pdfPath">
              <FileUpload :limit="1" v-model="form.pdfPath" :fileType="['pdf']"></FileUpload>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24">
            <el-form-item label="压缩包上传" prop="rarPath">
              <FileUpload :limit="1" v-model="form.rarPath" :fileType="['7z','zip','rar']"></FileUpload>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24">
            <el-form-item label="图片" prop="imgPath">
              <ImageUpload v-model="form.imgPath" file-size="5" :limit="5"></ImageUpload>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24">
            <el-form-item label="品牌">
              <el-tag
                v-for="item in allOption.brandLabel"
                :key="item.id"
                :type="formSelectBrand.includes(item.id) ?'' : 'info'"
                @click="handleTagClick(item,'formSelectBrand')"
                style="margin: 5px;cursor: pointer;"
              >
                {{ item.name }}
              </el-tag>

            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24">
            <el-form-item label="容量">

              <el-tag
                v-for="item in allOption.capLabel"
                :key="item.id"
                :type="formSelectCap.includes(item.id) ?'' : 'info'"
                @click="handleTagClick(item,'formSelectCap')"
                style="margin: 5px;cursor: pointer;"
              >
                {{ item.name }}
              </el-tag>


            </el-form-item>
          </el-col>
        </el-row>


        <el-row>
          <el-col :span="24">
            <el-form-item label="柜体类型">

              <el-tag
                v-for="item in allOption.categoryLabel"
                :key="item.id"
                :type="formSelectCategory.includes(item.id) ?'' : 'info'"
                @click="handleTagClick(item,'formSelectCategory')"
                style="margin: 5px;cursor: pointer;"
              >
                {{ item.name }}
              </el-tag>


            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24">
            <el-form-item label="回路个数">

              <el-tag
                v-for="item in allOption.waysLabel"
                :key="item.id"
                :type="formSelectWays.includes(item.id) ?'' : 'info'"
                @click="handleTagClick(item,'formSelectWays')"
                style="margin: 5px;cursor: pointer;"
              >
                {{ item.name }}
              </el-tag>


            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24">
            <el-form-item label="功能">

              <el-tag
                v-for="item in allOption.functionLabel"
                :key="item.id"
                :type="formSelectFunction.includes(item.id) ?'' : 'info'"
                @click="handleTagClick(item,'formSelectFunction',true)"
                style="margin: 5px;cursor: pointer;"
              >
                {{ item.name }}
              </el-tag>

            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="应用">

              <el-tag
                v-for="item in allOption.otherLabel"
                :key="item.id"
                :type="formSelectOther.includes(item.id) ?'' : 'info'"
                @click="handleTagClick(item,'formSelectOther',true)"
                style="margin: 5px;cursor: pointer;"
              >
                {{ item.name }}
              </el-tag>

            </el-form-item>
          </el-col>
        </el-row>

      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>


    <el-dialog :visible.sync="formOpen" width="980px" append-to-body>
      <el-form label-width="120px">
        <el-tabs v-model="productQueryParams.mainClass.toString()" type="border-card" @tab-click="handleTabsClick">
          <el-tab-pane label="柜体"  :name="product_cabinet+''" >

            <el-form :model="productQueryParams" ref="productQueryForm1" size="small" :inline="true" label-width="68px">

              <el-form-item label="名称" prop="title">
                <el-input
                  v-model="productQueryParams.title"
                  placeholder="请输入名称"
                  clearable
                  @keyup.enter.native="handleQuery"
                />
              </el-form-item>


              <el-form-item label="类型" prop="series">
                <el-select v-model="productQueryParams.series" placeholder="类型" clearable>
                  <el-option v-for="item in productOption.series" :key="item.id" :label="item.title" :value="item.id" />
                </el-select>
              </el-form-item>



              <el-form-item label="品牌" prop="brand">
                <el-select v-model="productQueryParams.brand" placeholder="品牌" clearable>
                  <el-option v-for="item in productOption.brand" :key="item.id" :label="item.title" :value="item.id" />
                </el-select>
              </el-form-item>

              <el-form-item>
                <el-button type="primary" icon="el-icon-search" size="mini" @click="productHandleQuery">搜索</el-button>
                <el-button icon="el-icon-refresh" size="mini" @click="productResetQuery">重置</el-button>
              </el-form-item>
            </el-form>

            <el-table v-loading="loading" :data="productList"  :show-header="false">
              <el-table-column label="图片" align="center" width="150">
                <template slot-scope="scope">
                  <ImagePreview :src="scope.row.img" :width="60" :height="60"></ImagePreview>
                </template>
              </el-table-column>
              <el-table-column align="left">
                <template slot-scope="scope">
                  {{scope.row.title}}<br/>
                  柜体类型：{{scope.row.seriesTitle}} | 柜体尺寸：{{scope.row.scaleTitle}} | 颜色：{{scope.row.colorTitle}} | 品牌：{{scope.row.brandTitle}} | 生产状态：<dict-tag2 :options="dict.type.produce_state" :value="scope.row.produceState"/><br/>
                  发布时间：{{parseTime(scope.row.releaseTime, '{y}-{m}-{d}')}}<br/>
                </template>
              </el-table-column>
              <el-table-column label="操作" align="center"  width="150" class-name="small-padding fixed-width">
                <template slot-scope="scope">
                  <span style="color: red;">￥{{scope.row.price}}</span><br/>
                  <el-button
                    size="mini"
                    type="text"
                    icon="el-icon-plus"
                    @click="handleAddProduct(scope.row)"
                  >加入配置单</el-button>

                  <!--          <el-button-->
                  <!--            size="mini"-->
                  <!--            type="text"-->
                  <!--            icon="el-icon-delete"-->
                  <!--            @click="handleDelete(scope.row)"-->
                  <!--            v-hasPermi="['system:ups:remove']"-->
                  <!--          >删除</el-button>-->
                </template>
              </el-table-column>
            </el-table>


            <pagination
              v-show="productTotal>0"
              :total="productTotal"
              :page.sync="productQueryParams.pageNum"
              :limit.sync="productQueryParams.pageSize"
              @pagination="getProductList"
            />


          </el-tab-pane>
          <el-tab-pane label="断路器"  :name="product_breaker+''" >

            <el-form :model="productQueryParams" ref="productQueryForm2" size="small" :inline="true" label-width="68px">

              <el-form-item label="名称" prop="title">
                <el-input
                  v-model="productQueryParams.title"
                  placeholder="请输入名称"
                  clearable
                  @keyup.enter.native="handleQuery"
                />
              </el-form-item>

              <el-form-item label="产品类别" prop="material">
                <el-select v-model="productQueryParams.material" placeholder="产品类别" clearable>
                  <el-option v-for="item in productOption.material" :key="item.id" :label="item.title" :value="item.id" />
                </el-select>
              </el-form-item>

              <el-form-item label="品牌" prop="brand">
                <el-select v-model="productQueryParams.brand" placeholder="品牌" clearable>
                  <el-option v-for="item in productOption.brand" :key="item.id" :label="item.title" :value="item.id" />
                </el-select>
              </el-form-item>

              <el-form-item label="容量" prop="capacity">
                <el-select v-model="productQueryParams.capacity" placeholder="容量" clearable>
                  <el-option v-for="item in productOption.capacity" :key="item.id" :label="item.title" :value="item.id" />
                </el-select>
              </el-form-item>

              <el-form-item label="级数" prop="poles">
                <el-select v-model="productQueryParams.poles" placeholder="级数" clearable>
                  <el-option v-for="item in productOption.poles" :key="item.id" :label="item.title" :value="item.id" />
                </el-select>
              </el-form-item>

              <el-form-item label="电流类型" prop="airSwitchType">
                <el-select v-model="productQueryParams.airSwitchType" placeholder="电流类型" clearable>
                  <el-option v-for="item in productOption.air_switch_type" :key="item.id" :label="item.title" :value="item.id" />
                </el-select>
              </el-form-item>




              <el-form-item>
                <el-button type="primary" icon="el-icon-search" size="mini" @click="productHandleQuery">搜索</el-button>
                <el-button icon="el-icon-refresh" size="mini" @click="productResetQuery">重置</el-button>
              </el-form-item>
            </el-form>

            <el-table v-loading="loading" :data="productList"  :show-header="false">
              <el-table-column label="图片" align="center" width="150">
                <template slot-scope="scope">
                  <ImagePreview :src="scope.row.img" :width="60" :height="60"></ImagePreview>
                </template>
              </el-table-column>
              <el-table-column align="left">
                <template slot-scope="scope">
                  {{scope.row.title}}<br/>
                  品牌：{{scope.row.brandTitle}} | 系列：{{scope.row.seriesTitle}} | 级数：{{scope.row.polesTitle}} | 容量：{{scope.row.capacityTitle}} | 品类：{{scope.row.materialTitle}}<br/>
                  生产状态：<dict-tag2 :options="dict.type.produce_state" :value="scope.row.produceState"/> | 发布时间：{{parseTime(scope.row.releaseTime, '{y}-{m}-{d}')}}<br/>
                </template>
              </el-table-column>
              <el-table-column label="操作" align="center"  width="150" class-name="small-padding fixed-width">
                <template slot-scope="scope">
                  <span style="color: red;">￥{{scope.row.price}}</span><br/>
                  <el-button
                    size="mini"
                    type="text"
                    icon="el-icon-plus"
                    @click="handleAddProduct(scope.row)"
                  >加入配置单</el-button>

                  <!--          <el-button-->
                  <!--            size="mini"-->
                  <!--            type="text"-->
                  <!--            icon="el-icon-delete"-->
                  <!--            @click="handleDelete(scope.row)"-->
                  <!--            v-hasPermi="['system:ups:remove']"-->
                  <!--          >删除</el-button>-->
                </template>
              </el-table-column>
            </el-table>

            <pagination
              v-show="productTotal>0"
              :total="productTotal"
              :page.sync="productQueryParams.pageNum"
              :limit.sync="productQueryParams.pageSize"
              @pagination="getProductList"
            />


          </el-tab-pane>
          <el-tab-pane label="ATS开关"  :name="product_ats+''" >

            <el-form :model="productQueryParams" ref="productQueryForm3" size="small" :inline="true" label-width="68px">

              <el-form-item label="名称" prop="title">
                <el-input
                  v-model="productQueryParams.title"
                  placeholder="请输入名称"
                  clearable
                  @keyup.enter.native="handleQuery"
                />
              </el-form-item>


              <el-form-item label="品牌" prop="brand">
                <el-select v-model="productQueryParams.brand" placeholder="品牌" clearable>
                  <el-option v-for="item in productOption.brand" :key="item.id" :label="item.title" :value="item.id" />
                </el-select>
              </el-form-item>

              <el-form-item label="容量" prop="capacity">
                <el-select v-model="productQueryParams.capacity" placeholder="容量" clearable>
                  <el-option v-for="item in productOption.capacity" :key="item.id" :label="item.title" :value="item.id" />
                </el-select>
              </el-form-item>

              <el-form-item label="级数" prop="poles">
                <el-select v-model="productQueryParams.poles" placeholder="级数" clearable>
                  <el-option v-for="item in productOption.poles" :key="item.id" :label="item.title" :value="item.id" />
                </el-select>
              </el-form-item>


              <el-form-item>
                <el-button type="primary" icon="el-icon-search" size="mini" @click="productHandleQuery">搜索</el-button>
                <el-button icon="el-icon-refresh" size="mini" @click="productResetQuery">重置</el-button>
              </el-form-item>
            </el-form>
            <el-table v-loading="loading" :data="productList"  :show-header="false">
              <el-table-column label="图片" align="center" width="150">
                <template slot-scope="scope">
                  <ImagePreview :src="scope.row.img" :width="60" :height="60"></ImagePreview>
                </template>
              </el-table-column>
              <el-table-column align="left">
                <template slot-scope="scope">
                  {{scope.row.title}}<br/>
                  品牌：{{scope.row.brandTitle}} | 系列：{{scope.row.seriesTitle}} | 级数：{{scope.row.polesTitle}} | 容量：{{scope.row.capacityTitle}} | 等级：{{scope.row.protectionTitle}}<br/>
                  生产状态：<dict-tag2 :options="dict.type.produce_state" :value="scope.row.produceState"/> | 发布时间：{{parseTime(scope.row.releaseTime, '{y}-{m}-{d}')}}<br/>
                </template>
              </el-table-column>
              <el-table-column label="操作" align="center"  width="150" class-name="small-padding fixed-width">
                <template slot-scope="scope">
                  <span style="color: red;">￥{{scope.row.price}}</span><br/>
                  <el-button
                    size="mini"
                    type="text"
                    icon="el-icon-plus"
                    @click="handleAddProduct(scope.row)"
                  >加入配置单</el-button>

                  <!--          <el-button-->
                  <!--            size="mini"-->
                  <!--            type="text"-->
                  <!--            icon="el-icon-delete"-->
                  <!--            @click="handleDelete(scope.row)"-->
                  <!--            v-hasPermi="['system:ups:remove']"-->
                  <!--          >删除</el-button>-->
                </template>
              </el-table-column>
            </el-table>
            <pagination
              v-show="productTotal>0"
              :total="productTotal"
              :page.sync="productQueryParams.pageNum"
              :limit.sync="productQueryParams.pageSize"
              @pagination="getProductList"
            />


          </el-tab-pane>
          <el-tab-pane label="互感器"  :name="product_transformer_instrument+''" >

            <el-form :model="productQueryParams" ref="productQueryForm4" size="small" :inline="true" label-width="68px">

              <el-form-item label="名称" prop="title">
                <el-input
                  v-model="productQueryParams.title"
                  placeholder="请输入名称"
                  clearable
                  @keyup.enter.native="handleQuery"
                />
              </el-form-item>

              <el-form-item label="品牌" prop="brand">
                <el-select v-model="productQueryParams.brand" placeholder="品牌" clearable>
                  <el-option v-for="item in productOption.brand" :key="item.id" :label="item.title" :value="item.id" />
                </el-select>
              </el-form-item>

              <el-form-item>
                <el-button type="primary" icon="el-icon-search" size="mini" @click="productHandleQuery">搜索</el-button>
                <el-button icon="el-icon-refresh" size="mini" @click="productResetQuery">重置</el-button>
              </el-form-item>
            </el-form>
            <el-table v-loading="loading" :data="productList"  :show-header="false">
              <el-table-column label="图片" align="center" width="150">
                <template slot-scope="scope">
                  <ImagePreview :src="scope.row.img" :width="60" :height="60"></ImagePreview>
                </template>
              </el-table-column>
              <el-table-column align="left">
                <template slot-scope="scope">
                  {{scope.row.title}}<br/>
                  品牌：{{scope.row.brandTitle}} | 生产状态：<dict-tag2 :options="dict.type.produce_state" :value="scope.row.produceState"/> | 发布时间：{{parseTime(scope.row.releaseTime, '{y}-{m}-{d}')}}
                </template>
              </el-table-column>
              <el-table-column label="操作" align="center"  width="150" class-name="small-padding fixed-width">
                <template slot-scope="scope">
                  <span style="color: red;">￥{{scope.row.price}}</span><br/>
                  <el-button
                    size="mini"
                    type="text"
                    icon="el-icon-plus"
                    @click="handleAddProduct(scope.row)"
                  >加入配置单</el-button>

                  <!--          <el-button-->
                  <!--            size="mini"-->
                  <!--            type="text"-->
                  <!--            icon="el-icon-delete"-->
                  <!--            @click="handleDelete(scope.row)"-->
                  <!--            v-hasPermi="['system:ups:remove']"-->
                  <!--          >删除</el-button>-->
                </template>
              </el-table-column>
            </el-table>
            <pagination
              v-show="productTotal>0"
              :total="productTotal"
              :page.sync="productQueryParams.pageNum"
              :limit.sync="productQueryParams.pageSize"
              @pagination="getProductList"
            />


          </el-tab-pane>
          <el-tab-pane label="按钮与指示灯"  :name="product_buttons_and_indicators+''">

            <el-form :model="productQueryParams" ref="productQueryForm5" size="small" :inline="true" label-width="68px">

              <el-form-item label="名称" prop="title">
                <el-input
                  v-model="productQueryParams.title"
                  placeholder="请输入名称"
                  clearable
                  @keyup.enter.native="handleQuery"
                />
              </el-form-item>


              <el-form-item label="品牌" prop="brand">
                <el-select v-model="productQueryParams.brand" placeholder="品牌" clearable>
                  <el-option v-for="item in productOption.brand" :key="item.id" :label="item.title" :value="item.id" />
                </el-select>
              </el-form-item>

              <el-form-item label="产品类别" prop="material">
                <el-select v-model="productQueryParams.material" placeholder="产品类别" clearable>
                  <el-option v-for="item in productOption.material" :key="item.id" :label="item.title" :value="item.id" />
                </el-select>
              </el-form-item>





              <el-form-item>
                <el-button type="primary" icon="el-icon-search" size="mini" @click="productHandleQuery">搜索</el-button>
                <el-button icon="el-icon-refresh" size="mini" @click="productResetQuery">重置</el-button>
              </el-form-item>
            </el-form>
            <el-table v-loading="loading" :data="productList"  :show-header="false">
              <el-table-column label="图片" align="center" width="150">
                <template slot-scope="scope">
                  <ImagePreview :src="scope.row.img" :width="60" :height="60"></ImagePreview>
                </template>
              </el-table-column>
              <el-table-column align="left">
                <template slot-scope="scope">
                  {{scope.row.title}}<br/>
                  品牌：{{scope.row.brandTitle}} | 颜色：{{scope.row.colorTitle}} | 电压类型：{{scope.row.voltageSortTitle}} | 材质：{{scope.row.structureTitle}} | 生产状态：<dict-tag2 :options="dict.type.produce_state" :value="scope.row.produceState"/><br/>
                  发布时间：{{parseTime(scope.row.releaseTime, '{y}-{m}-{d}')}}<br/>
                </template>
              </el-table-column>
              <el-table-column label="操作" align="center"  width="150" class-name="small-padding fixed-width">
                <template slot-scope="scope">
                  <span style="color: red;">￥{{scope.row.price}}</span><br/>
                  <el-button
                    size="mini"
                    type="text"
                    icon="el-icon-plus"
                    @click="handleAddProduct(scope.row)"
                  >加入配置单</el-button>

                  <!--          <el-button-->
                  <!--            size="mini"-->
                  <!--            type="text"-->
                  <!--            icon="el-icon-delete"-->
                  <!--            @click="handleDelete(scope.row)"-->
                  <!--            v-hasPermi="['system:ups:remove']"-->
                  <!--          >删除</el-button>-->
                </template>
              </el-table-column>
            </el-table>

            <pagination
              v-show="productTotal>0"
              :total="productTotal"
              :page.sync="productQueryParams.pageNum"
              :limit.sync="productQueryParams.pageSize"
              @pagination="getProductList"
            />

          </el-tab-pane>
          <el-tab-pane label="仪表" :name="product_instrument+''">

            <el-form :model="productQueryParams" ref="productQueryForm6" size="small" :inline="true" label-width="68px">

              <el-form-item label="名称" prop="title">
                <el-input
                  v-model="productQueryParams.title"
                  placeholder="请输入名称"
                  clearable
                  @keyup.enter.native="handleQuery"
                />
              </el-form-item>

              <el-form-item label="仪表功能" prop="fun">
                <el-select v-model="productQueryParams.fun" placeholder="仪表功能" clearable>
                  <el-option v-for="item in productOption.fun" :key="item.id" :label="item.title" :value="item.id" />
                </el-select>
              </el-form-item>

              <el-form-item label="电压类型" prop="voltageSort">
                <el-select v-model="productQueryParams.voltageSort" placeholder="电压类型" clearable>
                  <el-option v-for="item in productOption.voltagesort" :key="item.id" :label="item.title" :value="item.id" />
                </el-select>
              </el-form-item>
              <el-form-item>
                <el-button type="primary" icon="el-icon-search" size="mini" @click="productHandleQuery">搜索</el-button>
                <el-button icon="el-icon-refresh" size="mini" @click="productResetQuery">重置</el-button>
              </el-form-item>
            </el-form>
            <el-table v-loading="loading" :data="productList"  :show-header="false">
              <el-table-column label="图片" align="center" width="150">
                <template slot-scope="scope">
                  <ImagePreview :src="scope.row.img" :width="60" :height="60"></ImagePreview>
                </template>
              </el-table-column>
              <el-table-column align="left">
                <template slot-scope="scope">
                  {{scope.row.title}}<br/>
                  品牌：{{scope.row.brandTitle}} | 功能：{{scope.row.funTitle}} | 电压类型：{{scope.row.voltageSortTitle}} | 监控接口：{{scope.row.monitorTitle}} | 生产状态：<dict-tag2 :options="dict.type.produce_state" :value="scope.row.produceState"/><br/>
                  发布时间：{{parseTime(scope.row.releaseTime, '{y}-{m}-{d}')}}<br/>
                </template>
              </el-table-column>
              <el-table-column label="操作" align="center"  width="150" class-name="small-padding fixed-width">
                <template slot-scope="scope">
                  <span style="color: red;">￥{{scope.row.price}}</span><br/>
                  <el-button
                    size="mini"
                    type="text"
                    icon="el-icon-plus"
                    @click="handleAddProduct(scope.row)"
                  >加入配置单</el-button>

                  <!--          <el-button-->
                  <!--            size="mini"-->
                  <!--            type="text"-->
                  <!--            icon="el-icon-delete"-->
                  <!--            @click="handleDelete(scope.row)"-->
                  <!--            v-hasPermi="['system:ups:remove']"-->
                  <!--          >删除</el-button>-->
                </template>
              </el-table-column>
            </el-table>
            <pagination
              v-show="productTotal>0"
              :total="productTotal"
              :page.sync="productQueryParams.pageNum"
              :limit.sync="productQueryParams.pageSize"
              @pagination="getProductList"
            />

          </el-tab-pane>
          <el-tab-pane label="浪涌保护" :name="product_surge_protection+''">

            <el-form :model="productQueryParams" ref="productQueryForm7" size="small" :inline="true" label-width="68px">

              <el-form-item label="名称" prop="title">
                <el-input
                  v-model="productQueryParams.title"
                  placeholder="请输入名称"
                  clearable
                  @keyup.enter.native="handleQuery"
                />
              </el-form-item>

              <el-form-item label="放电电流" prop="circuit">
                <el-select v-model="productQueryParams.circuit" placeholder="放电电流" clearable>
                  <el-option v-for="item in productOption.circuit" :key="item.id" :label="item.title" :value="item.id" />
                </el-select>
              </el-form-item>

              <el-form-item label="品牌" prop="brand">
                <el-select v-model="productQueryParams.brand" placeholder="品牌" clearable>
                  <el-option v-for="item in productOption.brand" :key="item.id" :label="item.title" :value="item.id" />
                </el-select>
              </el-form-item>

              <el-form-item label="级数" prop="poles">
                <el-select v-model="productQueryParams.poles" placeholder="级数" clearable>
                  <el-option v-for="item in productOption.poles" :key="item.id" :label="item.title" :value="item.id" />
                </el-select>
              </el-form-item>
              <el-form-item>
                <el-button type="primary" icon="el-icon-search" size="mini" @click="productHandleQuery">搜索</el-button>
                <el-button icon="el-icon-refresh" size="mini" @click="productResetQuery">重置</el-button>
              </el-form-item>
            </el-form>
            <el-table v-loading="loading" :data="productList"  :show-header="false">
              <el-table-column label="图片" align="center" width="150">
                <template slot-scope="scope">
                  <ImagePreview :src="scope.row.img" :width="60" :height="60"></ImagePreview>
                </template>
              </el-table-column>
              <el-table-column align="left">
                <template slot-scope="scope">
                  {{scope.row.title}}<br/>
                  品牌：{{scope.row.brandTitle}} | 最大放电电流：{{scope.row.circuitTitle}} | 级数：{{scope.row.polesTitle}} | 安装方式：{{scope.row.methodTitle}} | 生产状态：<dict-tag2 :options="dict.type.produce_state" :value="scope.row.produceState"/><br/>
                  发布时间：{{parseTime(scope.row.releaseTime, '{y}-{m}-{d}')}}<br/>
                </template>
              </el-table-column>
              <el-table-column label="操作" align="center"  width="150" class-name="small-padding fixed-width">
                <template slot-scope="scope">
                  <span style="color: red;">￥{{scope.row.price}}</span><br/>
                  <el-button
                    size="mini"
                    type="text"
                    icon="el-icon-plus"
                    @click="handleAddProduct(scope.row)"
                  >加入配置单</el-button>

                  <!--          <el-button-->
                  <!--            size="mini"-->
                  <!--            type="text"-->
                  <!--            icon="el-icon-delete"-->
                  <!--            @click="handleDelete(scope.row)"-->
                  <!--            v-hasPermi="['system:ups:remove']"-->
                  <!--          >删除</el-button>-->
                </template>
              </el-table-column>
            </el-table>
            <pagination
              v-show="productTotal>0"
              :total="productTotal"
              :page.sync="productQueryParams.pageNum"
              :limit.sync="productQueryParams.pageSize"
              @pagination="getProductList"
            />

          </el-tab-pane>
          <el-tab-pane label="插座":name="product_socket+''">

            <el-form :model="productQueryParams" ref="productQueryForm8" size="small" :inline="true" label-width="68px">

              <el-form-item label="名称" prop="title">
                <el-input
                  v-model="productQueryParams.title"
                  placeholder="请输入名称"
                  clearable
                  @keyup.enter.native="handleQuery"
                />
              </el-form-item>


              <el-form-item label="品牌" prop="brand">
                <el-select v-model="productQueryParams.brand" placeholder="品牌" clearable>
                  <el-option v-for="item in productOption.brand" :key="item.id" :label="item.title" :value="item.id" />
                </el-select>
              </el-form-item>

              <el-form-item label="类型" prop="method">
                <el-select v-model="productQueryParams.method" placeholder="类型" clearable>
                  <el-option v-for="item in productOption.method" :key="item.id" :label="item.title" :value="item.id" />
                </el-select>
              </el-form-item>

              <el-form-item label="孔数" prop="channels">
                <el-select v-model="productQueryParams.channels" placeholder="孔数" clearable>
                  <el-option v-for="item in productOption.channels" :key="item.id" :label="item.title" :value="item.id" />
                </el-select>
              </el-form-item>

              <el-form-item label="额定电流" prop="circuit">
                <el-select v-model="productQueryParams.circuit" placeholder="额定电流" clearable>
                  <el-option v-for="item in productOption.circuit" :key="item.id" :label="item.title" :value="item.id" />
                </el-select>
              </el-form-item>

              <el-form-item>
                <el-button type="primary" icon="el-icon-search" size="mini" @click="productHandleQuery">搜索</el-button>
                <el-button icon="el-icon-refresh" size="mini" @click="productResetQuery">重置</el-button>
              </el-form-item>
            </el-form>
            <el-table v-loading="loading" :data="productList"  :show-header="false">
              <el-table-column label="图片" align="center" width="150">
                <template slot-scope="scope">
                  <ImagePreview :src="scope.row.img" :width="60" :height="60"></ImagePreview>
                </template>
              </el-table-column>
              <el-table-column align="left">
                <template slot-scope="scope">
                  {{scope.row.title}}<br/>
                  类型：{{scope.row.methodTitle}} | 孔数：{{scope.row.channelsTitle}} | 额定电流：{{scope.row.circuitTitle}} | 品牌：{{scope.row.brandTitle}} | 生产状态：<dict-tag2 :options="dict.type.produce_state" :value="scope.row.produceState"/><br/>
                  发布时间：{{parseTime(scope.row.releaseTime, '{y}-{m}-{d}')}}<br/>
                </template>
              </el-table-column>
              <el-table-column label="操作" align="center"  width="150" class-name="small-padding fixed-width">
                <template slot-scope="scope">
                  <span style="color: red;">￥{{scope.row.price}}</span><br/>
                  <el-button
                    size="mini"
                    type="text"
                    icon="el-icon-plus"
                    @click="handleAddProduct(scope.row)"
                  >加入配置单</el-button>

                  <!--          <el-button-->
                  <!--            size="mini"-->
                  <!--            type="text"-->
                  <!--            icon="el-icon-delete"-->
                  <!--            @click="handleDelete(scope.row)"-->
                  <!--            v-hasPermi="['system:ups:remove']"-->
                  <!--          >删除</el-button>-->
                </template>
              </el-table-column>
            </el-table>
            <pagination
              v-show="productTotal>0"
              :total="productTotal"
              :page.sync="productQueryParams.pageNum"
              :limit.sync="productQueryParams.pageSize"
              @pagination="getProductList"
            />

          </el-tab-pane>
          <el-tab-pane label="铜排":name="product_copper_bar+''">

            <el-form :model="productQueryParams" ref="productQueryForm9" size="small" :inline="true" label-width="68px">

              <el-form-item label="名称" prop="title">
                <el-input
                  v-model="productQueryParams.title"
                  placeholder="请输入名称"
                  clearable
                  @keyup.enter.native="handleQuery"
                />
              </el-form-item>


              <el-form-item>
                <el-button type="primary" icon="el-icon-search" size="mini" @click="productHandleQuery">搜索</el-button>
                <el-button icon="el-icon-refresh" size="mini" @click="productResetQuery">重置</el-button>
              </el-form-item>
            </el-form>
            <el-table v-loading="loading" :data="productList"  :show-header="false">
              <el-table-column label="图片" align="center" width="150">
                <template slot-scope="scope">
                  <ImagePreview :src="scope.row.img" :width="60" :height="60"></ImagePreview>
                </template>
              </el-table-column>
              <el-table-column align="left">
                <template slot-scope="scope">
                  {{scope.row.title}}<br/>
                  载流量：{{scope.row.carrying}} | 厚度：{{scope.row.thickness}} | 宽度：{{scope.row.width}} | 生产状态：<dict-tag2 :options="dict.type.produce_state" :value="scope.row.produceState"/> | 发布时间：{{parseTime(scope.row.releaseTime, '{y}-{m}-{d}')}}
                </template>
              </el-table-column>
              <el-table-column label="操作" align="center"  width="150" class-name="small-padding fixed-width">
                <template slot-scope="scope">
                  <span style="color: red;">￥{{scope.row.price}}</span><br/>
                  <el-button
                    size="mini"
                    type="text"
                    icon="el-icon-plus"
                    @click="handleAddProduct(scope.row)"
                  >加入配置单</el-button>

                  <!--          <el-button-->
                  <!--            size="mini"-->
                  <!--            type="text"-->
                  <!--            icon="el-icon-delete"-->
                  <!--            @click="handleDelete(scope.row)"-->
                  <!--            v-hasPermi="['system:ups:remove']"-->
                  <!--          >删除</el-button>-->
                </template>
              </el-table-column>
            </el-table>
            <pagination
              v-show="productTotal>0"
              :total="productTotal"
              :page.sync="productQueryParams.pageNum"
              :limit.sync="productQueryParams.pageSize"
              @pagination="getProductList"
            />

          </el-tab-pane>
          <el-tab-pane label="配件":name="product_accessory+''">

            <el-form :model="productQueryParams" ref="productQueryForm10" size="small" :inline="true" label-width="68px">

              <el-form-item label="名称" prop="title">
                <el-input
                  v-model="productQueryParams.title"
                  placeholder="请输入名称"
                  clearable
                  @keyup.enter.native="handleQuery"
                />
              </el-form-item>

              <el-form-item label="类别" prop="accessory">
                <el-select v-model="productQueryParams.accessory" placeholder="类别" clearable>
                  <el-option v-for="item in productOption.accessory" :key="item.id" :label="item.title" :value="item.id" />
                </el-select>
              </el-form-item>

              <el-form-item>
                <el-button type="primary" icon="el-icon-search" size="mini" @click="productHandleQuery">搜索</el-button>
                <el-button icon="el-icon-refresh" size="mini" @click="productResetQuery">重置</el-button>
              </el-form-item>
            </el-form>

            <el-table v-loading="loading" :data="productList"  :show-header="false">
              <el-table-column label="图片" align="center" width="150">
                <template slot-scope="scope">
                  <ImagePreview :src="scope.row.img" :width="60" :height="60"></ImagePreview>
                </template>
              </el-table-column>
              <el-table-column align="left">
                <template slot-scope="scope">
                  {{scope.row.title}}<br/>
                  类别：{{scope.row.accessoryTitle}} | 品牌：{{scope.row.brandTitle}} | 生产状态：<dict-tag2 :options="dict.type.produce_state" :value="scope.row.produceState"/> | 发布时间：{{parseTime(scope.row.releaseTime, '{y}-{m}-{d}')}}
                </template>
              </el-table-column>
              <el-table-column label="操作" align="center"  width="150" class-name="small-padding fixed-width">
                <template slot-scope="scope">
                  <span style="color: red;">￥{{scope.row.price}}</span><br/>
                  <el-button
                    size="mini"
                    type="text"
                    icon="el-icon-plus"
                    @click="handleAddProduct(scope.row)"
                  >加入配置单</el-button>

                  <!--          <el-button-->
                  <!--            size="mini"-->
                  <!--            type="text"-->
                  <!--            icon="el-icon-delete"-->
                  <!--            @click="handleDelete(scope.row)"-->
                  <!--            v-hasPermi="['system:ups:remove']"-->
                  <!--          >删除</el-button>-->
                </template>
              </el-table-column>
            </el-table>

            <pagination
              v-show="productTotal>0"
              :total="productTotal"
              :page.sync="productQueryParams.pageNum"
              :limit.sync="productQueryParams.pageSize"
              @pagination="getProductList"
            />

          </el-tab-pane>
        </el-tabs>
      </el-form>
    </el-dialog>

  </div>
</template>

<script>
import { listBoxlist, getBoxlist, delBoxlist, addBoxlist, updateBoxlist } from "@/api/scheme/boxlist";
import { getAllLabelByClassif} from "@/api/scheme/label";
import { getAllAttrGroupByMainClass, getAttrByFatherId} from "@/api/product/attr";
import {listUps} from "@/api/product/ups";
import { listProduct} from "@/api/product/product";

export default {
  name: "Ups",
  dicts: ['produce_state'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // UPS信息
      upsList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        classify:this.classif_power_distribution_cabinet,//默认产品类型
        labelId:null,
        title: null,
        listNo: null,
        labelIdArray: []
      },
      allOption:[],//选项
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        title: [
          { required: true, message: "名称不能为空", trigger: "blur" }
        ],
        titleEn: [
          { required: true, message: "名称(英文)不能为空", trigger: "blur" }
        ],
        subTitle: [
          { required: true, message: "副标题 简述不能为空", trigger: "blur" }
        ],
        subTitleEn: [
          { required: true, message: "副标题 简述(英文)不能为空", trigger: "blur" }
        ],
        brand: [
          { required: true, message: "品牌不能为空", trigger: "change" }
        ],
        series: [
          { required: true, message: "系列不能为空", trigger: "change" }
        ],
        capacity: [
          { required: true, message: "容量不能为空", trigger: "change" }
        ],
        voltage: [
          { required: true, message: "电压不能为空", trigger: "change" }
        ],
        weight: [
          { required: true, message: "重量不能为空", trigger: "blur" }
        ],
        length: [
          { required: true, message: "尺寸不能为空", trigger: "blur" }
        ],
        fullWeight: [
          { required: true, message: "毛重不能为空", trigger: "blur" }
        ],
        price: [
          { required: true, message: "目录价格不能为空", trigger: "blur" }
        ],
        priceRetail: [
          { required: true, message: "零售价格不能为空", trigger: "blur" }
        ],
        directSalePrice: [
          { required: true, message: "直营价不能为空", trigger: "blur" }
        ],
        priceCb: [
          { required: true, message: "成本价不能为空", trigger: "blur" }
        ],
        batteryType: [
          { required: true, message: "电池类型不能为空", trigger: "change" }
        ],
        hourRate: [
          { required: true, message: "小时率不能为空", trigger: "change" }
        ],
        produceState: [
          { required: true, message: "生产状态不能为空", trigger: "change" }
        ],
        deliveryDate: [
          { required: true, message: "货期不能为空", trigger: "blur" }
        ],
        warrantyPeriod: [
          { required: true, message: "质保期不能为空", trigger: "blur" }
        ],
        img: [
          { required: true, message: "图片不能为空", trigger: "blur" }
        ],
        imgs: [
          { required: true, message: "详情图不能为空", trigger: "blur" }
        ],
        imgsEn: [
          { required: true, message: "详情图英文不能为空", trigger: "blur" }
        ]
      },

      //是否显示弹窗中的弹窗
      formOpen: false,
      //选中的标签
      formSelectBrand:[],//品牌
      formSelectCap:[],//容量
      formSelectCategory:[],//柜体
      formSelectWays:[],//回路
      formSelectFunction:[],//功能
      formSelectOther:[],//应用
      //添加配置时筛选条件
      productOption:[],//选项
      //添加配置是查询参数
      productQueryParams: {
        pageNum: 1,
        pageSize: 10,
        mainClass:this.product_cabinet,//默认产品类型
        title: null,
        brand: null,
        series: null
      },
      productList:[],
      // 总条数
      productTotal: 0,
    };
  },
  created() {
    /**筛选条件*/
    this.getAllOption();

    this.getList();
  },
  //过滤出指定产品，显示在指定位置
  computed:{
    //柜体
    formProductCabinetFilter(){
      if(this.form.detailList==undefined){
        return;
      }
      return this.form.detailList.filter(item => item.mainClass === this.product_cabinet);
    },
    //断路器
    formProductBreakerFilter(){
      if(this.form.detailList==undefined){
        return;
      }
      return this.form.detailList.filter(item => item.mainClass === this.product_breaker);
    },
    //ATS开关
    formProductATSFilter(){
      if(this.form.detailList==undefined){
        return;
      }
      return this.form.detailList.filter(item => item.mainClass === this.product_ats);
    },
    //互感器
    formProductTransformerInstrumentFilter(){
      if(this.form.detailList==undefined){
        return;
      }
      return this.form.detailList.filter(item => item.mainClass === this.product_transformer_instrument);
    },
    //按钮与指示灯
    formProductButtonsAndIndicatorsFilter(){
      if(this.form.detailList==undefined){
        return;
      }
      return this.form.detailList.filter(item => item.mainClass === this.product_buttons_and_indicators);
    },
    //仪表
    formProductInstrumentFilter(){
      if(this.form.detailList==undefined){
        return;
      }
      return this.form.detailList.filter(item => item.mainClass === this.product_instrument);
    },
    //浪涌保护
    formProductSurgeProtectionFilter(){
      if(this.form.detailList==undefined){
        return;
      }
      return this.form.detailList.filter(item => item.mainClass === this.product_surge_protection);
    },
    //插座
    formProductSocketFilter(){
      if(this.form.detailList==undefined){
        return;
      }
      return this.form.detailList.filter(item => item.mainClass === this.product_socket);
    },
    //铜排
    formProductCopperBarFilter(){
      if(this.form.detailList==undefined){
        return;
      }
      return this.form.detailList.filter(item => item.mainClass === this.product_copper_bar);
    },
    //配件
    formProductAccessoryFilter(){
      if(this.form.detailList==undefined){
        return;
      }
      return this.form.detailList.filter(item => item.mainClass === this.product_accessory);
    }
  },
  methods: {
    /**
     * 标签点击事件
     * @param item 对象
     * @param arrayName 存储ID的数组名称
     * @param isMultiple 是否为多选
     */
    handleTagClick(item,arrayName,isMultiple) {
      if(isMultiple==undefined){
        isMultiple=false;
      }


      //如果多选
      if(isMultiple){

        if (this[arrayName].includes(item.id)) {
          this[arrayName] = this[arrayName].filter(id => id!== item.id);
        } else {
          this[arrayName].push(item.id);
        }

      }else{

        if (this[arrayName].includes(item.id)) {
          this[arrayName] = [];
        } else {
          this[arrayName] = [];
          this[arrayName].push(item.id);
        }


      }


    },
    /**选项*/
    getAllOption(){
      getAllLabelByClassif(this.queryParams.classify).then(response=>{
        this.allOption = response.data;
      });
    },
    /** 查询列表 */
    getList() {
      if (this.queryParams.labelIdArray != undefined && this.queryParams.labelIdArray !=null && this.queryParams.labelIdArray.length > 0) {
        this.queryParams.labelId=this.queryParams.labelIdArray.filter(item => item!== undefined && item!== null && item!='').join(",");
      }
      this.loading = true;
      listBoxlist(this.queryParams).then(response => {
        this.upsList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        classify:this.queryParams.classify,//默认产品类型
        id: undefined,
        detailList:[],
        title: undefined,
        titleEn: undefined,
        subTitle: undefined,
        subTitleEn: undefined,
        handlingFee: undefined,
        install: undefined,
        debugFee: undefined,
        freight: undefined,
        cadPath: undefined,
        pdfPath: undefined,
        rarPath: undefined,
        labelId:undefined
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.labelIdArray=[];
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    //数量变更
    productNumChange(row){
      //计算产品总金额
      row.totalPrice=row.num*row.price;
    },
    //tabs点击
    handleTabsClick(tab){
      //点击tabs时显示对应tabs页
      this.handleFormAdd(tab.name);
    },
    /** 弹窗中在弹窗 */
    handleFormAdd(mainClass) {
      //重置产品列表和总个数
      this.productList =[];
      this.productTotal = 0;

      //加载筛选条件
      this.productOption=[];
      getAllAttrGroupByMainClass(mainClass).then(response=>{
        this.productOption = response.data;
      });

      //当前产品类型
      this.productQueryParams.mainClass=mainClass;

      //加载列表数据
      this.productResetQuery();

      //显示弹窗
      this.formOpen = true;
    },
    //加载产品
    getProductList(){
      listProduct(this.productQueryParams).then(response => {
        this.productList = response.rows;
        this.productTotal = response.total;
      });

    },
    /**切换品牌查询系列*/
    getProductListSeries(value){
      //系列条件列表重置为空
      this.productQueryParams.series=null;

      //已选择的系列重置为空
      if (value==undefined || value==null || value<=0){
        this.productOption.series=null;
        return;
      }
      //根据品牌ID查询系列
      getAttrByFatherId(value).then(response=>{
        this.productOption.series=response.data;
      });
    },
    /** 搜索按钮操作 */
    productHandleQuery() {
      this.productQueryParams.pageNum = 1;
      this.getProductList();
    },
    /** 重置按钮操作 */
    productResetQuery() {
      this.resetForm("productQueryForm1");
      this.resetForm("productQueryForm2");
      this.resetForm("productQueryForm3");
      this.resetForm("productQueryForm4");
      this.resetForm("productQueryForm5");
      this.resetForm("productQueryForm6");
      this.resetForm("productQueryForm7");
      this.resetForm("productQueryForm8");
      this.resetForm("productQueryForm9");
      this.resetForm("productQueryForm10");
      this.productHandleQuery();
    },
    //添加产品到表单中
    handleAddProduct(row){

      if (this.form.detailList.some(x => x.productId === row.id)) {
        let item = this.form.detailList.find(x => x.productId === row.id);
        item.num++;


        this.productNumChange(item);
      } else {
        let item={
          "productId":row.id,
          "productTitle":row.title,
          "price":row.price,
          "num":1,
          "mainClass":row.mainClass,
          "type":"",
          "imgPath":row.img
        };
        this.form.detailList.push(item);


        this.productNumChange(item);
      }



      this.formOpen = false;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加信息";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getBoxlist(id).then(response => {
        this.handleUpdateInitTag(response.data.labelId);
        this.form = response.data;
        this.open = true;
        this.title = "修改信息"+this.form.title;
      });
    },
    /** 修改获取数据初始化标签 */
    handleUpdateInitTag(labelId){
      let that=this;

        that.formSelectBrand=[];//品牌
        that.formSelectCap=[];//容量
        that.formSelectCategory=[];//柜体
        that.formSelectWays=[];//回路
        that.formSelectFunction=[];//功能
        that.formSelectOther=[];//应用



      if(labelId==undefined || labelId==null || labelId==""){
        return;
      }
      let split = labelId.split(",");
      split.forEach(function (i){
        that.formSelectBrand.push(parseInt(i));
        that.formSelectCap.push(parseInt(i));
        that.formSelectCategory.push(parseInt(i));
        that.formSelectWays.push(parseInt(i));
        that.formSelectFunction.push(parseInt(i));
        that.formSelectOther.push(parseInt(i));
      });


      let tagId = this.allOption.brandLabel.map(x=>x.id);
      that.formSelectBrand=that.formSelectBrand.filter(x=> tagId.includes(x))

      tagId = this.allOption.capLabel.map(x=>x.id);
      that.formSelectCap=that.formSelectCap.filter(x=> tagId.includes(x))

      tagId = this.allOption.categoryLabel.map(x=>x.id);
      that.formSelectCategory=that.formSelectCategory.filter(x=> tagId.includes(x))

      tagId=this.allOption.waysLabel.map(x=>x.id);
      that.formSelectWays=that.formSelectWays.filter(x=> tagId.includes(x))

      tagId = this.allOption.functionLabel.map(x=>x.id);
      that.formSelectFunction=that.formSelectFunction.filter(x=> tagId.includes(x))

      tagId = this.allOption.otherLabel.map(x=>x.id);
      that.formSelectOther=that.formSelectOther.filter(x=> tagId.includes(x))

    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          let labelId=[...this.formSelectBrand,...this.formSelectCap,...this.formSelectCategory,...this.formSelectWays,...this.formSelectFunction,...this.formSelectOther];
          if(labelId.length>0){
            this.form.labelId=Array.from(new Set(labelId)).join(',');
          }else{
            this.form.labelId="";
          }

          if (this.form.id != null) {
            updateBoxlist(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addBoxlist(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除编号为"' + ids + '"的数据项？').then(function() {
        return delBoxlist(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /**表单中删除*/
    handleFormDelete(row){
      const productId= row.productId;
      this.form.detailList=this.form.detailList.filter(item => item.productId!== productId);
    },
    /** 导出按钮操作 */
    // handleExport() {
    //   this.download('system/ups/export', {
    //     ...this.queryParams
    //   }, `ups_${new Date().getTime()}.xlsx`)
    // }
  }
};
</script>



<!--<style scoped>-->
<!--.el-input-number /deep/ .el-input__inner {-->
<!--  text-align: left!important;-->
<!--}-->
<!--</style>-->
