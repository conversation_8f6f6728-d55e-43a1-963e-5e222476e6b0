<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">

      <el-form-item label="名称" prop="title">
        <el-input
          v-model="queryParams.title"
          placeholder="请输入名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item label="编号" prop="listNo">
        <el-input
          v-model="queryParams.listNo"
          placeholder="请输入编号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item label="容量">
        <el-select v-model="queryParams.labelIdArray[0]" placeholder="容量" clearable>
          <el-option v-for="item in allOption.capLabel" :key="item.id" :label="item.name" :value="item.id" />
        </el-select>
      </el-form-item>

      <el-form-item label="品牌">
        <el-select v-model="queryParams.labelIdArray[1]" placeholder="品牌" clearable>
          <el-option v-for="item in allOption.brandLabel" :key="item.id" :label="item.name" :value="item.id" />
        </el-select>
      </el-form-item>

      <el-form-item label="类型">
        <el-select v-model="queryParams.labelIdArray[2]" placeholder="类型" clearable>
          <el-option v-for="item in allOption.classifyLabel" :key="item.id" :label="item.name" :value="item.id" />
        </el-select>
      </el-form-item>

      <el-form-item label="延时时间">
        <el-select v-model="queryParams.labelIdArray[2]" placeholder="后备时间" clearable>
          <el-option v-for="item in allOption.timeLabel" :key="item.id" :label="item.name" :value="item.id" />
        </el-select>
      </el-form-item>

      <el-form-item label="特色">
        <el-select v-model="queryParams.labelIdArray[3]" placeholder="特色" clearable>
          <el-option v-for="item in allOption.otherLabel" :key="item.id" :label="item.name" :value="item.id" />
        </el-select>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['system:ups:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
        >修改</el-button>
      </el-col>
<!--      <el-col :span="1.5">-->
<!--        <el-button-->
<!--          type="danger"-->
<!--          plain-->
<!--          icon="el-icon-delete"-->
<!--          size="mini"-->
<!--          :disabled="multiple"-->
<!--          @click="handleDelete"-->
<!--          v-hasPermi="['system:ups:remove']"-->
<!--        >删除</el-button>-->
<!--      </el-col>-->
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="upsList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="编号" align="center" prop="listNo" />
      <el-table-column label="名称" align="center" prop="title" />
      <el-table-column label="作者" align="center" prop="author" />
      <el-table-column label="添加时间" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
          >修改</el-button>
<!--          <el-button-->
<!--            size="mini"-->
<!--            type="text"-->
<!--            icon="el-icon-delete"-->
<!--            @click="handleDelete(scope.row)"-->
<!--            v-hasPermi="['system:ups:remove']"-->
<!--          >删除</el-button>-->
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改 -->
    <el-dialog :title="title" :visible.sync="open" width="1200px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">

        <el-row>
          <el-col :span="24">
            <el-form-item label="EPS：">
              <div>
                <el-button
                  size="mini"
                  type="text"
                  icon="el-icon-plus"
                  @click="handleFormAdd(eps_eps)"

                >添加</el-button>
              </div>
              <el-table :data="formEpsFilter" :show-header="false">
                <el-table-column label="图片" align="center" prop="imgPath">
                  <template slot-scope="scope">
                    <ImagePreview :src="scope.row.imgPath" :width="60" :height="60"></ImagePreview>
                  </template>
                </el-table-column>
                <el-table-column label="名称" align="center" prop="productTitle" />
                <el-table-column label="价格" align="center" prop="price">
                  <template slot-scope="scope">
                    <span style="color: red;">￥{{scope.row.price}}</span><br/>
                    <span>总价￥{{scope.row.totalPrice}}</span>

                  </template>
                </el-table-column>
                <el-table-column label="数量" align="center" prop="num" width="180">
                  <template slot-scope="scope">
                    <el-input-number  v-model="scope.row.num" :min="1" label="数量" @change="productNumChange(scope.row)"></el-input-number>
                  </template>
                </el-table-column>
                <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
                  <template slot-scope="scope">
                    <el-button
                      size="mini"
                      type="text"
                      icon="el-icon-delete"
                      @click="handleFormDelete(scope.row)"

                    >删除</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="电池：">
              <div>
              <el-button
                size="mini"
                type="text"
                icon="el-icon-plus"
                @click="handleFormAdd(ups_battery)"

              >添加</el-button>
            </div>
              <el-table :data="formBatteryFilter" :show-header="false">
                <el-table-column label="图片" align="center" prop="imgPath">
                  <template slot-scope="scope">
                    <ImagePreview :src="scope.row.imgPath" :width="60" :height="60"></ImagePreview>
                  </template>
                </el-table-column>
                <el-table-column label="名称" align="center" prop="productTitle" />
                <el-table-column label="价格" align="center" prop="price">
                  <template slot-scope="scope">
                    <span style="color: red;">￥{{scope.row.price}}</span><br/>
                    <span>总价￥{{scope.row.totalPrice}}</span>

                  </template>
                </el-table-column>
                <el-table-column label="数量" align="center" prop="num" width="180">
                  <template slot-scope="scope">
                    <el-input-number  v-model="scope.row.num" :min="1" label="数量" @change="productNumChange(scope.row)"></el-input-number>
                  </template>
                </el-table-column>
                <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
                  <template slot-scope="scope">
                    <el-button
                      size="mini"
                      type="text"
                      icon="el-icon-delete"
                      @click="handleFormDelete(scope.row)"
                      v-hasPermi="['system:ups:remove']"
                    >删除</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="电池柜：">
              <div>
              <el-button
                size="mini"
                type="text"
                icon="el-icon-plus"
                @click="handleFormAdd(ups_cabinet)"
                v-hasPermi="['system:ups:remove']"
              >添加</el-button>
            </div>
              <el-table :data="formCabinetFilter" :show-header="false">
                <el-table-column label="图片" align="center" prop="imgPath">
                  <template slot-scope="scope">
                    <ImagePreview :src="scope.row.imgPath" :width="60" :height="60"></ImagePreview>
                  </template>
                </el-table-column>
                <el-table-column label="名称" align="center" prop="productTitle" />
                <el-table-column label="价格" align="center" prop="price">
                  <template slot-scope="scope">
                    <span style="color: red;">￥{{scope.row.price}}</span><br/>
                    <span>总价￥{{scope.row.totalPrice}}</span>

                  </template>
                </el-table-column>
                <el-table-column label="数量" align="center" prop="num" width="180">
                  <template slot-scope="scope">
                    <el-input-number  v-model="scope.row.num" :min="1" label="数量" @change="productNumChange(scope.row)"></el-input-number>
                  </template>
                </el-table-column>
                <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
                  <template slot-scope="scope">
                    <el-button
                      size="mini"
                      type="text"
                      icon="el-icon-delete"
                      @click="handleFormDelete(scope.row)"
                      v-hasPermi="['system:ups:remove']"
                    >删除</el-button>
                  </template>
                </el-table-column>
              </el-table>


            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="电池线：">
              <div>
                <el-button
                  size="mini"
                  type="text"
                  icon="el-icon-plus"
                  @click="handleFormAdd(ups_line)"
                  v-hasPermi="['system:ups:remove']"
                >添加</el-button>
              </div>
              <el-table :data="formLineFilter" :show-header="false">
                <el-table-column label="图片" align="center" prop="imgPath">
                  <template slot-scope="scope">
                    <ImagePreview :src="scope.row.imgPath" :width="60" :height="60"></ImagePreview>
                  </template>
                </el-table-column>
                <el-table-column label="名称" align="center" prop="productTitle" />
                <el-table-column label="价格" align="center" prop="price">
                  <template slot-scope="scope">
                    <span style="color: red;">￥{{scope.row.price}}</span><br/>
                    <span>总价￥{{scope.row.totalPrice}}</span>

                  </template>
                </el-table-column>
                <el-table-column label="数量" align="center" prop="num" width="180">
                  <template slot-scope="scope">
                    <el-input-number  v-model="scope.row.num" :min="1" label="数量" @change="productNumChange(scope.row)"></el-input-number>
                  </template>
                </el-table-column>
                <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
                  <template slot-scope="scope">
                    <el-button
                      size="mini"
                      type="text"
                      icon="el-icon-delete"
                      @click="handleFormDelete(scope.row)"
                      v-hasPermi="['system:ups:remove']"
                    >删除</el-button>
                  </template>
                </el-table-column>
              </el-table>

            </el-form-item>
          </el-col>
        </el-row>




        <el-row>
          <el-col :span="12">
            <el-form-item label="名称" prop="title">
              <el-input v-model="form.title" placeholder="请输入名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="名称(英文)" prop="titleEn">
              <el-input v-model="form.titleEn" placeholder="请输入名称(英文)" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="12">
            <el-form-item label="副标题" prop="subTitle">
              <el-input v-model="form.subTitle" placeholder="请输入副标题" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="副标题(英文)" prop="subTitleEn">
              <el-input v-model="form.subTitleEn" placeholder="请输入副标题(英文)" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="12">
            <el-form-item label="就位费" prop="handlingFee">
              <el-input v-model="form.handlingFee" placeholder="请输入就位费" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="安装费" prop="install">
              <el-input v-model="form.install" placeholder="请输入安装费" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="12">
            <el-form-item label="调试费" prop="debugFee">
              <el-input v-model="form.debugFee" placeholder="请输入调试费" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="运费" prop="freight">
              <el-input v-model="form.freight" placeholder="请输入运费" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24">
            <el-form-item label="CAD上传" prop="cadPath">
              <FileUpload :limit="1" v-model="form.cadPath" :fileType="['dwg','dws','dwt','dxf']"></FileUpload>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24">
            <el-form-item label="PDF上传" prop="pdfPath">
              <FileUpload :limit="1" v-model="form.pdfPath" :fileType="['pdf']"></FileUpload>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24">
            <el-form-item label="压缩包上传" prop="rarPath">
              <FileUpload :limit="1" v-model="form.rarPath" :fileType="['7z','zip','rar']"></FileUpload>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24">
            <el-form-item label="图片" prop="imgPath">
              <ImageUpload v-model="form.imgPath" file-size="5" :limit="5"></ImageUpload>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24">
            <el-form-item label="容量">

              <el-tag
                v-for="item in allOption.capLabel"
                :key="item.id"
                :type="formSelectCap.includes(item.id) ?'' : 'info'"
                @click="handleTagClick(item,'formSelectCap')"
                style="margin: 5px;cursor: pointer;"
              >
                {{ item.name }}
              </el-tag>


            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24">
            <el-form-item label="品牌">
              <el-tag
                v-for="item in allOption.brandLabel"
                :key="item.id"
                :type="formSelectBrand.includes(item.id) ?'' : 'info'"
                @click="handleTagClick(item,'formSelectBrand')"
                style="margin: 5px;cursor: pointer;"
              >
                {{ item.name }}
              </el-tag>

            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24">
            <el-form-item label="类型">
              <el-tag
                v-for="item in allOption.classifyLabel"
                :key="item.id"
                :type="formSelectClassify.includes(item.id) ?'' : 'info'"
                @click="handleTagClick(item,'formSelectClassify')"
                style="margin: 5px;cursor: pointer;"
              >
                {{ item.name }}
              </el-tag>

            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24">
            <el-form-item label="延时时间">

              <el-tag
                v-for="item in allOption.timeLabel"
                :key="item.id"
                :type="formSelectTime.includes(item.id) ?'' : 'info'"
                @click="handleTagClick(item,'formSelectTime')"
                style="margin: 5px;cursor: pointer;"
              >
                {{ item.name }}
              </el-tag>


            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24">
            <el-form-item label="特色">

              <el-tag
                v-for="item in allOption.otherLabel"
                :key="item.id"
                :type="formSelectOther.includes(item.id) ?'' : 'info'"
                @click="handleTagClick(item,'formSelectOther',true)"
                style="margin: 5px;cursor: pointer;"
              >
                {{ item.name }}
              </el-tag>

            </el-form-item>
          </el-col>
        </el-row>

      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>


    <el-dialog :visible.sync="formOpen" width="980px" append-to-body>
      <el-form label-width="120px">
        <el-tabs v-model="productQueryParams.mainClass.toString()" type="border-card" @tab-click="handleTabsClick">
          <el-tab-pane label="EPS"  :name="eps_eps+''" >

            <el-form :model="productQueryParams" ref="productQueryForm1" size="small" :inline="true" label-width="68px">

              <el-form-item label="名称" prop="title">
                <el-input
                  v-model="productQueryParams.title"
                  placeholder="请输入名称"
                  clearable
                  @keyup.enter.native="handleQuery"
                />
              </el-form-item>

              <el-form-item label="品牌" prop="brand">
                <el-select v-model="productQueryParams.brand" placeholder="品牌" clearable @change="getProductListSeries">
                  <el-option v-for="item in productOption.brand" :key="item.id" :label="item.title" :value="item.id" />
                </el-select>
              </el-form-item>

              <el-form-item label="系列" prop="series">
                <el-select v-model="productQueryParams.series" placeholder="系列" clearable>
                  <el-option v-for="item in productOption.series" :key="item.id" :label="item.title" :value="item.id" />
                </el-select>
              </el-form-item>
              <el-form-item>
                <el-button type="primary" icon="el-icon-search" size="mini" @click="productHandleQuery">搜索</el-button>
                <el-button icon="el-icon-refresh" size="mini" @click="productResetQuery">重置</el-button>
              </el-form-item>
            </el-form>

            <el-table v-loading="loading" :data="productList"  :show-header="false">
              <el-table-column label="图片" align="center" width="150">
                <template slot-scope="scope">
                  <ImagePreview :src="scope.row.img" :width="60" :height="60"></ImagePreview>
                </template>
              </el-table-column>
              <el-table-column align="left">
                <template slot-scope="scope">
                  {{scope.row.title}}<br/>
                  品牌：{{scope.row.brandTitle}} | 容量：{{scope.row.capacityTitle}} | 类型：{{scope.row.classifyTitle}} | 尺寸：{{scope.row.width}} | 重量：{{scope.row.weight}}
                </template>
              </el-table-column>
              <el-table-column label="操作" align="center"  width="150" class-name="small-padding fixed-width">
                <template slot-scope="scope">
                  <span style="color: red;">￥{{scope.row.price}}</span><br/>
                  <el-button
                    size="mini"
                    type="text"
                    icon="el-icon-plus"
                    @click="handleAddProduct(scope.row)"
                  >加入配置单</el-button>

                  <!--          <el-button-->
                  <!--            size="mini"-->
                  <!--            type="text"-->
                  <!--            icon="el-icon-delete"-->
                  <!--            @click="handleDelete(scope.row)"-->
                  <!--            v-hasPermi="['system:ups:remove']"-->
                  <!--          >删除</el-button>-->
                </template>
              </el-table-column>
            </el-table>

            <pagination
              v-show="productTotal>0"
              :total="productTotal"
              :page.sync="productQueryParams.pageNum"
              :limit.sync="productQueryParams.pageSize"
              @pagination="getProductList"
            />


          </el-tab-pane>
          <el-tab-pane label="电池"  :name="ups_battery+''">

            <el-form :model="productQueryParams" ref="productQueryForm2" size="small" :inline="true" label-width="68px">

              <el-form-item label="名称" prop="title">
                <el-input
                  v-model="productQueryParams.title"
                  placeholder="请输入名称"
                  clearable
                  @keyup.enter.native="handleQuery"
                />
              </el-form-item>

              <el-form-item label="品牌" prop="brand">
                <el-select v-model="productQueryParams.brand" placeholder="品牌" clearable @change="getProductListSeries">
                  <el-option v-for="item in productOption.brand" :key="item.id" :label="item.title" :value="item.id" />
                </el-select>
              </el-form-item>

              <el-form-item label="系列" prop="series">
                <el-select v-model="productQueryParams.series" placeholder="系列" clearable>
                  <el-option v-for="item in productOption.series" :key="item.id" :label="item.title" :value="item.id" />
                </el-select>
              </el-form-item>
              <el-form-item>
                <el-button type="primary" icon="el-icon-search" size="mini" @click="productHandleQuery">搜索</el-button>
                <el-button icon="el-icon-refresh" size="mini" @click="productResetQuery">重置</el-button>
              </el-form-item>
            </el-form>

            <el-table v-loading="loading" :data="productList"  :show-header="false">
              <el-table-column label="图片" align="center" width="150">
                <template slot-scope="scope">
                  <ImagePreview :src="scope.row.img" :width="60" :height="60"></ImagePreview>
                </template>
              </el-table-column>
              <el-table-column align="left">
                <template slot-scope="scope">
                  {{scope.row.title}}<br/>
                  品牌：{{scope.row.brandTitle}} | 系列：{{scope.row.seriesTitle}} | 容量：{{scope.row.capacityTitle}} | 电压：{{scope.row.voltageTitle}} | 尺寸：{{scope.row.length}}<br/>
                  重量：{{scope.row.weight}}
                </template>
              </el-table-column>
              <el-table-column label="操作" align="center"  width="150" class-name="small-padding fixed-width">
                <template slot-scope="scope">
                  <span style="color: red;">￥{{scope.row.price}}</span><br/>
                  <el-button
                    size="mini"
                    type="text"
                    icon="el-icon-plus"
                    @click="handleAddProduct(scope.row)"
                  >加入配置单</el-button>

                  <!--          <el-button-->
                  <!--            size="mini"-->
                  <!--            type="text"-->
                  <!--            icon="el-icon-delete"-->
                  <!--            @click="handleDelete(scope.row)"-->
                  <!--            v-hasPermi="['system:ups:remove']"-->
                  <!--          >删除</el-button>-->
                </template>
              </el-table-column>
            </el-table>

            <pagination
              v-show="productTotal>0"
              :total="productTotal"
              :page.sync="productQueryParams.pageNum"
              :limit.sync="productQueryParams.pageSize"
              @pagination="getProductList"
            />

          </el-tab-pane>
          <el-tab-pane label="电池柜" :name="ups_cabinet+''">

            <el-form :model="productQueryParams" ref="productQueryForm3" size="small" :inline="true" label-width="68px">

              <el-form-item label="名称" prop="title">
                <el-input
                  v-model="productQueryParams.title"
                  placeholder="请输入名称"
                  clearable
                  @keyup.enter.native="handleQuery"
                />
              </el-form-item>

              <el-form-item label="品牌" prop="brand">
                <el-select v-model="productQueryParams.brand" placeholder="品牌" clearable @change="getProductListSeries">
                  <el-option v-for="item in productOption.brand" :key="item.id" :label="item.title" :value="item.id" />
                </el-select>
              </el-form-item>

              <el-form-item label="系列" prop="series">
                <el-select v-model="productQueryParams.series" placeholder="系列" clearable>
                  <el-option v-for="item in productOption.series" :key="item.id" :label="item.title" :value="item.id" />
                </el-select>
              </el-form-item>
              <el-form-item>
                <el-button type="primary" icon="el-icon-search" size="mini" @click="productHandleQuery">搜索</el-button>
                <el-button icon="el-icon-refresh" size="mini" @click="productResetQuery">重置</el-button>
              </el-form-item>
            </el-form>

            <el-table v-loading="loading" :data="productList"  :show-header="false">
              <el-table-column label="图片" align="center" width="150">
                <template slot-scope="scope">
                  <ImagePreview :src="scope.row.img" :width="60" :height="60"></ImagePreview>
                </template>
              </el-table-column>
              <el-table-column align="left">
                <template slot-scope="scope">
                  {{scope.row.title}}<br/>
                  品牌：{{scope.row.brandTitle}} | 系列：{{scope.row.seriesTitle}} | 尺寸：{{scope.row.length}} | 重量：{{scope.row.weight}}
                </template>
              </el-table-column>
              <el-table-column label="操作" align="center"  width="150" class-name="small-padding fixed-width">
                <template slot-scope="scope">
                  <span style="color: red;">￥{{scope.row.price}}</span><br/>
                  <el-button
                    size="mini"
                    type="text"
                    icon="el-icon-plus"
                    @click="handleAddProduct(scope.row)"
                  >加入配置单</el-button>

                  <!--          <el-button-->
                  <!--            size="mini"-->
                  <!--            type="text"-->
                  <!--            icon="el-icon-delete"-->
                  <!--            @click="handleDelete(scope.row)"-->
                  <!--            v-hasPermi="['system:ups:remove']"-->
                  <!--          >删除</el-button>-->
                </template>
              </el-table-column>
            </el-table>
            <pagination
              v-show="productTotal>0"
              :total="productTotal"
              :page.sync="productQueryParams.pageNum"
              :limit.sync="productQueryParams.pageSize"
              @pagination="getProductList"
            />

          </el-tab-pane>
          <el-tab-pane label="电池线" :name="ups_line+''">

            <el-form :model="productQueryParams" ref="productQueryForm4" size="small" :inline="true" label-width="68px">

              <el-form-item label="名称" prop="title">
                <el-input
                  v-model="productQueryParams.title"
                  placeholder="请输入名称"
                  clearable
                  @keyup.enter.native="handleQuery"
                />
              </el-form-item>

              <el-form-item label="品牌" prop="brand">
                <el-select v-model="productQueryParams.brand" placeholder="品牌" clearable @change="getProductListSeries">
                  <el-option v-for="item in productOption.brand" :key="item.id" :label="item.title" :value="item.id" />
                </el-select>
              </el-form-item>

              <el-form-item label="系列" prop="series">
                <el-select v-model="productQueryParams.series" placeholder="系列" clearable>
                  <el-option v-for="item in productOption.series" :key="item.id" :label="item.title" :value="item.id" />
                </el-select>
              </el-form-item>
              <el-form-item>
                <el-button type="primary" icon="el-icon-search" size="mini" @click="productHandleQuery">搜索</el-button>
                <el-button icon="el-icon-refresh" size="mini" @click="productResetQuery">重置</el-button>
              </el-form-item>
            </el-form>

            <el-table v-loading="loading" :data="productList"  :show-header="false">
              <el-table-column label="图片" align="center" width="150">
                <template slot-scope="scope">
                  <ImagePreview :src="scope.row.img" :width="60" :height="60"></ImagePreview>
                </template>
              </el-table-column>
              <el-table-column align="left">
                <template slot-scope="scope">
                  {{scope.row.title}}<br/>
                  品牌：{{scope.row.brandTitle}} | 规格：{{scope.row.lineDiameterTitle}}
                </template>
              </el-table-column>
              <el-table-column label="操作" align="center"  width="150" class-name="small-padding fixed-width">
                <template slot-scope="scope">
                  <span style="color: red;">￥{{scope.row.price}}</span><br/>
                  <el-button
                    size="mini"
                    type="text"
                    icon="el-icon-plus"
                    @click="handleAddProduct(scope.row)"
                  >加入配置单</el-button>

                  <!--          <el-button-->
                  <!--            size="mini"-->
                  <!--            type="text"-->
                  <!--            icon="el-icon-delete"-->
                  <!--            @click="handleDelete(scope.row)"-->
                  <!--            v-hasPermi="['system:ups:remove']"-->
                  <!--          >删除</el-button>-->
                </template>
              </el-table-column>
            </el-table>
            <pagination
              v-show="productTotal>0"
              :total="productTotal"
              :page.sync="productQueryParams.pageNum"
              :limit.sync="productQueryParams.pageSize"
              @pagination="getProductList"
            />

          </el-tab-pane>
        </el-tabs>
      </el-form>
    </el-dialog>

  </div>
</template>

<script>
import { listBoxlist, getBoxlist, delBoxlist, addBoxlist, updateBoxlist } from "@/api/scheme/boxlist";
import { getAllLabelByClassif} from "@/api/scheme/label";
import { getAllAttrGroupByMainClass, getAttrByFatherId} from "@/api/product/attr";
import {listUps} from "@/api/product/ups";
import {listEps} from "@/api/product/eps";
import { listProduct} from "@/api/product/product";

export default {
  name: "Ups",
  dicts: ['produce_state'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // UPS信息
      upsList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        classify:this.classif_eps,//默认产品类型
        title: null,
        listNo: null,
        labelId:null,
        labelIdArray: []
      },
      allOption:[],//选项
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        title: [
          { required: true, message: "名称不能为空", trigger: "blur" }
        ],
        titleEn: [
          { required: true, message: "名称(英文)不能为空", trigger: "blur" }
        ],
        subTitle: [
          { required: true, message: "副标题 简述不能为空", trigger: "blur" }
        ],
        subTitleEn: [
          { required: true, message: "副标题 简述(英文)不能为空", trigger: "blur" }
        ],
        brand: [
          { required: true, message: "品牌不能为空", trigger: "change" }
        ],
        series: [
          { required: true, message: "系列不能为空", trigger: "change" }
        ],
        capacity: [
          { required: true, message: "容量不能为空", trigger: "change" }
        ],
        voltage: [
          { required: true, message: "电压不能为空", trigger: "change" }
        ],
        weight: [
          { required: true, message: "重量不能为空", trigger: "blur" }
        ],
        length: [
          { required: true, message: "尺寸不能为空", trigger: "blur" }
        ],
        fullWeight: [
          { required: true, message: "毛重不能为空", trigger: "blur" }
        ],
        price: [
          { required: true, message: "目录价格不能为空", trigger: "blur" }
        ],
        directSalePrice: [
          { required: true, message: "直营价不能为空", trigger: "blur" }
        ],
        priceCb: [
          { required: true, message: "成本价不能为空", trigger: "blur" }
        ],
        batteryType: [
          { required: true, message: "电池类型不能为空", trigger: "change" }
        ],
        hourRate: [
          { required: true, message: "小时率不能为空", trigger: "change" }
        ],
        produceState: [
          { required: true, message: "生产状态不能为空", trigger: "change" }
        ],
        deliveryDate: [
          { required: true, message: "货期不能为空", trigger: "blur" }
        ],
        warrantyPeriod: [
          { required: true, message: "质保期不能为空", trigger: "blur" }
        ],
        img: [
          { required: true, message: "图片不能为空", trigger: "blur" }
        ],
        imgs: [
          { required: true, message: "详情图不能为空", trigger: "blur" }
        ],
        imgsEn: [
          { required: true, message: "详情图英文不能为空", trigger: "blur" }
        ]
      },

      //是否显示弹窗中的弹窗
      formOpen: false,
      //选中的标签
      formSelectCap:[],
      formSelectBrand:[],
      formSelectClassify:[],
      formSelectTime:[],
      formSelectOther:[],
      //添加配置时筛选条件
      productOption:[],//选项
      //添加配置是查询参数
      productQueryParams: {
        pageNum: 1,
        pageSize: 10,
        mainClass:this.eps_eps,//默认产品类型
        title: null,
        brand: null,
        series: null
      },
      productList:[],
      // 总条数
      productTotal: 0,
    };
  },
  created() {
    /**筛选条件*/
    this.getAllOption();

    this.getList();
  },

  //过滤出指定产品，显示在指定位置
  computed:{
    //EPS
    formEpsFilter(){
      if(this.form.detailList==undefined){
        return;
      }
      return this.form.detailList.filter(item => item.mainClass === this.eps_eps);
    },
    //电池
    formBatteryFilter(){
      if(this.form.detailList==undefined){
        return;
      }
      return this.form.detailList.filter(item => item.mainClass === this.ups_battery);
    },
    //电池柜
    formCabinetFilter(){
      if(this.form.detailList==undefined){
        return;
      }
      return this.form.detailList.filter(item => item.mainClass === this.ups_cabinet);
    },
    //电池线
    formLineFilter(){
      if(this.form.detailList==undefined){
        return;
      }
      return this.form.detailList.filter(item => item.mainClass === this.ups_line);
    }
  },

  methods: {
    /**
     * 标签点击事件
     * @param item 对象
     * @param arrayName 存储ID的数组名称
     * @param isMultiple 是否为多选
     */
    handleTagClick(item,arrayName,isMultiple) {
      if(isMultiple==undefined){
        isMultiple=false;
      }


      //如果多选
      if(isMultiple){

        if (this[arrayName].includes(item.id)) {
          this[arrayName] = this[arrayName].filter(id => id!== item.id);
        } else {
          this[arrayName].push(item.id);
        }

      }else{

        if (this[arrayName].includes(item.id)) {
          this[arrayName] = [];
        } else {
          this[arrayName] = [];
          this[arrayName].push(item.id);
        }


      }


    },
    /**选项*/
    getAllOption(){
      getAllLabelByClassif(this.queryParams.classify).then(response=>{
        this.allOption = response.data;
      });
    },
    /** 查询列表 */
    getList() {
      if (this.queryParams.labelIdArray != undefined && this.queryParams.labelIdArray !=null && this.queryParams.labelIdArray.length > 0) {
        this.queryParams.labelId=this.queryParams.labelIdArray.filter(item => item!== undefined && item!== null && item!='').join(",");
      }
      this.loading = true;
      listBoxlist(this.queryParams).then(response => {
        this.upsList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        classify:this.queryParams.classify,//默认产品类型
        id: undefined,
        detailList:[],
        title: undefined,
        titleEn: undefined,
        subTitle: undefined,
        subTitleEn: undefined,
        handlingFee: undefined,
        install: undefined,
        debugFee: undefined,
        freight: undefined,
        cadPath: undefined,
        pdfPath: undefined,
        rarPath: undefined,
        labelId:undefined
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams.labelIdArray=[];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    //数量变更
    productNumChange(row){
      //计算产品总金额
      row.totalPrice=row.num*row.price;
    },
    //tabs点击
    handleTabsClick(tab){
      //点击tabs时显示对应tabs页
      this.handleFormAdd(tab.name);
    },
    /** 弹窗中在弹窗 */
    handleFormAdd(mainClass) {
      //重置产品列表和总个数
      this.productList =[];
      this.productTotal = 0;

      //加载筛选条件
      this.productOption=[];
      getAllAttrGroupByMainClass(mainClass).then(response=>{
        this.productOption = response.data;
      });

      //当前产品类型
      this.productQueryParams.mainClass=mainClass;

      //加载列表数据
      this.productResetQuery();

      //显示弹窗
      this.formOpen = true;
    },
    //加载产品
    getProductList(){
      if (this.eps_eps == this.productQueryParams.mainClass) {
        listEps(this.productQueryParams).then(response => {
          this.productList = response.rows;
          this.productTotal = response.total;
        });
      }else if (this.ups_battery==this.productQueryParams.mainClass || this.ups_cabinet==this.productQueryParams.mainClass || this.ups_line==this.productQueryParams.mainClass) {
        listUps(this.productQueryParams).then(response => {
          this.productList = response.rows;
          this.productTotal = response.total;
        });
      }
    },
    /**切换品牌查询系列*/
    getProductListSeries(value){
      //系列条件列表重置为空
      this.productQueryParams.series=null;

      //已选择的系列重置为空
      if (value==undefined || value==null || value<=0){
        this.productOption.series=null;
        return;
      }
      //根据品牌ID查询系列
      getAttrByFatherId(value).then(response=>{
        this.productOption.series=response.data;
      });
    },
    /** 搜索按钮操作 */
    productHandleQuery() {
      this.productQueryParams.pageNum = 1;
      this.getProductList();
    },
    /** 重置按钮操作 */
    productResetQuery() {
      this.resetForm("productQueryForm1");
      this.resetForm("productQueryForm2");
      this.resetForm("productQueryForm3");
      this.resetForm("productQueryForm4");
      this.productHandleQuery();
    },
    //添加产品到表单中
    handleAddProduct(row){


      if (this.form.detailList.some(x => x.productId === row.id)) {
        let item = this.form.detailList.find(x => x.productId === row.id);
        item.num++;


        this.productNumChange(item);
      } else {
        let item={
          "productId":row.id,
          "productTitle":row.title,
          "price":row.price,
          "num":1,
          "mainClass":row.mainClass,
          "type":"",
          "imgPath":row.img
        };
        this.form.detailList.push(item);


        this.productNumChange(item);
      }

      this.formOpen = false;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加信息";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getBoxlist(id).then(response => {
        this.handleUpdateInitTag(response.data.labelId);
        this.form = response.data;
        this.open = true;
        this.title = "修改信息"+this.form.title;
      });
    },
    /** 修改获取数据初始化标签 */
    handleUpdateInitTag(labelId){
      let that=this;


      that.formSelectCap=[];
      that.formSelectBrand=[];
      that.formSelectClassify=[];
      that.formSelectTime=[];
      that.formSelectOther=[];



      if(labelId==undefined || labelId==null || labelId==""){
        return;
      }
      let split = labelId.split(",");
      split.forEach(function (i){
        that.formSelectCap.push(parseInt(i));
        that.formSelectBrand.push(parseInt(i));
        that.formSelectClassify.push(parseInt(i));
        that.formSelectTime.push(parseInt(i));
        that.formSelectOther.push(parseInt(i));
      });


      let tagId = this.allOption.capLabel.map(x=>x.id);
      that.formSelectCap=that.formSelectCap.filter(x=> tagId.includes(x))

      tagId=this.allOption.brandLabel.map(x=>x.id);
      that.formSelectBrand=that.formSelectBrand.filter(x=> tagId.includes(x))

      tagId=this.allOption.classifyLabel.map(x=>x.id);
      that.formSelectClassify=that.formSelectClassify.filter(x=> tagId.includes(x))

      tagId = this.allOption.timeLabel.map(x=>x.id);
      that.formSelectTime=that.formSelectTime.filter(x=> tagId.includes(x))

      tagId = this.allOption.otherLabel.map(x=>x.id);
      that.formSelectOther=that.formSelectOther.filter(x=> tagId.includes(x))

    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {


          let labelId=[...this.formSelectCap,...this.formSelectBrand,...this.formSelectClassify,...this.formSelectTime,...this.formSelectOther];
          if(labelId.length>0){
            this.form.labelId=Array.from(new Set(labelId)).join(',');
          }else{
            this.form.labelId="";
          }

          if (this.form.id != null) {
            updateBoxlist(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addBoxlist(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除编号为"' + ids + '"的数据项？').then(function() {
        return delBoxlist(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /**表单中删除*/
    handleFormDelete(row){
      const productId= row.productId;
      this.form.detailList=this.form.detailList.filter(item => item.productId!== productId);
    },
    /** 导出按钮操作 */
    // handleExport() {
    //   this.download('system/ups/export', {
    //     ...this.queryParams
    //   }, `ups_${new Date().getTime()}.xlsx`)
    // }
  }
};
</script>



<!--<style scoped>-->
<!--.el-input-number /deep/ .el-input__inner {-->
<!--  text-align: left!important;-->
<!--}-->
<!--</style>-->
