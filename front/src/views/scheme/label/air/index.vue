<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">


      <el-form-item label="类别" prop="classsifyId">
        <el-select v-model="queryParams.classsifyId" placeholder="类别" clearable style="width: 100%;">
          <el-option v-for="dict in dict.type.label_classsify_id" v-if="label_classsify_id.includes(dict.value)" :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>


      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['system:ups:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"

        >删除</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="upsList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="ID" align="center" prop="id" />
      <el-table-column label="名称" align="center" prop="name" />

      <el-table-column prop="classsifyId" label="类别" width="150">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.label_classsify_id" :value="scope.row.classsifyId" />
        </template>
      </el-table-column>

      <el-table-column prop="labelClass" label="系统" width="150">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.classif" :value="scope.row.labelClass" />
        </template>
      </el-table-column>


      <el-table-column label="添加时间" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"

          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>



    <!-- 添加或修改 -->
    <el-dialog :title="title" :visible.sync="open" width="950px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">

        <el-row>
          <el-col :span="12">
            <el-form-item label="名称" prop="title">
              <el-input v-model="form.name" placeholder="请输入名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="名称(英文)" prop="titleEn">
              <el-input v-model="form.nameEn" placeholder="请输入名称(英文)" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="类别" prop="classsifyId">
              <el-select v-model="form.classsifyId" placeholder="类别" clearable style="width: 100%;">
                <el-option v-for="dict in dict.type.label_classsify_id" v-if="label_classsify_id.includes(dict.value)" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="排序" prop="sort">
              <el-input-number v-model="form.sort" controls-position="right" :min="0" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="点击量" prop="hits">
              <el-input-number v-model="form.hits" controls-position="right" :min="0" />
            </el-form-item>
          </el-col>
        </el-row>


      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import { listLabel,getLabel,addLabel,updateLabel,delLabel,getLabelDistinctByClassif } from "@/api/scheme/label";


export default {
  name: "Ups",
  dicts: ['classif','label_classsify_id'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // UPS信息
      upsList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        labelClass:this.classif_air,//默认方案类型
        classsifyId: null,
      },
      label_classsify_id:[],
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        name: [
          { required: true, message: "名称不能为空", trigger: "blur" }
        ],
        nameEn: [
          { required: true, message: "名称(英文)不能为空", trigger: "blur" }
        ],
        classsifyId: [
          { required: true, message: "制冷量不能为空", trigger: "change" }
        ]
      }
    };
  },
  created() {

    /**筛选条件*/
    this.getAllOption();

    this.getList();
  },
  methods: {
    /**选项*/
    getAllOption(){
      getLabelDistinctByClassif(this.queryParams.labelClass).then(response=>{
        this.label_classsify_id=response.data;
      })
    },
    /** 查询列表 */
    getList() {
      this.loading = true;
      listLabel(this.queryParams).then(response => {
        this.upsList = response.data;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        labelClass: this.queryParams.labelClass,
        id: undefined,
        name: undefined,
        nameEn: undefined,
        classsifyId: undefined,
        sort: undefined
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加信息";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getLabel(id).then(response => {

        response.data.classsifyId+="";

        this.form = response.data;
        this.open = true;
        this.title = "修改信息"+this.form.title;
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateLabel(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addLabel(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除编号为"' + ids + '"的数据项？').then(function() {
        return delLabel(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    // handleExport() {
    //   this.download('system/ups/export', {
    //     ...this.queryParams
    //   }, `ups_${new Date().getTime()}.xlsx`)
    // }
  }
};
</script>



<style scoped>
.el-input-number /deep/ .el-input__inner {
  text-align: left!important;
}
</style>
