<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">

      <el-form-item label="名称" prop="title">
        <el-input
          v-model="queryParams.title"
          placeholder="请输入名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item label="品牌" prop="brand">
        <el-select v-model="queryParams.brand" placeholder="品牌" clearable @change="getListSeries">
          <el-option v-for="item in allOption.brand" :key="item.id" :label="item.title" :value="item.id" />
        </el-select>
      </el-form-item>

      <el-form-item label="系列" prop="series">
        <el-select v-model="queryParams.series" placeholder="系列" clearable>
          <el-option v-for="item in allOption.series" :key="item.id" :label="item.title" :value="item.id" />
        </el-select>
      </el-form-item>


      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['system:ups:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
        >删除</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="upsList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="ID" align="center" prop="id" />
      <el-table-column label="名称" align="center" prop="title" />
      <el-table-column label="品牌" align="center" prop="brandTitle" />
      <el-table-column label="系列" align="center" prop="seriesTitle" />
      <el-table-column label="制冷量" align="center" prop="refrigeratingTitle" />
      <el-table-column label="冷却类型" align="center" prop="coolingTypeTitle" />
      <el-table-column label="空调类型" align="center" prop="airTypeTitle" />
      <el-table-column label="目录价格" align="center" prop="price" />
      <el-table-column label="零售价格" align="center" prop="priceRetail" />
      <el-table-column label="添加时间" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-goods"
            @click="handleShelves(scope.row)"
            :style="scope.row.isShow==0?'color: #FFB800;':''"
          >{{ scope.row.isShow==0?"上架":"下架" }}</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改 -->
    <el-dialog :title="title" :visible.sync="open" width="950px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">

        <el-row>
          <el-col :span="12">
            <el-form-item label="名称" prop="title">
              <el-input v-model="form.title" placeholder="请输入名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="名称(英文)" prop="titleEn">
              <el-input v-model="form.titleEn" placeholder="请输入名称(英文)" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="12">
            <el-form-item label="副标题" prop="subTitle">
              <el-input v-model="form.subTitle" placeholder="请输入副标题" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="副标题(英文)" prop="subTitleEn">
              <el-input v-model="form.subTitleEn" placeholder="请输入副标题(英文)" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="12">
            <el-form-item label="关联url" prop="linkUrl">
              <el-input v-model="form.linkUrl" placeholder="请输入关联url" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="69码" prop="barcode">
              <el-input type="number" v-model="form.barcode" placeholder="请输入69码" />
            </el-form-item>
          </el-col>
        </el-row>


        <el-row>
          <el-col :span="12">
            <el-form-item label="品牌" prop="brand">
              <el-select v-model="form.brand" placeholder="品牌" clearable @change="getFromSeries">
                <el-option v-for="item in allOption.brand" :key="item.id" :label="item.title" :value="item.id" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="系列" prop="series">
              <el-select v-model="form.series" placeholder="系列" clearable>
                <el-option v-for="item in fromSeries" :key="item.id" :label="item.title" :value="item.id" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>


        <el-row>
          <el-col :span="12">
            <el-form-item label="制冷量" prop="refrigerating">
              <el-select v-model="form.refrigerating" placeholder="容量" clearable>
                <el-option v-for="item in allOption.refrigerating" :key="item.id" :label="item.title" :value="item.id" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
              <el-form-item label="空调类型" prop="airType">
                <el-select v-model="form.airType" placeholder="空调类型" clearable>
                  <el-option v-for="item in allOption.air_type" :key="item.id" :label="item.title" :value="item.id" />
                </el-select>
              </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="12">
            <el-form-item label="送风方式" prop="airSupply">
              <el-select v-model="form.airSupply" placeholder="送风方式" clearable>
                <el-option v-for="item in allOption.air_supply" :key="item.id" :label="item.title" :value="item.id" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
              <el-form-item label="制热量" prop="heating">
                <el-select v-model="form.heating" placeholder="制热量" clearable>
                  <el-option v-for="item in allOption.heating" :key="item.id" :label="item.title" :value="item.id" />
                </el-select>
              </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="12">
            <el-form-item label="冷却类型" prop="coolingType">
              <el-select v-model="form.coolingType" placeholder="冷却类型" clearable>
                <el-option v-for="item in allOption.cooling_type" :key="item.id" :label="item.title" :value="item.id" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
              <el-form-item label="用电类型" prop="electricType">
                <el-select v-model="form.electricType" placeholder="用电类型" clearable>
                  <el-option v-for="item in allOption.electric_type" :key="item.id" :label="item.title" :value="item.id" />
                </el-select>
              </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="12">
            <el-form-item label="尺寸" prop="length">
              <el-input v-model="form.length" placeholder="请输入尺寸" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="12">
            <el-form-item label="重量" prop="weight">
              <el-input-number v-model="form.weight" placeholder="请输入重量" :controls="false" :min="0"  style="width: 100%;" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="毛重" prop="fullWeight">
              <el-input v-model="form.fullWeight" placeholder="请输入毛重" />
            </el-form-item>
          </el-col>
        </el-row>


        <el-row>
          <el-col :span="12">
            <el-form-item label="目录价格" prop="price">
              <el-input-number v-model="form.price" placeholder="请输入目录价格" :controls="false" :min="0"  style="width: 100%;" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="零售价格" prop="priceRetail">
              <el-input-number v-model="form.priceRetail" placeholder="请输入零售价格" :controls="false" :min="0"  style="width: 100%;" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="12">
            <el-form-item label="直营价" prop="directSalePrice">
              <el-input-number v-model="form.directSalePrice" placeholder="请输入直营价" :controls="false" :min="0"  style="width: 100%;"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="成本价" prop="priceCb">
              <el-input-number v-model="form.priceCb" placeholder="请输入成本价" :controls="false" :min="0"  style="width: 100%;"/>
            </el-form-item>
          </el-col>
        </el-row>



        <el-row>
          <el-col :span="12">
            <el-form-item label="生产状态" prop="produceState">
              <el-select v-model="form.produceState" placeholder="生产状态" clearable style="width: 100%;">
                <el-option v-for="dict in dict.type.produce_state" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="发布时间" prop="releaseTime">
              <el-date-picker clearable
                              v-model="form.releaseTime"
                              type="date"
                              value-format="yyyy-MM-dd HH:mm:ss"
                              placeholder="请选择发布时间">
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>


        <el-row>
          <el-col :span="12">
            <el-form-item label="货期" prop="deliveryDate">
              <el-input v-model="form.deliveryDate" placeholder="请输入货期" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="价格更新时间" prop="priceUpdateTime">
              <el-date-picker clearable
                              v-model="form.priceUpdateTime"
                              type="date"
                              value-format="yyyy-MM-dd HH:mm:ss"
                              placeholder="请选择价格更新时间">
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>


        <el-row>
          <el-col :span="12">
            <el-form-item label="质保期" prop="warrantyPeriod">
              <el-input v-model="form.warrantyPeriod" placeholder="请输入质保期" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24">
            <el-form-item label="备注" prop="remark">
              <el-input v-model="form.remark" placeholder="请输入备注" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24">
            <el-form-item label="主图" prop="img">
              <ImageUpload v-model="form.img" file-size="1"></ImageUpload>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24">
            <el-form-item label="详情图" prop="imgs">
              <ImageUpload v-model="form.imgs" file-size="1"></ImageUpload>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24">
            <el-form-item label="详情图英文" prop="imgsEn">
              <ImageUpload v-model="form.imgsEn" file-size="1"></ImageUpload>
            </el-form-item>
          </el-col>
        </el-row>


        <el-row>
          <el-col :span="24">
            <el-form-item label="详细参数" prop="content">
              <wangEditor v-if="open" v-model="form.detailParameters.content" :minHeight="200"/>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24">
            <el-form-item label="详细参数英文" prop="contentEn">
              <wangEditor v-if="open" v-model="form.detailParameters.contentEn" :minHeight="200"/>
            </el-form-item>
          </el-col>
        </el-row>

      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import { listAir, getAir, delAir, addAir, updateAir } from "@/api/product/air";
import { getAllAttrGroupByMainClass, getAttrByFatherId} from "@/api/product/attr";
import {updateIsShow} from "@/api/product/ups";

export default {
  name: "Ups",
  dicts: ['produce_state'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // UPS信息
      upsList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        mainClass:this.ari_indoor,//默认产品类型
        title: null,
        brand: null,
        series: null
      },
      allOption:[],//选项
      fromSeries:[],//添加修改表单使用的系列
      // 表单参数
      form: {
        detailParameters: {}
      },
      // 表单校验
      rules: {
        title: [
          { required: true, message: "名称不能为空", trigger: "blur" }
        ],
        titleEn: [
          { required: true, message: "名称(英文)不能为空", trigger: "blur" }
        ],
        subTitle: [
          { required: true, message: "副标题 简述不能为空", trigger: "blur" }
        ],
        subTitleEn: [
          { required: true, message: "副标题 简述(英文)不能为空", trigger: "blur" }
        ],
        brand: [
          { required: true, message: "品牌不能为空", trigger: "change" }
        ],
        series: [
          { required: true, message: "系列不能为空", trigger: "change" }
        ],
        refrigerating: [
          { required: true, message: "制冷量不能为空", trigger: "change" }
        ],
        airType: [
          { required: true, message: "空调类型不能为空", trigger: "change" }
        ],
        airSupply: [
          { required: true, message: "送风方式不能为空", trigger: "change" }
        ],
        heating: [
          { required: true, message: "制热量不能为空", trigger: "change" }
        ],
        coolingType: [
          { required: true, message: "冷却类型不能为空", trigger: "change" }
        ],
        electricType: [
          { required: true, message: "用电类型不能为空", trigger: "change" }
        ],
        length: [
          { required: true, message: "尺寸不能为空", trigger: "blur" }
        ],
        weight: [
          { required: true, message: "重量不能为空", trigger: "blur" }
        ],
        fullWeight: [
          { required: true, message: "毛重不能为空", trigger: "blur" }
        ],
        price: [
          { required: true, message: "目录价格不能为空", trigger: "blur" }
        ],
        priceRetail: [
          { required: true, message: "零售价格不能为空", trigger: "blur" }
        ],
        directSalePrice: [
          { required: true, message: "直营价不能为空", trigger: "blur" }
        ],
        priceCb: [
          { required: true, message: "成本价不能为空", trigger: "blur" }
        ],
        produceState: [
          { required: true, message: "生产状态不能为空", trigger: "change" }
        ],
        deliveryDate: [
          { required: true, message: "货期不能为空", trigger: "blur" }
        ],
        warrantyPeriod: [
          { required: true, message: "质保期不能为空", trigger: "blur" }
        ],
        img: [
          { required: true, message: "图片不能为空", trigger: "blur" }
        ],
        imgs: [
          { required: true, message: "详情图不能为空", trigger: "blur" }
        ]
      }
    };
  },
  created() {

    /**筛选条件*/
    this.getAllOption();

    this.getList();
  },
  methods: {
    /**选项*/
    getAllOption(){
      getAllAttrGroupByMainClass(this.ari_indoor).then(response=>{
        this.allOption = response.data;
      });
    },
    /**查询列表系列*/
    getListSeries(value){
      this.queryParams.series=null;

      if (value==undefined || value==null || value<=0){
        this.allOption.series=[];
        return;
      }
      getAttrByFatherId(value).then(response=>{
        this.allOption.series = response.data;
      });
    },
    /**查询表单系列*/
    getFromSeries(value){
      this.form.series=null;
      if (value==undefined || value==null || value<=0){
        this.fromSeries=[];
        return;
      }
      getAttrByFatherId(value).then(response=>{
        this.fromSeries = response.data;
      });
    },
    /** 查询列表 */
    getList() {
      this.loading = true;
      listAir(this.queryParams).then(response => {
        this.upsList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        mainClass: this.ari_indoor,
        id: undefined,
        title: undefined,
        titleEn: undefined,
        subTitle: undefined,
        subTitleEn: undefined,
        linkUrl: undefined,
        barcode: undefined,
        brand: undefined,
        series: undefined,
        refrigerating: undefined,
        airType: undefined,
        airSupply: undefined,
        heating: undefined,
        coolingType: undefined,
        electricType: undefined,
        length: undefined,
        weight: undefined,
        fullWeight: undefined,
        price: undefined,
        priceRetail: undefined,
        directSalePrice: undefined,
        priceCb: undefined,
        produceState: undefined,
        releaseTime: undefined,
        deliveryDate: undefined,
        warrantyPeriod: undefined,
        img: undefined,
        imgs: undefined,
        imgsEn: undefined,
        detailParameters: {}
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /**上下架*/
    handleShelves(row){
      let data={
        "id":row.id,
        "isShow":row.isShow==0?1:0
      };
      updateIsShow(data).then(response => {
        this.$modal.msgSuccess("操作成功");
        this.getList();
      });
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加信息";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getAir(id).then(response => {
        if(response.data.brand>0){
          this.getFromSeries(response.data.brand);
        }

        response.data.produceState+="";

        if (response.data.detailParameters == undefined || response.data.detailParameters == null) {
          response.data.detailParameters={};
        }

        this.form = response.data;
        this.open = true;
        this.title = "修改信息"+this.form.title;
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateAir(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addAir(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除编号为"' + ids + '"的数据项？').then(function() {
        return delAir(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    // handleExport() {
    //   this.download('system/ups/export', {
    //     ...this.queryParams
    //   }, `ups_${new Date().getTime()}.xlsx`)
    // }
  }
};
</script>



<style scoped>
.el-input-number /deep/ .el-input__inner {
  text-align: left!important;
}
</style>
