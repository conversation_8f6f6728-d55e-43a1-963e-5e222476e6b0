<template>
  <div class="app-container">

    <el-form v-show="showSearch" ref="queryForm" :model="queryParams" size="small" :inline="true">
      <el-form-item label="名称" prop="title">
        <el-input v-model="queryParams.title" placeholder="请输入名称" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>

      <el-form-item label="系统" prop="mainClass">
        <el-select v-model="queryParams.mainClass" filterable placeholder="系统" clearable>
          <el-option v-for="dict in dict.type.main_class" :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>


      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>


    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd">新增</el-button>
      </el-col>
<!--      <el-col :span="1.5">-->
<!--        <el-button type="info" plain icon="el-icon-sort" size="mini" @click="toggleExpandAll">展开/折叠</el-button>-->
<!--      </el-col>-->
      <right-toolbar :show-search.sync="showSearch" @queryTable="getList" />
    </el-row>

    <el-table v-if="refreshTable" v-loading="loading" :data="attrList" row-key="id" :default-expand-all="isExpandAll" :tree-props="{ children: 'children', hasChildren: 'hasChildren' }">
      <el-table-column prop="title" label="名称" :show-overflow-tooltip="true" width="200" />

      <el-table-column prop="mainClass" label="系统" width="150">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.main_class" :value="scope.row.mainClass" />
        </template>
      </el-table-column>

      <el-table-column prop="sort" label="排序" width="60" />

      <el-table-column prop="isShow" label="是否显示" width="80">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.customizable_yes_no" :value="scope.row.isShow" />
        </template>
      </el-table-column>

      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button  size="mini" type="text" icon="el-icon-edit"
                     @click="handleUpdate(scope.row)">修改</el-button>
          <el-button  size="mini" type="text" icon="el-icon-plus"
                     @click="handleAdd(scope.row)">新增</el-button>
          <el-button  size="mini" type="text" icon="el-icon-delete"
                     @click="handleDelete(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page-sizes="[5,10]"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />


    <!-- 添加或修改属性对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="680px" append-to-body>

      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-row>
          <el-col :span="24">
            <el-form-item label="上级属性">
              <treeselect v-model="form.fatherId" @select="departTreeSelected" :options="attrOptions" :normalizer="normalizer" :show-count="true" placeholder="选择上级属性" />
            </el-form-item>
          </el-col>

          <el-col :span="24">
            <el-form-item label="名称" prop="title">
              <el-input v-model="form.title" placeholder="请输入名称" />
            </el-form-item>
          </el-col>

          <el-col :span="24">
            <el-form-item label="英文名称" prop="titleEn">
              <el-input v-model="form.titleEn" placeholder="请输入英文名称" />
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="显示排序" prop="sort">
              <el-input-number v-model="form.sort" controls-position="right" :min="0" />
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="是否显示">
              <el-radio-group v-model="form.isShow">
                <el-radio v-for="dict in dict.type.customizable_yes_no" :key="dict.value" :label="dict.value">{{ dict.label }}
                </el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>

        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  list,
  getAttr,
  delAttr,
  addAttr,
  updateAttr, listAll
} from '@/api/product/attr'

import Treeselect from '@riophae/vue-treeselect'
import '@riophae/vue-treeselect/dist/vue-treeselect.css'
import IconSelect from '@/components/IconSelect'

export default {
  name: 'Attr',
  dicts: ['sys_show_hide', 'sys_normal_disable','main_class','customizable_yes_no'],
  components: {
    Treeselect,
    IconSelect
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      // 菜单表格树数据
      attrList: [],
      // 总条数
      total: 0,
      // 菜单树选项
      attrOptions: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 是否展开，默认全部折叠
      isExpandAll: false,
      // 重新渲染表格状态
      refreshTable: true,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 5,
        title: undefined,
        mainClass: undefined
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        title: [{
          required: true,
          message: '名称不能为空',
          trigger: 'blur'
        }],
        titleEn: [{
          required: true,
          message: '英文名称不能为空',
          trigger: 'blur'
        }],
        sort: [{
          required: true,
          message: '排序不能为空',
          trigger: 'blur'
        }],
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    //选择上级属性时，赋值mainClass
    departTreeSelected(node,instanceId){
      if (node != undefined && node != null && node.mainClass!=undefined) {
        this.form.mainClass=node.mainClass;
      }
    },
    // 选择图标
    selected(name) {
      this.form.icon = name
    },

    /** 查询列表 */
    getList() {
      this.loading = true
      list(this.queryParams).then((response) => {
        this.attrList = this.handleTree(response.rows,'id', 'fatherId','children',1)
        this.total = response.total;
        this.loading = false
      })
    },
    /** 转换菜单数据结构 */
    normalizer(node) {
      if (node.children && !node.children.length) {
        delete node.children
      }
      return {
        id: node.id,
        label: node.title,
        children: node.children
      }
    },

    /** 查询菜单下拉树结构 */
    getTreeselect() {
      if(this.attrOptions!=undefined && this.attrOptions!=null && this.attrOptions.length>0){
        return;
      }


      listAll().then((response) => {
        this.attrOptions = []

        const attr = {
          id: 0,
          title: '主类目',
          mainClass:0,
          children: []
        }
        attr.children = this.handleTree(response.data,'id', 'fatherId','children',1)
        this.attrOptions.push(attr)


        // let children = this.handleTree(response.data,'id', 'fatherId','children',1)
        // this.attrOptions.push(children)
      })
    },

    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },

    // 表单重置
    reset() {
      this.form = {
        id: undefined,
        fatherId: 0,
        mainClass:0,
        title: undefined,
        titleEn: undefined,
        sort: undefined,
        isShow: '1'
      }
      this.resetForm('form')
    },

    /** 搜索按钮操作 */
    handleQuery() {
      this.getList()
    },

    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    },

    /** 新增按钮操作 */
    handleAdd(row) {
      this.reset()
      this.getTreeselect()
      if (row != null && row.id) {
        this.form.fatherId = row.id
        this.form.mainClass = row.mainClass
      } else {
        this.form.fatherId = 0
      }
      this.open = true
      this.title = '添加属性'
    },

    /** 展开/折叠操作 */
    toggleExpandAll() {
      this.refreshTable = false
      this.isExpandAll = !this.isExpandAll
      this.$nextTick(() => {
        this.refreshTable = true
      })
    },

    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      this.getTreeselect()
      getAttr(row.id).then((response) => {
        response.data.isShow+="";
        this.form = response.data
        this.open = true
        this.title = '修改属性'
      })
    },
    /** 提交按钮 */
    submitForm: function () {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          if (this.form.id != undefined) {
            updateAttr(this.form).then((response) => {
              this.$modal.msgSuccess('修改成功')
              this.open = false
              this.getList()
            })
          } else {
            addAttr(this.form).then((response) => {
              this.$modal.msgSuccess('新增成功')
              this.open = false
              this.getList()
            })
          }
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      this.$modal
        .confirm('是否确认删除名称为"' + row.title + '"的数据项？')
        .then(function () {
          return delAttr(row.id)
        })
        .then(() => {
          this.getList()
          this.$modal.msgSuccess('删除成功')
        })
        .catch(() => {})
    }
  }
}

</script>
