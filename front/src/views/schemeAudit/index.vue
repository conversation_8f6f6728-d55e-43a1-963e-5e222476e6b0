<template>
  <div class="app-container">

    <el-table v-loading="loading" :data="dataList">
      <el-table-column label="编号" align="center" prop="listNo" />
      <el-table-column label="名称" align="center" prop="title" />
      <el-table-column label="作者" align="center" prop="author" />
      <el-table-column label="更新时间" align="center" prop="updateTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.updateTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleAudit(scope.row)">审核</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />


    <el-dialog :title="title" :visible.sync="open" width="1200px" append-to-body>
      <el-form ref="form" :model="form" label-width="120px">

                <el-row>
                  <el-col :span="24" style="text-align: center;">
                      <ImagePreview :src="form.imgPath" :width="200" :height="200" :showMultiple="true" :spacing="20"></ImagePreview>
                  </el-col>
                </el-row>

                <el-row style="margin-top: 50px;">
                  <el-col :span="24" style="text-align: center;">
                    {{form.title}}
                  </el-col>
                </el-row>

                <el-row>
                  <el-col :span="24">
                    <el-form-item label="作者：">
                      {{form.author}}
                    </el-form-item>
                  </el-col>
                </el-row>

                <el-row>
                  <el-col :span="24">
                    <el-form-item label="简述：">
                      {{form.subTitle}}
                    </el-form-item>
                  </el-col>
                </el-row>

                <el-row>
                  <el-col :span="24">
                    <el-table :data="form.detailList">
                      <el-table-column label="产品类别" align="center" prop="">
                        <template slot-scope="scope">
                          <dict-tag v-if="isNumber(scope.row.mainClass)" :options="dict.type.main_class" :value="scope.row.mainClass" />
                          <span v-else>{{scope.row.mainClass}}</span>
                        </template>
                      </el-table-column>

                      <el-table-column label="品牌" align="center" prop="brandTitle" />
                      <el-table-column label="产品名称" align="center" prop="productTitle" />
                      <el-table-column label="数量" align="center" prop="num"/>
                      <el-table-column label="单价" align="center" prop="price">
                        <template slot-scope="scope">
                          <span>
                            {{ scope.row.price>0?scope.row.price+'元':'' }}
                          </span>
                        </template>
                      </el-table-column>
                      <el-table-column label="总价" align="center" prop="totalPrice">
                        <template slot-scope="scope">
                          <span style="color: red;">
                            {{ scope.row.totalPrice>0?scope.row.totalPrice+'元':'' }}
                          </span>
                        </template>
                      </el-table-column>
                    </el-table>
                  </el-col>
                </el-row>

        <el-row style="margin-top: 20px;" v-if="form.classify!=classif_power_distribution_cabinet">
          <el-col :span="4">
            <el-form-item label="就位费：" style="color: red;">
              {{form.handlingFee>0?form.handlingFee+'元':''}}
            </el-form-item>
          </el-col>
          <el-col :span="4">

            <el-form-item label="安装费：" style="color: red;">
              {{form.install>0?form.install+'元':''}}
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item label="调试费：" style="color: red;">
              {{form.debugFee>0?form.debugFee+'元':''}}
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item label="运费：" style="color: red;">
              {{form.freight>0?form.freight+'元':''}}
            </el-form-item>
          </el-col>
          <el-col :span="5">
            <el-form-item label="参考费用总计：" style="color: red;">
              {{form.handlingFee+form.install+form.debugFee+form.freight+'元'}}
            </el-form-item>
          </el-col>
        </el-row>





        <el-row style="margin-top: 20px;">
          <el-col :span="24">
            <el-form-item>
              <el-button v-if="form.cadPath!== null && form.cadPath!== undefined && form.cadPath!== ''" type="primary" icon="el-icon-download" @click="downloadFile(form.cadPath)">CAD下载</el-button>
              <el-button v-if="form.pdfPath!== null && form.pdfPath!== undefined && form.pdfPath!== ''" type="primary" icon="el-icon-download" @click="downloadFile(form.pdfPath)">PDF下载</el-button>
              <el-button v-if="form.rarPath!== null && form.rarPath!== undefined && form.rarPath!== ''" type="primary" icon="el-icon-download" @click="downloadFile(form.rarPath)">RAR下载</el-button>
            </el-form-item>
          </el-col>
        </el-row>

      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="passed">通 过</el-button>
        <el-button @click="overrule">驳 回</el-button>
      </div>
    </el-dialog>


  </div>
</template>

<script>
import { listBoxlist, getBoxlist, delBoxlist, addBoxlist, updateBoxlist,passed,overrule } from "@/api/scheme/boxlist";
import empty from "element-ui/packages/empty";


export default {
  name: "Ups",
  computed: {
    empty() {
      return empty
    }
  },
  dicts: ['produce_state','main_class'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 总条数
      total: 0,
      //列表信息
      dataList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        status:0
      },
      // 表单参数
      form: {},
    };
  },
  created() {
    this.getList();
  },
  methods: {
    isNumber(value) {
      return!isNaN(value) && typeof value === 'number';
    },
    downloadFile(url) {
      if (url == undefined || url == null || url == '') {
        return;
      }
      const link = document.createElement('a');
      link.href = url;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    },
    /** 查询列表 */
    getList() {
      this.loading = true;
      listBoxlist(this.queryParams).then(response => {
        this.dataList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
    },

    /** 审核按钮操作 */
    handleAudit(row) {
      const id = row.id || this.ids
      getBoxlist(id).then(response => {
        if (response.data.detailList != null && response.data.detailList.length > 0) {
          let sums;
          const values =  response.data.detailList.map(item => Number(item["totalPrice"]));
          if (!values.every(value => isNaN(value))) {
            sums = values.reduce((prev, curr) => {
              const value = Number(curr);
              if (!isNaN(value)) {
                return prev + curr;
              } else {
                return prev;
              }
            }, 0);
          }

          response.data.detailList.push({
            "mainClass":"元件总计",
            "totalPrice":sums
          });

          //是配电柜方案的时候追加
          if (response.data.classify == this.classif_power_distribution_cabinet) {
            response.data.detailList.push({
              "mainClass":"组装费",
              "totalPrice":response.data.mprice
            });
            response.data.detailList.push({
              "mainClass":"辅料价格",
              "totalPrice":response.data.acprice
            });
            response.data.detailList.push({
              "mainClass":"运营费用",
              "totalPrice":response.data.profit
            });
            response.data.detailList.push({
              "mainClass":"税金",
              "totalPrice":response.data.tax
            });
            response.data.detailList.push({
              "mainClass":"钣金费用",
              "totalPrice":response.data.sheetMetalCost
            });
            response.data.detailList.push({
              "mainClass":"面板费用",
              "totalPrice":response.data.panelCost
            });
            response.data.detailList.push({
              "mainClass":"包装费",
              "totalPrice":response.data.packingFee
            });
          }

        }

        this.form = response.data;
        this.open = true;
        this.title = "审核信息"+this.form.title;
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {


          let labelId=[...this.formSelectCap,...this.formSelectBrand,...this.formSelectTime,...this.formSelectOther];
          if(labelId.length>0){
            this.form.labelId=Array.from(new Set(labelId)).join(',');
          }else{
            this.form.labelId="";
          }

          if (this.form.id != null) {
            updateBoxlist(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addBoxlist(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 通过按钮 */
    passed() {
      passed(this.form.id).then(response => {
          this.$modal.msgSuccess("审核成功");
          this.open = false;
          this.getList();
        });
    },
    /** 驳回按钮 */
    overrule() {
      overrule(this.form.id).then(response => {
          this.$modal.msgSuccess("驳回成功");
          this.open = false;
          this.getList();
        });
    }
  }
};
</script>



<!--<style scoped>-->
<!--.el-input-number /deep/ .el-input__inner {-->
<!--  text-align: left!important;-->
<!--}-->
<!--</style>-->
