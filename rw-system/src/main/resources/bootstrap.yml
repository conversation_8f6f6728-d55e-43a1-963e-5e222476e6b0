server:
  port: ${SERVER_PORT:8082}

# Spring
spring:
  main:
    allow-bean-definition-overriding: true
  application:
    # 应用名称
    name: rw-system
  profiles:
    # 环境配置
    active: ${SPRING_PROFILES_ACTIVE:dev}

# mybatis配置
mybatis-plus:
  # 搜索指定包别名
  typeAliasesPackage: com.rw.**.domain
  # 配置mapper的扫描，找到所有的mapper.xml映射文件
  mapperLocations: classpath:mapper/**/*.xml
  global-config:
    db-config:
      id-type: auto
      logic-delete-field: delFlag
      logic-delete-value: 1 # 逻辑已删除值(默认为 1)
      logic-not-delete-value: 0 # 逻辑未删除值(默认为 0)
# swagger配置
swagger:
  title: 系统模块接口文档
  license: Powered By Mints
  licenseUrl: https://bos-api.mintstech.art/swagger-ui/index.html
logging:
  config: classpath:logback-spring.xml