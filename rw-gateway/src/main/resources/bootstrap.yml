# Tomcat
server:
  port: ${SERVER_PORT:15000}

# Spring
spring:
  main:
    allow-circular-references: true
    allow-bean-definition-overriding: true
  application:
    # 应用名称
    name: rw-gateway
  profiles:
    # 环境配置
    active: ${SPRING_PROFILES_ACTIVE:dev}
#    sentinel:
#      # 取消控制台懒加载
#      eager: true
#      transport:
#        # 控制台地址
#        dashboard: 127.0.0.1:8718
#      # nacos配置持久化
#      datasource:
#        ds1:
#          nacos:
#            server-addr: 8.142.43.148:8848
#            dataId: sentinel-bohe-gateway
#            groupId: DEFAULT_GROUP
#            data-type: json
#            rule-type: flow
#logging:
#  config: classpath:logback-spring.xml
