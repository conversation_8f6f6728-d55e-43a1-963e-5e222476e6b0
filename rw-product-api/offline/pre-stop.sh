#!/bin/bash
#脚本说明: 通过kubernetes优雅退出策略，实现微服务提供者的委婉退出的目的，保证pod完整生命周期，保证业务的持续可用性.
#作者: 窦海超

#注册中心offline
#curl -X GET "http://${HOSTNAME}:$(netstat -ntlp | grep java | awk -F':' '{print $2}' | awk '{print $1}' | head -1)/offline"
curl -X GET "http://localhost:8080/offline"

#可以定义一个传参:$OFFLINE_TIMEOUT
if [ -z "$OFFLINE_TIMEOUT" ];then
    sleep 70
else
    sleep "$OFFLINE_TIMEOUT"
fi

#杀死服务并立刻重启容器
killall java
