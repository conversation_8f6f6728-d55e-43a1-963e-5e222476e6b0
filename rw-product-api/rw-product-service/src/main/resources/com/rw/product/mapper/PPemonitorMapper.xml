<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rw.product.mapper.PPemonitorMapper">


    <select id="page" resultType="com.rw.product.model.entity.PPemonitor">
        select id,
                CASE #{req.language} WHEN 'en' THEN title_en ELSE title END           AS title,
                CASE #{req.language} WHEN 'en' THEN sub_title_en ELSE sub_title END   AS sub_title,
               main_class,
               price_retail,
               brand,
               full_weight,
               width,
               business_id,
               release_time,
               produce_state,
               is_show,
               del_flag,
               create_id,
               create_by,
               create_time,
               update_id,
               update_by,
               update_time,
               remark,
                barcode,
                delivery_date,
                warranty_period,
                direct_sale_price,
                price_cb,
                price_update_time,
                img,
                CASE #{req.language} WHEN 'en' THEN imgs_en ELSE imgs END             AS imgs,
        from p_pemonitor
        where del_flag=0 and main_class=#{req.mainClass}
        <if test="req.isShow != null">
            and is_show = #{req.isShow}
        </if>
        <if test="req.title != null and req.title !=''">
            <choose>
                <when test="req.language=='en'">
                    and title_en like concat('%',#{req.title},'%')
                </when>
                <otherwise>
                    and title like concat('%',#{req.title},'%')
                </otherwise>
            </choose>
        </if>
    </select>



    <select id="getInfoById" resultType="com.rw.product.model.entity.PPemonitor">
        select id,
        CASE #{req.language} WHEN 'en' THEN title_en ELSE title END           AS title,
        CASE #{req.language} WHEN 'en' THEN sub_title_en ELSE sub_title END   AS sub_title,
        main_class,
        price_retail,
        brand,
        full_weight,
        width,
        business_id,
        release_time,
        produce_state,
        is_show,
        del_flag,
        create_id,
        create_by,
        create_time,
        update_id,
        update_by,
        update_time,
        remark,
        barcode,
        delivery_date,
        warranty_period,
        direct_sale_price,
        price_cb,
        price_update_time,
        img,
        CASE #{req.language} WHEN 'en' THEN imgs_en ELSE imgs END             AS imgs,
        from p_pemonitor
        where del_flag=0 and id=#{req.id}  and main_class=#{req.mainClass}
        <if test="req.title != null and req.title !=''">
            <choose>
                <when test="req.language=='en'">
                    and title_en like concat('%',#{req.title},'%')
                </when>
                <otherwise>
                    and title like concat('%',#{req.title},'%')
                </otherwise>
            </choose>
        </if>
    </select>
</mapper>