<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rw.product.mapper.PUpsMapper">


    <select id="page" resultType="com.rw.product.model.entity.PUps">
        select ups.id,
        CASE #{req.language} WHEN 'en' THEN ups.title_en ELSE ups.title END AS title,
        CASE #{req.language} WHEN 'en' THEN ups.sub_title_en ELSE ups.sub_title END AS sub_title,
        ups.main_class,
        ups.price,
        ups.price_retail,
        ups.brand,
        CASE #{req.language} WHEN 'en' THEN brand.title_en ELSE brand.title END AS brand_title,
        ups.series,
        CASE #{req.language} WHEN 'en' THEN series.title_en ELSE series.title END AS series_title,
        ups.capacity,
        CASE #{req.language} WHEN 'en' THEN capacity.title_en ELSE capacity.title END AS capacity_title,
        ups.weight,
        ups.full_weight,
        ups.length,
        ups.voltage_sort,
        CASE #{req.language} WHEN 'en' THEN voltage_sort.title_en ELSE voltage_sort.title END AS voltage_sort_title,
        ups.voltage,
        CASE #{req.language} WHEN 'en' THEN voltage.title_en ELSE voltage.title END AS voltage_title,
        ups.img,
        CASE #{req.language} WHEN 'en' THEN ups.imgs_en ELSE ups.imgs END             AS imgs,
        ups.link_url,
        ups.version,
        ups.battery_type,
        CASE #{req.language} WHEN 'en' THEN battery_type.title_en ELSE battery_type.title END AS battery_type_title,
        ups.battery_form,
        CASE #{req.language} WHEN 'en' THEN battery_form.title_en ELSE battery_form.title END AS battery_form_title,
        ups.line_diameter,
        CASE #{req.language} WHEN 'en' THEN line_diameter.title_en ELSE line_diameter.title END AS line_diameter_title,
        ups.hour_rate,
        CASE #{req.language} WHEN 'en' THEN hour_rate.title_en ELSE hour_rate.title END AS hour_rate_title,
        ups.release_time,
        ups.produce_state,
        ups.is_show,
        ups.del_flag,
        ups.create_id,
        ups.create_by,
        ups.create_time,
        ups.update_id,
        ups.update_by,
        ups.update_time,
        ups.remark,
        ups.barcode,
        ups.power,
        ups.structure,
        CASE #{req.language} WHEN 'en' THEN structure.title_en ELSE structure.title END AS structure_title,
        ups.delivery_date,
        ups.warranty_period,
        ups.direct_sale_price,
        ups.price_cb,
        ups.price_update_time,
        ups.reference_host_capacity
        from p_ups as ups
        left join p_attr as brand on ups.brand = brand.id and brand.del_flag=0
        left join p_attr as series on ups.series = series.id and series.del_flag=0
        left join p_attr as capacity on ups.capacity = capacity.id and capacity.del_flag=0
        left join p_attr as voltage_sort on ups.voltage_sort = voltage_sort.id and voltage_sort.del_flag=0
        left join p_attr as voltage on ups.voltage = voltage.id and voltage.del_flag=0
        left join p_attr as battery_type on ups.battery_type = battery_type.id and battery_type.del_flag=0
        left join p_attr as battery_form on ups.battery_form = battery_form.id and battery_form.del_flag=0
        left join p_attr as line_diameter on ups.line_diameter = line_diameter.id and line_diameter.del_flag=0
        left join p_attr as hour_rate on ups.hour_rate = hour_rate.id and hour_rate.del_flag=0
        left join p_attr as structure on ups.structure = structure.id and structure.del_flag=0
        where ups.del_flag=0 and ups.main_class=#{req.mainClass}
        <if test="req.isShow != null">
            and ups.is_show = #{req.isShow}
        </if>
        <if test="req.brand != null and req.brand > 0">
            and ups.brand = #{req.brand}
        </if>
        <if test="req.series != null and req.series > 0">
            and ups.series = #{req.series}
        </if>
        <if test="req.capacity != null and req.capacity > 0">
            and ups.capacity = #{req.capacity}
        </if>
        <if test="req.voltageSort != null and req.voltageSort > 0">
            and ups.voltage_sort = #{req.voltageSort}
        </if>
        <if test="req.voltage != null and req.voltage > 0">
            and ups.voltage = #{req.voltage}
        </if>
        <if test="req.lineDiameter != null and req.lineDiameter > 0">
            and ups.line_diameter = #{req.lineDiameter}
        </if>
        <if test="req.version != null and req.version != ''">
            and ups.version = #{req.version}
        </if>
        <if test="req.batteryType != null and req.batteryType > 0">
            and ups.battery_type = #{req.batteryType}
        </if>
        <if test="req.batteryForm != null and req.batteryForm > 0">
            and ups.battery_form = #{req.batteryForm}
        </if>
        <if test="req.hourRate != null and req.hourRate > 0">
            and ups.hour_rate = #{req.hourRate}
        </if>
        <if test="req.title != null and req.title !=''">
            <choose>
                <when test="req.language=='en'">
                    and ups.title_en like concat('%',#{req.title},'%')
                </when>
                <otherwise>
                    and ups.title like concat('%',#{req.title},'%')
                </otherwise>
            </choose>
        </if>
        order by ups.create_time desc
    </select>

    <select id="getInfoById" resultType="com.rw.product.model.entity.PUps">
        select ups.id,
        CASE #{req.language} WHEN 'en' THEN ups.title_en ELSE ups.title END AS title,
        CASE #{req.language} WHEN 'en' THEN ups.sub_title_en ELSE ups.sub_title END AS sub_title,
        ups.main_class,
        ups.price,
        ups.price_retail,
        ups.brand,
        CASE #{req.language} WHEN 'en' THEN brand.title_en ELSE brand.title END AS brand_title,
        ups.series,
        CASE #{req.language} WHEN 'en' THEN series.title_en ELSE series.title END AS series_title,
        ups.capacity,
        CASE #{req.language} WHEN 'en' THEN capacity.title_en ELSE capacity.title END AS capacity_title,
        ups.weight,
        ups.full_weight,
        ups.length,
        ups.voltage_sort,
        CASE #{req.language} WHEN 'en' THEN voltage_sort.title_en ELSE voltage_sort.title END AS voltage_sort_title,
        ups.voltage,
        CASE #{req.language} WHEN 'en' THEN voltage.title_en ELSE voltage.title END AS voltage_title,
        ups.img,
        CASE #{req.language} WHEN 'en' THEN ups.imgs_en ELSE ups.imgs END             AS imgs,
        ups.link_url,
        ups.version,
        ups.battery_type,
        CASE #{req.language} WHEN 'en' THEN battery_type.title_en ELSE battery_type.title END AS battery_type_title,
        ups.battery_form,
        CASE #{req.language} WHEN 'en' THEN battery_form.title_en ELSE battery_form.title END AS battery_form_title,
        ups.line_diameter,
        CASE #{req.language} WHEN 'en' THEN line_diameter.title_en ELSE line_diameter.title END AS line_diameter_title,
        ups.hour_rate,
        CASE #{req.language} WHEN 'en' THEN hour_rate.title_en ELSE hour_rate.title END AS hour_rate_title,
        ups.release_time,
        ups.produce_state,
        ups.is_show,
        ups.del_flag,
        ups.create_id,
        ups.create_by,
        ups.create_time,
        ups.update_id,
        ups.update_by,
        ups.update_time,
        ups.remark,
        ups.barcode,
        ups.power,
        ups.structure,
        CASE #{req.language} WHEN 'en' THEN structure.title_en ELSE structure.title END AS structure_title,
        ups.delivery_date,
        ups.warranty_period,
        ups.direct_sale_price,
        ups.price_cb,
        ups.price_update_time,
        ups.reference_host_capacity
        from p_ups as ups
        left join p_attr as brand on ups.brand = brand.id and brand.del_flag=0
        left join p_attr as series on ups.series = series.id and series.del_flag=0
        left join p_attr as capacity on ups.capacity = capacity.id and capacity.del_flag=0
        left join p_attr as voltage_sort on ups.voltage_sort = voltage_sort.id and voltage_sort.del_flag=0
        left join p_attr as voltage on ups.voltage = voltage.id and voltage.del_flag=0
        left join p_attr as battery_type on ups.battery_type = battery_type.id and battery_type.del_flag=0
        left join p_attr as battery_form on ups.battery_form = battery_form.id and battery_form.del_flag=0
        left join p_attr as line_diameter on ups.line_diameter = line_diameter.id and line_diameter.del_flag=0
        left join p_attr as hour_rate on ups.hour_rate = hour_rate.id and hour_rate.del_flag=0
        left join p_attr as structure on ups.structure = structure.id and structure.del_flag=0
        where ups.del_flag=0 and ups.id=#{req.id} and ups.main_class=#{req.mainClass}
        <if test="req.brand != null and req.brand > 0">
            and ups.brand = #{req.brand}
        </if>
        <if test="req.series != null and req.series > 0">
            and ups.series = #{req.series}
        </if>
        <if test="req.capacity != null and req.capacity > 0">
            and ups.capacity = #{req.capacity}
        </if>
        <if test="req.voltageSort != null and req.voltageSort > 0">
            and ups.voltage_sort = #{req.voltageSort}
        </if>
        <if test="req.voltage != null and req.voltage > 0">
            and ups.voltage = #{req.voltage}
        </if>
        <if test="req.lineDiameter != null and req.lineDiameter > 0">
            and ups.line_diameter = #{req.lineDiameter}
        </if>
        <if test="req.version != null and req.version !=''">
            and ups.version = #{req.version}
        </if>
        <if test="req.batteryType != null and req.batteryType > 0">
            and ups.battery_type = #{req.batteryType}
        </if>
        <if test="req.batteryForm != null and req.batteryForm > 0">
            and ups.battery_form = #{req.batteryForm}
        </if>
        <if test="req.hourRate != null and req.hourRate > 0">
            and ups.hour_rate = #{req.hourRate}
        </if>
        <if test="req.title != null and req.title !=''">
            <choose>
                <when test="req.language=='en'">
                    and ups.title_en like concat('%',#{req.title},'%')
                </when>
                <otherwise>
                    and ups.title like concat('%',#{req.title},'%')
                </otherwise>
            </choose>
        </if>
    </select>
</mapper>