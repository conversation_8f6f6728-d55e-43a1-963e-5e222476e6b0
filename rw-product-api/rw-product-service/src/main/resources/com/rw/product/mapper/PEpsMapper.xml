<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rw.product.mapper.PEpsMapper">


    <select id="page" resultType="com.rw.product.model.entity.PEps">
        select eps.id,
               CASE #{req.language} WHEN 'en' THEN eps.title_en ELSE eps.title END           AS title,
               CASE #{req.language} WHEN 'en' THEN eps.sub_title_en ELSE eps.sub_title END   AS sub_title,
               eps.main_class,
               eps.price,
               eps.price_retail,
               eps.brand,
               CASE #{req.language} WHEN 'en' THEN brand.title_en ELSE brand.title END       AS brand_title,
               eps.series,
               CASE #{req.language} WHEN 'en' THEN series.title_en ELSE series.title END     AS series_title,
               eps.img,
               CASE #{req.language} WHEN 'en' THEN eps.imgs_en ELSE eps.imgs END             AS imgs,
               eps.link_url,
               eps.weight,
               eps.full_weight,
               eps.width,
               eps.height,
               eps.deep,
               eps.capacity,
               CASE #{req.language} WHEN 'en' THEN capacity.title_en ELSE capacity.title END AS capacity_title,
               eps.classify,
               CASE #{req.language} WHEN 'en' THEN classify.title_en ELSE classify.title END AS classify_title,
               eps.release_time,
               eps.produce_state,
               eps.is_show,
               eps.del_flag,
               eps.create_id,
               eps.create_by,
               eps.create_time,
               eps.update_id,
               eps.update_by,
               eps.update_time,
               eps.remark,
               eps.barcode,
               eps.voltage_sort,
               CASE #{req.language} WHEN 'en' THEN voltage_sort.title_en ELSE voltage_sort.title END AS voltage_sort_title,
               eps.delivery_date,
               eps.warranty_period,
               eps.direct_sale_price,
               eps.price_cb,
               eps.price_update_time
        from p_eps as eps
                 left join p_attr as brand on eps.brand = brand.id and brand.del_flag=0
                 left join p_attr as series on eps.series = series.id and series.del_flag=0
                 left join p_attr as capacity on eps.capacity = capacity.id and capacity.del_flag=0
                 left join p_attr as classify on eps.classify = classify.id and classify.del_flag=0
                 left join p_attr as voltage_sort on eps.voltage_sort = voltage_sort.id and voltage_sort.del_flag=0
        where eps.del_flag=0 and eps.main_class=#{req.mainClass}
        <if test="req.isShow != null">
            and eps.is_show = #{req.isShow}
        </if>
        <if test="req.brand != null and req.brand > 0">
            and eps.brand = #{req.brand}
        </if>
        <if test="req.series != null and req.series > 0">
            and eps.series = #{req.series}
        </if>
        <if test="req.capacity != null and req.capacity > 0">
            and eps.capacity = #{req.capacity}
        </if>
        <if test="req.classify != null and req.classify > 0">
            and eps.classify = #{req.classify}
        </if>
        <if test="req.title != null and req.title !=''">
            <choose>
                <when test="req.language=='en'">
                    and eps.title_en like concat('%',#{req.title},'%')
                </when>
                <otherwise>
                    and eps.title like concat('%',#{req.title},'%')
                </otherwise>
            </choose>
        </if>
        order by eps.create_time desc
    </select>



    <select id="getInfoById" resultType="com.rw.product.model.entity.PEps">
        select eps.id,
               CASE #{req.language} WHEN 'en' THEN eps.title_en ELSE eps.title END           AS title,
               CASE #{req.language} WHEN 'en' THEN eps.sub_title_en ELSE eps.sub_title END   AS sub_title,
               eps.main_class,
               eps.price,
               eps.price_retail,
               eps.brand,
               CASE #{req.language} WHEN 'en' THEN brand.title_en ELSE brand.title END       AS brand_title,
               eps.series,
               CASE #{req.language} WHEN 'en' THEN series.title_en ELSE series.title END     AS series_title,
               eps.img,
               eps.imgs,
               CASE #{req.language} WHEN 'en' THEN eps.imgs_en ELSE eps.imgs END             AS imgs,
               eps.link_url,
               eps.weight,
               eps.full_weight,
               eps.width,
               eps.height,
               eps.deep,
               eps.capacity,
               CASE #{req.language} WHEN 'en' THEN capacity.title_en ELSE capacity.title END AS capacity_title,
               eps.classify,
               CASE #{req.language} WHEN 'en' THEN classify.title_en ELSE classify.title END AS classify_title,
               eps.release_time,
               eps.produce_state,
               eps.is_show,
               eps.del_flag,
               eps.create_id,
               eps.create_by,
               eps.create_time,
               eps.update_id,
               eps.update_by,
               eps.update_time,
               eps.remark,
               eps.barcode,
               eps.voltage_sort,
               CASE #{req.language} WHEN 'en' THEN voltage_sort.title_en ELSE voltage_sort.title END AS voltage_sort_title,
               eps.delivery_date,
               eps.warranty_period,
               eps.direct_sale_price,
               eps.price_cb,
               eps.price_update_time
        from p_eps as eps
        left join p_attr as brand on eps.brand = brand.id and brand.del_flag=0
        left join p_attr as series on eps.series = series.id and series.del_flag=0
        left join p_attr as capacity on eps.capacity = capacity.id and capacity.del_flag=0
        left join p_attr as classify on eps.classify = classify.id and classify.del_flag=0
        left join p_attr as voltage_sort on eps.voltage_sort = voltage_sort.id and voltage_sort.del_flag=0
        where eps.del_flag=0 and eps.id=#{req.id} and eps.main_class=#{req.mainClass}
        <if test="req.brand != null and req.brand > 0">
            and eps.brand = #{req.brand}
        </if>
        <if test="req.series != null and req.series > 0">
            and eps.series = #{req.series}
        </if>
        <if test="req.capacity != null and req.capacity > 0">
            and eps.capacity = #{req.capacity}
        </if>
        <if test="req.classify != null and req.classify > 0">
            and eps.classify = #{req.classify}
        </if>
        <if test="req.title != null and req.title !=''">
            <choose>
                <when test="req.language=='en'">
                    and eps.title_en like concat('%',#{req.title},'%')
                </when>
                <otherwise>
                    and eps.title like concat('%',#{req.title},'%')
                </otherwise>
            </choose>
        </if>

    </select>
</mapper>