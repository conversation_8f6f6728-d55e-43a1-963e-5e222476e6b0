<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rw.product.mapper.PRoomMapper">


    <select id="page" resultType="com.rw.product.model.entity.PRoom">
        select room.id,
               CASE #{req.language} WHEN 'en' THEN room.title_en ELSE room.title END         AS title,
               CASE #{req.language} WHEN 'en' THEN room.sub_title_en ELSE room.sub_title END AS sub_title,
               room.main_class,
               room.price,
               room.price_retail,
               room.brand,
               CASE #{req.language} WHEN 'en' THEN brand.title_en ELSE brand.title END       AS brand_title,
               room.series,
               CASE #{req.language} WHEN 'en' THEN series.title_en ELSE series.title END     AS series_title,
               room.img,
               CASE #{req.language} WHEN 'en' THEN room.imgs_en ELSE room.imgs END             AS imgs,
               room.link_url,
               room.weight,
               room.full_weight,
               room.width,
               room.height,
               room.deep,
               room.capacity,
               CASE #{req.language} WHEN 'en' THEN capacity.title_en ELSE capacity.title END AS capacity_title,
               room.classify,
               CASE #{req.language} WHEN 'en' THEN classify.title_en ELSE classify.title END AS classify_title,
               room.voltage,
               CASE #{req.language} WHEN 'en' THEN voltage.title_en ELSE voltage.title END   AS voltage_title,
               room.roles,
               CASE #{req.language} WHEN 'en' THEN roles.title_en ELSE roles.title END       AS roles_title,
               room.release_time,
               room.produce_state,
               room.is_show,
               room.del_flag,
               room.create_id,
               room.create_by,
               room.create_time,
               room.update_id,
               room.update_by,
               room.update_time,
               room.remark,
               room.barcode,
               room.delivery_date,
               room.warranty_period,
               room.direct_sale_price,
               room.price_cb,
               room.price_update_time
        from p_room as room
                 left join p_attr as brand on room.brand = brand.id and brand.del_flag=0
                 left join p_attr as series on room.series = series.id and series.del_flag=0
                 left join p_attr as capacity on room.capacity = capacity.id and capacity.del_flag=0
                 left join p_attr as classify on room.classify = classify.id and classify.del_flag=0
                 left join p_attr as voltage on room.voltage = voltage.id and voltage.del_flag=0
                 left join p_attr as roles on room.roles = roles.id and roles.del_flag=0
        where room.del_flag=0 and room.main_class=#{req.mainClass}
        <if test="req.isShow != null">
            and room.is_show = #{req.isShow}
        </if>
        <if test="req.brand != null and req.brand > 0">
            and room.brand = #{req.brand}
        </if>
        <if test="req.series != null and req.series > 0">
            and room.series = #{req.series}
        </if>
        <if test="req.capacity != null and req.capacity > 0">
            and room.capacity = #{req.capacity}
        </if>
        <if test="req.classify != null and req.classify > 0">
            and room.classify = #{req.classify}
        </if>
        <if test="req.voltage != null and req.voltage > 0">
            and room.voltage = #{req.voltage}
        </if>
        <if test="req.roles != null and req.roles > 0">
            and room.roles = #{req.roles}
        </if>
        <if test="req.title != null and req.title !=''">
            <choose>
                <when test="req.language=='en'">
                    and room.title_en like concat('%',#{req.title},'%')
                </when>
                <otherwise>
                    and room.title like concat('%',#{req.title},'%')
                </otherwise>
            </choose>
        </if>
        order by room.create_time desc
    </select>

    <select id="getInfoById" resultType="com.rw.product.model.entity.PRoom">
        select room.id,
               CASE #{req.language} WHEN 'en' THEN room.title_en ELSE room.title END         AS title,
               CASE #{req.language} WHEN 'en' THEN room.sub_title_en ELSE room.sub_title END AS sub_title,
               room.main_class,
               room.price,
               room.price_retail,
               room.brand,
               CASE #{req.language} WHEN 'en' THEN brand.title_en ELSE brand.title END       AS brand_title,
               room.series,
               CASE #{req.language} WHEN 'en' THEN series.title_en ELSE series.title END     AS series_title,
               room.img,
               room.imgs,
               CASE #{req.language} WHEN 'en' THEN room.imgs_en ELSE room.imgs END             AS imgs,
               room.link_url,
               room.weight,
               room.full_weight,
               room.width,
               room.height,
               room.deep,
               room.capacity,
               CASE #{req.language} WHEN 'en' THEN capacity.title_en ELSE capacity.title END AS capacity_title,
               room.classify,
               CASE #{req.language} WHEN 'en' THEN classify.title_en ELSE classify.title END AS classify_title,
               room.voltage,
               CASE #{req.language} WHEN 'en' THEN voltage.title_en ELSE voltage.title END   AS voltage_title,
               room.roles,
               CASE #{req.language} WHEN 'en' THEN roles.title_en ELSE roles.title END       AS roles_title,
               room.release_time,
               room.produce_state,
               room.is_show,
               room.del_flag,
               room.create_id,
               room.create_by,
               room.create_time,
               room.update_id,
               room.update_by,
               room.update_time,
               room.remark,
               room.barcode,
               room.delivery_date,
               room.warranty_period,
               room.direct_sale_price,
               room.price_cb,
               room.price_update_time
        from p_room as room
        left join p_attr as brand on room.brand = brand.id and brand.del_flag=0
        left join p_attr as series on room.series = series.id and series.del_flag=0
        left join p_attr as capacity on room.capacity = capacity.id and capacity.del_flag=0
        left join p_attr as classify on room.classify = classify.id and classify.del_flag=0
        left join p_attr as voltage on room.voltage = voltage.id and voltage.del_flag=0
        left join p_attr as roles on room.roles = roles.id and roles.del_flag=0
        where room.del_flag=0 and room.id=#{req.id} and room.main_class=#{req.mainClass}
        <if test="req.brand != null and req.brand > 0">
            and room.brand = #{req.brand}
        </if>
        <if test="req.series != null and req.series > 0">
            and room.series = #{req.series}
        </if>
        <if test="req.capacity != null and req.capacity > 0">
            and room.capacity = #{req.capacity}
        </if>
        <if test="req.classify != null and req.classify > 0">
            and room.classify = #{req.classify}
        </if>
        <if test="req.voltage != null and req.voltage > 0">
            and room.voltage = #{req.voltage}
        </if>
        <if test="req.roles != null and req.roles > 0">
            and room.roles = #{req.roles}
        </if>
        <if test="req.title != null and req.title !=''">
            <choose>
                <when test="req.language=='en'">
                    and room.title_en like concat('%',#{req.title},'%')
                </when>
                <otherwise>
                    and room.title like concat('%',#{req.title},'%')
                </otherwise>
            </choose>
        </if>

    </select>
</mapper>