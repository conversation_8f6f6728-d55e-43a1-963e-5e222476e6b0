<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rw.product.mapper.PAirMapper">


    <select id="page" resultType="com.rw.product.model.entity.PAir">
        select air.id,
               CASE #{req.language} WHEN 'en' THEN air.title_en ELSE air.title END                     AS title,
               CASE #{req.language} WHEN 'en' THEN air.sub_title_en ELSE air.sub_title END             AS sub_title,
               air.main_class,
               air.price,
               air.price_retail,
               air.price_cb,
               air.price_wholesale,
               air.brand,
               CASE #{req.language} WHEN 'en' THEN brand.title_en ELSE brand.title END                 AS brand_title,
               air.series,
               CASE #{req.language} WHEN 'en' THEN series.title_en ELSE series.title END               AS series_title,
               air.air_supply,
               CASE #{req.language} WHEN 'en' THEN air_supply.title_en ELSE air_supply.title END       AS air_supply_title,
               air.power,
               air.weight,
               air.full_weight,
               air.length,
                <if test="req.mainClass==24 or req.mainClass==25">
                    CASE #{req.language} WHEN 'en' THEN length.title_en ELSE length.title END               AS length_title,
                </if>
               air.heating,
               CASE #{req.language} WHEN 'en' THEN heating.title_en ELSE heating.title END             AS heating_title,
               air.refrigerating,
               CASE #{req.language} WHEN 'en' THEN refrigerating.title_en ELSE refrigerating.title END AS refrigerating_title,
               air.img,
               CASE #{req.language} WHEN 'en' THEN air.imgs_en ELSE air.imgs END             AS imgs,
               air.electric_type,
               CASE #{req.language} WHEN 'en' THEN electric_type.title_en ELSE electric_type.title END AS electric_type_title,
               air.air_type,
               CASE #{req.language} WHEN 'en' THEN air_type.title_en ELSE air_type.title END           AS air_type_title,
               air.cooling_type,
               CASE #{req.language} WHEN 'en' THEN cooling_type.title_en ELSE cooling_type.title END   AS cooling_type_title,
               air.link_url,
               air.accessory,
               CASE #{req.language} WHEN 'en' THEN accessory.title_en ELSE accessory.title END         AS accessory_title,
               air.release_time,
               air.produce_state,
               air.is_show,
               air.air_quantity,
               air.pic_count,
               air.flag,
               air.del_flag,
               air.create_id,
               air.create_by,
               air.create_time,
               air.update_id,
               air.update_by,
               air.update_time,
               air.remark,
        air.barcode,
        air.delivery_date,
        air.warranty_period,
        air.direct_sale_price,
        air.price_update_time
        from p_air as air
                 left join p_attr as brand on air.brand = brand.id and brand.del_flag=0
                 left join p_attr as series on air.series = series.id and series.del_flag=0
                 left join p_attr as air_supply on air.air_supply = air_supply.id and air_supply.del_flag=0
                <if test="req.mainClass==24 or req.mainClass==25">
                    left join p_attr as length on air.length = length.id
                </if>
                 left join p_attr as heating on air.heating = heating.id and heating.del_flag=0
                 left join p_attr as refrigerating on air.refrigerating = refrigerating.id and refrigerating.del_flag=0
                 left join p_attr as electric_type on air.electric_type = electric_type.id and electric_type.del_flag=0
                 left join p_attr as air_type on air.air_type = air_type.id and air_type.del_flag=0
                 left join p_attr as cooling_type on air.cooling_type = cooling_type.id and cooling_type.del_flag=0
                 left join p_attr as accessory on air.accessory = accessory.id and accessory.del_flag=0
        where air.del_flag=0 and air.main_class=#{req.mainClass}
        <if test="req.isShow != null">
            and air.is_show = #{req.isShow}
        </if>
        <if test="req.brand != null and req.brand > 0">
            and air.brand = #{req.brand}
        </if>
        <if test="req.series != null and req.series > 0">
            and air.series = #{req.series}
        </if>
        <if test="req.airSupply != null and req.airSupply > 0">
            and air.air_supply = #{req.airSupply}
        </if>
        <if test="req.length != null and req.length > 0">
            and air.length = #{req.length}
        </if>
        <if test="req.heating != null and req.heating > 0">
            and air.heating = #{req.heating}
        </if>
        <if test="req.refrigerating != null and req.refrigerating > 0">
            and air.refrigerating = #{req.refrigerating}
        </if>
        <if test="req.electricType != null and req.electricType > 0">
            and air.electric_type = #{req.electricType}
        </if>
        <if test="req.airType != null and req.airType > 0">
            and air.air_type = #{req.airType}
        </if>
        <if test="req.coolingType != null and req.coolingType > 0">
            and air.cooling_type = #{req.coolingType}
        </if>
        <if test="req.accessory != null and req.accessory > 0">
            and air.accessory = #{req.accessory}
        </if>
        <if test="req.title != null and req.title !=''">
            <choose>
                <when test="req.language=='en'">
                    and air.title_en like concat('%',#{req.title},'%')
                </when>
                <otherwise>
                    and air.title like concat('%',#{req.title},'%')
                </otherwise>
            </choose>
        </if>
        order by air.create_time desc
    </select>

    <select id="getInfoById" resultType="com.rw.product.model.entity.PAir">
        select air.id,
               CASE #{req.language} WHEN 'en' THEN air.title_en ELSE air.title END                     AS title,
               CASE #{req.language} WHEN 'en' THEN air.sub_title_en ELSE air.sub_title END             AS sub_title,
               air.main_class,
               air.price,
               air.price_retail,
               air.price_cb,
               air.price_wholesale,
               air.brand,
               CASE #{req.language} WHEN 'en' THEN brand.title_en ELSE brand.title END                 AS brand_title,
               air.series,
               CASE #{req.language} WHEN 'en' THEN series.title_en ELSE series.title END               AS series_title,
               air.air_supply,
               CASE #{req.language} WHEN 'en' THEN air_supply.title_en ELSE air_supply.title END       AS air_supply_title,
               air.power,
               air.weight,
               air.full_weight,
               air.length,
                <if test="req.mainClass==24 or req.mainClass==25">
                    CASE #{req.language} WHEN 'en' THEN length.title_en ELSE length.title END               AS length_title,
                </if>
               air.heating,
               CASE #{req.language} WHEN 'en' THEN heating.title_en ELSE heating.title END             AS heating_title,
               air.refrigerating,
               CASE #{req.language} WHEN 'en' THEN refrigerating.title_en ELSE refrigerating.title END AS refrigerating_title,
               air.img,
               CASE #{req.language} WHEN 'en' THEN air.imgs_en ELSE air.imgs END             AS imgs,
               air.electric_type,
               CASE #{req.language} WHEN 'en' THEN electric_type.title_en ELSE electric_type.title END AS electric_type_title,
               air.air_type,
               CASE #{req.language} WHEN 'en' THEN air_type.title_en ELSE air_type.title END           AS air_type_title,
               air.cooling_type,
               CASE #{req.language} WHEN 'en' THEN cooling_type.title_en ELSE cooling_type.title END   AS cooling_type_title,
               air.link_url,
               air.accessory,
               CASE #{req.language} WHEN 'en' THEN accessory.title_en ELSE accessory.title END         AS accessory_title,
               air.release_time,
               air.produce_state,
               air.is_show,
               air.air_quantity,
               air.pic_count,
               air.flag,
               air.del_flag,
               air.create_id,
               air.create_by,
               air.create_time,
               air.update_id,
               air.update_by,
               air.update_time,
               air.remark,
        air.barcode,
        air.delivery_date,
        air.warranty_period,
        air.direct_sale_price,
        air.price_update_time
        from p_air as air
        left join p_attr as brand on air.brand = brand.id and brand.del_flag=0
        left join p_attr as series on air.series = series.id and series.del_flag=0
        left join p_attr as air_supply on air.air_supply = air_supply.id and air_supply.del_flag=0
        <if test="req.mainClass==24 or req.mainClass==25">
            left join p_attr as length on air.length = length.id
        </if>
        left join p_attr as heating on air.heating = heating.id and heating.del_flag=0
        left join p_attr as refrigerating on air.refrigerating = refrigerating.id and refrigerating.del_flag=0
        left join p_attr as electric_type on air.electric_type = electric_type.id and electric_type.del_flag=0
        left join p_attr as air_type on air.air_type = air_type.id and air_type.del_flag=0
        left join p_attr as cooling_type on air.cooling_type = cooling_type.id and cooling_type.del_flag=0
        left join p_attr as accessory on air.accessory = accessory.id and accessory.del_flag=0
        where air.del_flag=0 and air.id=#{req.id} and air.main_class=#{req.mainClass}
        <if test="req.brand != null and req.brand > 0">
            and air.brand = #{req.brand}
        </if>
        <if test="req.series != null and req.series > 0">
            and air.series = #{req.series}
        </if>
        <if test="req.airSupply != null and req.airSupply > 0">
            and air.air_supply = #{req.airSupply}
        </if>
        <if test="req.length != null and req.length > 0">
            and air.length = #{req.length}
        </if>
        <if test="req.heating != null and req.heating > 0">
            and air.heating = #{req.heating}
        </if>
        <if test="req.refrigerating != null and req.refrigerating > 0">
            and air.refrigerating = #{req.refrigerating}
        </if>
        <if test="req.electricType != null and req.electricType > 0">
            and air.electric_type = #{req.electricType}
        </if>
        <if test="req.airType != null and req.airType > 0">
            and air.air_type = #{req.airType}
        </if>
        <if test="req.coolingType != null and req.coolingType > 0">
            and air.cooling_type = #{req.coolingType}
        </if>
        <if test="req.accessory != null and req.accessory > 0">
            and air.accessory = #{req.accessory}
        </if>
        <if test="req.title != null and req.title !=''">
            <choose>
                <when test="req.language=='en'">
                    and air.title_en like concat('%',#{req.title},'%')
                </when>
                <otherwise>
                    and air.title like concat('%',#{req.title},'%')
                </otherwise>
            </choose>
        </if>
    </select>
</mapper>