<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rw.product.mapper.PSingleMapper">


    <select id="page" resultType="com.rw.product.model.entity.PSingle">
        select single.id,
               CASE #{req.language} WHEN 'en' THEN single.title_en ELSE single.title END         AS title,
               CASE #{req.language} WHEN 'en' THEN single.sub_title_en ELSE single.sub_title END AS sub_title,
               single.main_class,
               single.price,
               single.price_retail,
               single.brand,
               CASE #{req.language} WHEN 'en' THEN brand.title_en ELSE brand.title END           AS brand_title,
               single.series,
               CASE #{req.language} WHEN 'en' THEN series.title_en ELSE series.title END         AS series_title,
               single.img,
               CASE #{req.language} WHEN 'en' THEN single.imgs_en ELSE single.imgs END             AS imgs,
               single.link_url,
               single.weight,
               single.full_weight,
               single.width,
               single.height,
               single.deep,
               single.capacity,
               CASE #{req.language} WHEN 'en' THEN capacity.title_en ELSE capacity.title END     AS capacity_title,
               single.principle,
               CASE #{req.language} WHEN 'en' THEN principle.title_en ELSE principle.title END   AS principle_title,
               single.accuracy,
               CASE #{req.language} WHEN 'en' THEN accuracy.title_en ELSE accuracy.title END     AS accuracy_title,
               single.voltage,
               CASE #{req.language} WHEN 'en' THEN voltage.title_en ELSE voltage.title END       AS voltage_title,
               single.input,
               CASE #{req.language} WHEN 'en' THEN input.title_en ELSE input.title END           AS input_title,
               single.release_time,
               single.produce_state,
               single.is_show,
               single.del_flag,
               single.create_id,
               single.create_by,
               single.create_time,
               single.update_id,
               single.update_by,
               single.update_time,
               single.remark,
        single.barcode,
        single.delivery_date,
        single.warranty_period,
        single.direct_sale_price,
        single.price_cb,
        single.price_update_time
        from p_single as single
                 left join p_attr as brand on single.brand = brand.id and brand.del_flag=0
                 left join p_attr as series on single.series = series.id and series.del_flag=0
                 left join p_attr as capacity on single.capacity = capacity.id and capacity.del_flag=0
                 left join p_attr as principle on single.principle = principle.id and principle.del_flag=0
                 left join p_attr as accuracy on single.accuracy = accuracy.id and accuracy.del_flag=0
                 left join p_attr as voltage on single.voltage = voltage.id and voltage.del_flag=0
                 left join p_attr as input on single.input = input.id and input.del_flag=0
        where single.del_flag=0 and single.main_class=#{req.mainClass}
        <if test="req.isShow != null">
            and single.is_show = #{req.isShow}
        </if>
        <if test="req.brand != null and req.brand > 0">
            and single.brand = #{req.brand}
        </if>
        <if test="req.series != null and req.series > 0">
            and single.series = #{req.series}
        </if>
        <if test="req.capacity != null and req.capacity > 0">
            and single.capacity = #{req.capacity}
        </if>
        <if test="req.principle != null and req.principle > 0">
            and single.principle = #{req.principle}
        </if>
        <if test="req.accuracy != null and req.accuracy > 0">
            and single.accuracy = #{req.accuracy}
        </if>
        <if test="req.voltage != null and req.voltage > 0">
            and single.voltage = #{req.voltage}
        </if>
        <if test="req.input != null and req.input > 0">
            and single.input = #{req.input}
        </if>
        <if test="req.title != null and req.title !=''">
            <choose>
                <when test="req.language=='en'">
                    and single.title_en like concat('%',#{req.title},'%')
                </when>
                <otherwise>
                    and single.title like concat('%',#{req.title},'%')
                </otherwise>
            </choose>
        </if>
        order by single.create_time desc
    </select>

    <select id="getInfoById" resultType="com.rw.product.model.entity.PSingle">
        select single.id,
               CASE #{req.language} WHEN 'en' THEN single.title_en ELSE single.title END         AS title,
               CASE #{req.language} WHEN 'en' THEN single.sub_title_en ELSE single.sub_title END AS sub_title,
               single.main_class,
               single.price,
               single.price_retail,
               single.brand,
               CASE #{req.language} WHEN 'en' THEN brand.title_en ELSE brand.title END           AS brand_title,
               single.series,
               CASE #{req.language} WHEN 'en' THEN series.title_en ELSE series.title END         AS series_title,
               single.img,
               single.imgs,
               CASE #{req.language} WHEN 'en' THEN single.imgs_en ELSE single.imgs END             AS imgs,
               single.link_url,
               single.weight,
               single.full_weight,
               single.width,
               single.height,
               single.deep,
               single.capacity,
               CASE #{req.language} WHEN 'en' THEN capacity.title_en ELSE capacity.title END     AS capacity_title,
               single.principle,
               CASE #{req.language} WHEN 'en' THEN principle.title_en ELSE principle.title END   AS principle_title,
               single.accuracy,
               CASE #{req.language} WHEN 'en' THEN accuracy.title_en ELSE accuracy.title END     AS accuracy_title,
               single.voltage,
               CASE #{req.language} WHEN 'en' THEN voltage.title_en ELSE voltage.title END       AS voltage_title,
               single.input,
               CASE #{req.language} WHEN 'en' THEN input.title_en ELSE input.title END           AS input_title,
               single.release_time,
               single.produce_state,
               single.is_show,
               single.del_flag,
               single.create_id,
               single.create_by,
               single.create_time,
               single.update_id,
               single.update_by,
               single.update_time,
               single.remark,
        single.barcode,
        single.delivery_date,
        single.warranty_period,
        single.direct_sale_price,
        single.price_cb,
        single.price_update_time
        from p_single as single
        left join p_attr as brand on single.brand = brand.id and brand.del_flag=0
        left join p_attr as series on single.series = series.id and series.del_flag=0
        left join p_attr as capacity on single.capacity = capacity.id and capacity.del_flag=0
        left join p_attr as principle on single.principle = principle.id and principle.del_flag=0
        left join p_attr as accuracy on single.accuracy = accuracy.id and accuracy.del_flag=0
        left join p_attr as voltage on single.voltage = voltage.id and voltage.del_flag=0
        left join p_attr as input on single.input = input.id and input.del_flag=0
        where single.del_flag=0 and single.id=#{req.id} and single.main_class=#{req.mainClass}
        <if test="req.brand != null and req.brand > 0">
            and single.brand = #{req.brand}
        </if>
        <if test="req.series != null and req.series > 0">
            and single.series = #{req.series}
        </if>
        <if test="req.capacity != null and req.capacity > 0">
            and single.capacity = #{req.capacity}
        </if>
        <if test="req.principle != null and req.principle > 0">
            and single.principle = #{req.principle}
        </if>
        <if test="req.accuracy != null and req.accuracy > 0">
            and single.accuracy = #{req.accuracy}
        </if>
        <if test="req.voltage != null and req.voltage > 0">
            and single.voltage = #{req.voltage}
        </if>
        <if test="req.input != null and req.input > 0">
            and single.input = #{req.input}
        </if>
        <if test="req.title != null and req.title !=''">
            <choose>
                <when test="req.language=='en'">
                    and single.title_en like concat('%',#{req.title},'%')
                </when>
                <otherwise>
                    and single.title like concat('%',#{req.title},'%')
                </otherwise>
            </choose>
        </if>
    </select>
</mapper>