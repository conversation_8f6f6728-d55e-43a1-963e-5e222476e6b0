<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE configuration
        PUBLIC "-//mybatis.org//DTD Config 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-config.dtd">
<configuration>

    <settings>
        <setting name="cacheEnabled" value="true"/>
        <setting name="mapUnderscoreToCamelCase" value="true"/>
        <setting name="logImpl" value="org.apache.ibatis.logging.stdout.StdOutImpl"/>
    	<setting name="callSettersOnNulls" value="true"/>
    </settings>
    <!--<plugins>-->
        <!--<plugin interceptor="com.cloud.database.interceptor.MybatisInterceptor">-->
        <!--</plugin>-->
    <!--</plugins>-->
</configuration>