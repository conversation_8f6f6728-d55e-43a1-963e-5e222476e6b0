<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rw.product.mapper.SBoxlistMapper">


    <select id="getBoxList10ByProductId" resultType="com.rw.product.model.entity.SBoxlist">
        select
            b.id,
            b.list_no,
            b.usertype,
            b.userid,
            b.is_flag,
            CASE #{req.language} WHEN 'en' THEN b.title_en ELSE b.title END                     AS title,
            CASE #{req.language} WHEN 'en' THEN b.sub_title_en ELSE b.sub_title END             AS sub_title,
            b.label_id,
            b.classify,
            b.is_show,
            b.cad_path,
            b.pdf_path,
            b.rar_path,
            b.img_path,
            b.status,
            b.author,
            b.second_author,
            b.freight,
            b.install,
            b.handling_fee,
            b.debug_fee,
            b.mprice,
            b.profit,
            b.acprice,
            b.tax,
            b.sheet_metal_cost,
            b.panel_cost,
            b.packing_fee,
            b.eleprice,
            b.like_count,
            b.sort,
            b.category,
            b.input_cap,
            b.ways,
            b.low,
            b.secondary,
            b.high,
            b.del_flag,
            b.create_id,
            b.create_by,
            b.create_time,
            b.update_id,
            b.update_by,
            b.update_time,
            b.remark
        from s_boxlist as b
        join s_boxlist_detail as d on b.list_no = d.list_no
        where b.del_flag = 0
          and d.del_flag = 0
          and d.main_class = #{req.mainClass}
          and d.product_id = #{req.productId}
          and b.userid=#{req.userId}
        order by d.id desc
    </select>






    <select id="page" resultType="com.rw.product.model.entity.SBoxlist">
        select id,
               list_no,
               usertype,
               userid,
               is_flag,
               CASE #{req.language} WHEN 'en' THEN title_en ELSE title END                     AS title,
               CASE #{req.language} WHEN 'en' THEN sub_title_en ELSE sub_title END             AS sub_title,
               label_id,
               classify,
               is_show,
               cad_path,
               pdf_path,
               rar_path,
               img_path,
               status,
               author,
               second_author,
               freight,
               install,
               handling_fee,
               debug_fee,
               mprice,
               profit,
               acprice,
               tax,
               sheet_metal_cost,
               panel_cost,
               packing_fee,
               eleprice,
               like_count,
               sort,
               category,
               input_cap,
               ways,
               low,
               secondary,
               high,
               del_flag,
               create_id,
               create_by,
               create_time,
               update_id,
               update_by,
               update_time,
               remark
        from s_boxlist
        where del_flag = 0
        <if test="req.isShow != null">
            and is_show = #{req.isShow}
        </if>
        <if test="req.classify != null and req.classify>0">
            and classify = #{req.classify}
        </if>
        <if test="req.usertype != null and req.usertype>0">
            and usertype = #{req.usertype}
        </if>
        <if test="req.userid != null and req.userid!=''">
            and userid = #{req.userid}
        </if>
        <if test="req.status != null">
            and status = #{req.status}
        </if>
        <if test="req.labelId != null and req.labelId !='' ">
            <foreach collection="req.labelId.split(',')" item="id" separator=" and " open="and ( " close=" ) ">
                find_in_set(#{id},label_id)>0
            </foreach>
        </if>
    </select>


    <select id="getInfoById" resultType="com.rw.product.model.entity.SBoxlist">
        select
        id,
        list_no,
        usertype,
        userid,
        is_flag,
        CASE #{req.language} WHEN 'en' THEN title_en ELSE title END                     AS title,
        CASE #{req.language} WHEN 'en' THEN sub_title_en ELSE sub_title END             AS sub_title,
        label_id,
        classify,
        is_show,
        cad_path,
        pdf_path,
        rar_path,
        img_path,
        status,
        author,
        second_author,
        freight,
        install,
        handling_fee,
        debug_fee,
        mprice,
        profit,
        acprice,
        tax,
        sheet_metal_cost,
        panel_cost,
        packing_fee,
        eleprice,
        like_count,
        sort,
        category,
        input_cap,
        ways,
        low,
        secondary,
        high,
        del_flag,
        create_id,
        create_by,
        create_time,
        update_id,
        update_by,
        update_time,
        remark
        from s_boxlist
        where del_flag = 0 and list_no=#{req.listNo}
        <if test="req.classify != null">
            and classify = #{req.classify}
        </if>

        <if test="req.isShow != null">
            and is_show = #{req.isShow}
        </if>
        <if test="req.usertype != null and req.usertype>0">
            and usertype = #{req.usertype}
        </if>
        <if test="req.userid != null and req.userid!=''">
            and userid = #{req.userid}
        </if>
        <if test="req.status != null">
            and status = #{req.status}
        </if>
    </select>


</mapper>