package com.rw.product.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.rw.common.core.web.controller.BaseController;
import com.rw.common.core.web.domain.AjaxResult;
import com.rw.common.core.web.page.TableDataInfo;
import com.rw.product.mapper.SBoxlistDetailMapper;
import com.rw.product.model.entity.SBoxlistDetail;
import com.rw.product.service.ISBoxlistDetailService;
import com.rw.product.service.UserService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.List;
/**
 * 方案详情Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-07-23
 */
@Slf4j
@RestController
@AllArgsConstructor
public class SBoxlistDetailServiceImpl extends BaseController implements ISBoxlistDetailService
{
    @Autowired
    UserService userService;
    @Autowired
    SBoxlistDetailMapper sBoxlistDetailMapper;

    /**
     * 查询方案详情列表
     */
    @Override
    public TableDataInfo list(SBoxlistDetail sBoxlistDetail)
    {
        startPage();
        QueryWrapper<SBoxlistDetail> queryWrapper = new QueryWrapper<>();
        queryWrapper.setEntity(sBoxlistDetail);
        queryWrapper.orderByDesc("id");
        List<SBoxlistDetail> list =sBoxlistDetailMapper.selectList(queryWrapper);
        return getDataTable(list);
    }


    /**
     * 获取方案详情详细信息
     */
    @Override
    public AjaxResult getInfo(@PathVariable("id") Integer id)
    {
        return AjaxResult.success(sBoxlistDetailMapper.selectById(id));
    }

    /**
     * 新增方案详情
     */
    @Override
    public AjaxResult add(@RequestBody SBoxlistDetail sBoxlistDetail)
    {
        sBoxlistDetail.setCreateId(userService.getUserId());
        sBoxlistDetail.setCreateBy(userService.getNickname());
        return toAjax(sBoxlistDetailMapper.insert(sBoxlistDetail));
    }

    /**
     * 修改方案详情
     */
    @Override
    public AjaxResult edit(@RequestBody SBoxlistDetail sBoxlistDetail)
    {
        sBoxlistDetail.setUpdateId(userService.getUserId());
        sBoxlistDetail.setUpdateBy(userService.getNickname());
        return toAjax(sBoxlistDetailMapper.updateById(sBoxlistDetail));
    }

    /**
     * 删除方案详情
     */
    @Override
    public AjaxResult remove(@PathVariable Integer[] ids)
    {
        return toAjax(sBoxlistDetailMapper.deleteBatchIds(Arrays.asList(ids)));
    }
}
