package com.rw.product.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.rw.common.core.web.controller.BaseController;
import com.rw.product.mapper.PDetailParametersMapper;
import com.rw.product.model.entity.PDetailParameters;
import com.rw.product.service.IPDetailParametersService;
import com.rw.product.service.UserService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

/**
 * 详情参数
 */
@Slf4j
@RestController
@AllArgsConstructor
public class PDetailParametersServiceImpl extends BaseController implements IPDetailParametersService
{
    @Autowired
    UserService userService;
    @Autowired
    PDetailParametersMapper detailParametersMapper;



    /**
     * 查询列
     * @param language
     * @return
     */
    private String getColumns(String language){
        String columns="id, main_class, product_id, content, content_en, del_flag, create_id, create_by,create_time, update_id, update_by, update_time, remark";
        if ("en".equals(language)) {
            columns = "id, main_class, product_id, content_en as content, del_flag, create_id, create_by,create_time, update_id, update_by, update_time, remark";
        }
        return columns;
    }


    public int addDetailParameters(PDetailParameters pDetailParameters){

        QueryWrapper<PDetailParameters> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("product_id", pDetailParameters.getProductId());
        queryWrapper.eq("main_class", pDetailParameters.getMainClass());

        int result =0;
        if (detailParametersMapper.selectCount(queryWrapper)>0) {
            result = detailParametersMapper.update(pDetailParameters, queryWrapper);
        }else{
            result = detailParametersMapper.insert(pDetailParameters);
        }
        return result;
    }


    public PDetailParameters getOneByProductIdAndMainClass(PDetailParameters pDetailParameters){
        QueryWrapper<PDetailParameters> queryWrapper = new QueryWrapper<>();
        queryWrapper.select(getColumns(pDetailParameters.getLanguage()));
        queryWrapper.eq("product_id", pDetailParameters.getProductId());
        queryWrapper.eq("main_class", pDetailParameters.getMainClass());
        return detailParametersMapper.selectOne(queryWrapper);
    }

}
