package com.rw.product.utils.uploadFile;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.http.*;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Slf4j
@Component
public class FileUploadUtil {

    @Value("${file.upload.url}")
    private String loadUploadUrl;

    @Value("${ali.domain}")
    private String loadAliDomain;


    private static String aliDomain;

    private static String uploadUrl;

    @PostConstruct
    public void getAliDomain() {
        aliDomain = this.loadAliDomain;
    }

    @PostConstruct
    public void getUploadUrl() {
        uploadUrl = this.loadUploadUrl;
    }


    public static String uploadFile2AliOssSplit(String urlStr) {
        if(StrUtil.isEmpty(urlStr)){
            return urlStr;
        }

        List<String> urlList = new ArrayList<>();
        for (String url : urlStr.split(",")) {
            String aliUrl = uploadFile2AliOss(url);
            if (StrUtil.isNotBlank(aliUrl)) {
                urlList.add(aliUrl);
            }
        }

        if (urlList != null && urlList.size() > 0) {
            return StrUtil.join(",", urlList);
        }
        return "";
    }


    public static String uploadFile2AliOss(String url) {


        if (StringUtils.isEmpty(uploadUrl) || StrUtil.isEmpty(url)) {
            return "";
        }

        //如果已经是OSS地址了直接返回
        if (url.contains(aliDomain)) {
            return url;
        }


        try {
            //下载文件
            byte[] imageByteArray = HttpUtil.downloadBytes(url);

            //截取名称
            String originalFilename = StrUtil.sub(url, url.lastIndexOf("/") + 1, url.length());

            // 创建一个模拟的 MultipartFile
            MultipartFile file = new MockMultipartFile(
                    "file",
                    originalFilename,
                    "multipart/form-data",
                    imageByteArray
            );

            RestTemplate restTemplate = new RestTemplate();
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.MULTIPART_FORM_DATA);

            MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();
            try {
                body.add("file", new MultipartInputStreamFileResource(file.getBytes(), file.getOriginalFilename()));
                body.add("dir", "product");
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
            HttpEntity<MultiValueMap<String, Object>> requestEntity = new HttpEntity<>(body, headers);
            ResponseEntity<UploadFileResultDTO> response = restTemplate.postForEntity(uploadUrl, requestEntity, UploadFileResultDTO.class);
            if (response.getStatusCode() == HttpStatus.OK && Objects.nonNull(response.getBody())) {
                return response.getBody().getData().getUrl();
            }
        } catch (RuntimeException e) {
            log.error("下载图片失败{}",url);
        }

        return "";
    }


    public static class MultipartInputStreamFileResource extends ByteArrayResource {
        private final String filename;

        MultipartInputStreamFileResource(byte[] byteArray, String filename) {
            super(byteArray);
            this.filename = filename;
        }

        @Override
        public String getFilename() {
            return this.filename;
        }
    }

}
