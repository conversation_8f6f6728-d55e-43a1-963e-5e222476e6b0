package com.rw.product.utils;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.rw.common.redis.service.RedisService;
import com.rw.product.mapper.PAttrMapper;
import com.rw.product.model.entity.PAttr;
import org.apache.commons.collections4.map.LinkedMap;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 产品筛选条件
 */
@Service
public class ProductConditionsUtil {
    /**
     * @Fields rm : Redis的业务 数据输入输出
     */
    @Autowired
    private RedisService rm;

    @Autowired
    private PAttrMapper attrMapper;


    /**
     * @Fields bundle : 产品功能参数文件
     */
    private static final ResourceBundle PRODUCTS_FUNCTION = ResourceBundle.getBundle("products");


    /**
     * 获取筛选参数
     *
     * @param mainclass
     * @return
     */
    public Map<String, Object> getProductParameters(Integer mainclass) {

        Map<String, Object> map = new LinkedMap();
        List<PAttr> accessory = new ArrayList<PAttr>();
        List<PAttr> brand = new ArrayList<PAttr>();
        List<PAttr> series = new ArrayList<PAttr>();
        String classField = getClassField(mainclass);
        List<String> functions = new ArrayList<String>();
        try {
            Collections.addAll(functions, PRODUCTS_FUNCTION.getString(classField + "functions").split(","));
        } catch (Exception e) {

        }


        for (String field : functions) {
            // 排除map已有 accessory-brand-series 和 brand-series 的项
            if (map.containsKey("accessory") && ("brand".equals(field) || "series".equals(field)) || (map.containsKey("brand") && "series".equals(field) && mainclass != 15)) {
                continue;
                // 有brand-series的项 得到brand-series
            } else if (!map.containsKey("brand") && ("brand".equals(field) || "series".equals(field))) {

                try {
                    PAttr pAttr = new PAttr();
                    pAttr.setFatherId(Integer.parseInt(PRODUCTS_FUNCTION.getString(classField + field)));
                    pAttr.setIsShow(1);

                    brand = getAttrList(pAttr);// 品牌

                    map.put("brand", brand);

                    // 柜体品牌跟系列没有联系 各独立取值
                    if (mainclass != 15) {
                        if(brand!=null && brand.size()>0) {
                            pAttr.setFatherId(brand.get(0).getId());
                            series = getAttrList(pAttr);// 系列
                        }
                    } else {
                        pAttr.setFatherId(Integer.parseInt(PRODUCTS_FUNCTION.getString(classField + "series")));
                        series = getAttrList(pAttr);// 系列
                    }
                    map.put("series", series);
                } catch (Exception e) {

                }

                // 非accessory-brand-series的项 正常取值
            } else if (!map.containsKey("accessory") && (!"brand".equals(field) || !"series".equals(field))) {
                try {
                    PAttr pAttr = new PAttr();
                    pAttr.setFatherId(Integer.parseInt(PRODUCTS_FUNCTION.getString(classField + field)));
                    pAttr.setIsShow(1);

                    List<PAttr> productParameters = getAttrList(pAttr);
                    map.put(field, productParameters);
                } catch (Exception e) {

                }

                // 有accessory-brand-series的项 得到accessory-brand-series
            } else {

                try {
                    PAttr pAttr = new PAttr();
                    pAttr.setFatherId(Integer.parseInt(PRODUCTS_FUNCTION.getString(classField + field)));

                    accessory = getAttrList(pAttr);// 类别
                    map.put("accessory", accessory);

                    if (accessory.size() > 0) {
                        pAttr.setFatherId(accessory.get(0).getId());
                        brand = getAttrList(pAttr);// 品牌
                        map.put("brand", brand);
                    }
                    if (brand.size() > 0) {
                        pAttr.setFatherId(brand.get(0).getId());
                        pAttr.setIsShow(1);
                        series = getAttrList(pAttr);// 系列
                        map.put("series", series);
                    }
                } catch (Exception e) {

                }
            }
        }



        return map;
    }


    /**
     * 获取分类前缀
     * @param mainclass
     * @return
     */
    private String getClassField(int mainclass) {
        String classField = "";
        switch (mainclass) {
            case 1:
                classField = "ups.";
                break;
            case 2:
                classField = "battery.";
                break;
            case 4:
                classField = "air.";
                break;
            case 5:
                classField = "outdoor.";
                break;
            case 6:
                classField = "indoor.";
                break;
            case 15:
                classField = "box.";
                break;
            case 16:
                classField = "meter.";
                break;
            case 17:
                classField = "light.";
                break;
            case 18:
                classField = "inductor.";
                break;
            case 19:
                classField = "protection.";
                break;
            case 20:
                classField = "capperbar.";
                break;
            case 21:
                classField = "socket.";
                break;
            case 22:
                classField = "distbox.part.";
                break;
            case 23:
                classField = "ats.";
                break;
            case 24:
                classField = "gas.";
                break;
            case 25:
                classField = "pipe.";
                break;
            case 27:
                classField = "refrigeration.";
                break;
            case 30:
                classField = "camera.";
                break;
            case 31:
                classField = "recorder.";
                break;
            case 32:
                classField = "switch.";
                break;
            case 33:
                classField = "transmission.";
                break;
            case 40:
                classField = "eps.";
                break;
            case 50:
                classField = "sts.";
                break;
            case 51:
                classField = "pdu.";
                break;
            case 52:
                classField = "rps.";
                break;
            case 53:
                classField = "inverter.";
                break;
            case 68:
                classField = "batterybox.";
                break;
            case 69:
                classField = "batteryline.";
                break;
            case 70:
                classField = "csc.";
                break;
            case 71:
                classField = "distboxs.";
                break;
            case 72:
                classField = "upss.";
                break;
            case 73:
                classField = "airs.";
                break;
            case 74:
                classField = "lw.";
                break;
            case 75:
                classField = "wsd.";
                break;
            case 76:
                classField = "video.";
                break;
            case 77:
                classField = "acs.";
                break;
            case 78:
                classField = "ff.";
                break;
            case 80:
                classField = "qs.";
                break;
            case 81:
                classField = "ql.";
                break;
            case 82:
                classField = "km.";
                break;
            case 83:
                classField = "fu.";
                break;
            case 84:
                classField = "ka.";
                break;
            case 85:
                classField = "ac.";
                break;
            case 200:
                classField = "ups.part.";
                break;
            case 201:
                classField = "air.part.";
                break;
            case 202:
                classField = "monitor.part.";
                break;
        }

        return classField;
    }

    /**
     * 获取属性
     *
     * @param pAttr
     * @return
     */
    private List<PAttr> getAttrList(PAttr pAttr) {
        QueryWrapper<PAttr> queryWrapper = new QueryWrapper<>();
        queryWrapper.setEntity(pAttr);
        return attrMapper.selectList(queryWrapper);
    }


    /**
     * 获取分类的品牌ID
     * @param mainclass
     * @return
     */
    public int getBrandId(int mainclass) {
        //获取分类前缀
        String classField = getClassField(mainclass);

        int brandId=0;
        try {
            brandId=Integer.parseInt(PRODUCTS_FUNCTION.getString(classField + "brand"));
        } catch (Exception e) {
            //如果报错，说明没有声明这个属性的ID，就是没有这个品牌
        }

        return brandId;
    }


}
