package com.rw.product.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rw.product.model.entity.PEps;
import com.rw.product.model.entity.PPemonitor;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
/**
 * 动环产品 包含 监控中心,配电系统,UPS系统,空调系统,漏水系统,温湿度检测,视频监控,门禁系统,消防系统Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-07-23
 */
@Repository
public interface PPemonitorMapper extends BaseMapper<PPemonitor>
{
    IPage<PPemonitor> page(Page page, @Param("req") PPemonitor pPemonitor);

    PPemonitor getInfoById(@Param("req") PPemonitor pPemonitor);

}
