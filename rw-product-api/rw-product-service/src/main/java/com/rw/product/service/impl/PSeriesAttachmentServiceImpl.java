package com.rw.product.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.img.ImgUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rw.common.core.domain.R;
import com.rw.common.core.utils.PageResultUtils;
import com.rw.common.core.web.controller.BaseController;
import com.rw.common.core.web.domain.AjaxResult;
import com.rw.common.core.web.page.TableDataInfo;
import com.rw.product.mapper.PAttrMapper;
import com.rw.product.mapper.PSeriesAttachmentMapper;
import com.rw.product.model.entity.PAttr;
import com.rw.product.model.entity.PRoom;
import com.rw.product.model.entity.PSeriesAttachment;
import com.rw.product.service.IPSeriesAttachmentService;
import com.rw.product.service.UserService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.awt.image.BufferedImage;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 系列附件Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-08-16
 */
@Slf4j
@RestController
@AllArgsConstructor
public class PSeriesAttachmentServiceImpl extends BaseController implements IPSeriesAttachmentService
{
    @Autowired
    UserService userService;
    @Autowired
    PSeriesAttachmentMapper pSeriesAttachmentMapper;
    @Autowired
    PAttrMapper attrMapper;



    /**
     * 查询列
     * @param language
     * @return
     */
    private String getColumns(String language){
        String columns=" id, title, title_en, attachment, attachment_size, attachment_en, attachment_en_size, main_class, brand, series, del_flag,create_id, create_by, create_time, update_id, update_by, update_time, remark";
        if ("en".equals(language)) {
            columns = "id, title_en as title, attachment_en as attachment, attachment_en_size as , attachment_size, main_class, brand, series, del_flag,create_id, create_by, create_time, update_id, update_by, update_time, remark";
        }
        return columns;
    }


    /**
     * 查询系列附件列表
     */
    @Override
    public TableDataInfo list(PSeriesAttachment pSeriesAttachment)
    {
        startPage();
        QueryWrapper<PSeriesAttachment> queryWrapper = new QueryWrapper<>();
        if (StrUtil.isNotBlank(pSeriesAttachment.getTitle())) {
            queryWrapper.like("title", pSeriesAttachment.getTitle());
        }

        if (pSeriesAttachment.getMainClass()!=null && pSeriesAttachment.getMainClass()>0) {
            queryWrapper.eq("main_class", pSeriesAttachment.getMainClass());
        }
        if (pSeriesAttachment.getBrand()!=null && pSeriesAttachment.getBrand()>0) {
            queryWrapper.eq("brand", pSeriesAttachment.getBrand());
        }
        if (pSeriesAttachment.getSeries()!=null && pSeriesAttachment.getSeries()>0) {
            queryWrapper.eq("series", pSeriesAttachment.getSeries());
        }

        queryWrapper.setEntity(pSeriesAttachment);
        queryWrapper.orderByDesc("id");
        List<PSeriesAttachment> list =pSeriesAttachmentMapper.selectList(queryWrapper);


        fillAttrTitle(list);

        return getDataTable(list);
    }


    /**
     * 组装属性名称
     */
    private void fillAttrTitle(List<PSeriesAttachment> list ){

        if (CollUtil.isEmpty(list)) {
            return;
        }

        List<Integer> attrIdList=new ArrayList<>();
        //取出所有属性ID
        for (PSeriesAttachment item : list) {
            if (item.getBrand()!=null && item.getBrand()>0) {
                attrIdList.add(item.getBrand());
            }
            if (item.getSeries()!=null && item.getSeries()>0) {
                attrIdList.add(item.getSeries());
            }
        }


        if (CollUtil.isEmpty(attrIdList)) {
            return;
        }
        //去重
        attrIdList = attrIdList.stream().distinct().collect(Collectors.toList());

        //根据ID批量获取属性
        List<PAttr> allAttr = attrMapper.selectBatchIds(attrIdList);

        //转换为Map
        Map<Integer, PAttr> attrMap = allAttr.stream().collect(Collectors.toMap(x -> x.getId(), x -> x));

        //循环赋值
        for (PSeriesAttachment item : list) {
            if (item.getBrand()!=null && item.getBrand()>0) {
                if (attrMap.containsKey(item.getBrand())) {
                    item.setBrandTitle(attrMap.get(item.getBrand()).getTitle());
                }
            }
            if (item.getSeries()!=null && item.getSeries()>0) {
                if (attrMap.containsKey(item.getSeries())) {
                    item.setSeriesTitle(attrMap.get(item.getSeries()).getTitle());
                }
            }

        }


    }


    /**
     * 获取系列附件详细信息
     */
    @Override
    public AjaxResult getInfo(@PathVariable("id") Integer id)
    {
        return AjaxResult.success(pSeriesAttachmentMapper.selectById(id));
    }


    /**
     * 新增系列附件
     */
    @Override
    public AjaxResult add(@RequestBody PSeriesAttachment pSeriesAttachment)
    {
        try {
            if (StrUtil.isNotBlank(pSeriesAttachment.getAttachment())) {
                byte[] bytes = HttpUtil.downloadBytes(pSeriesAttachment.getAttachment());
                double sizeInMB=(double)bytes.length / 1024 / 1024;
                pSeriesAttachment.setAttachmentSize(StrUtil.format("{}MB", NumberUtil.roundStr(sizeInMB,2)));
            }

            if (StrUtil.isNotBlank(pSeriesAttachment.getAttachmentEn())) {
                byte[] bytes = HttpUtil.downloadBytes(pSeriesAttachment.getAttachmentEn());
                double sizeInMB=(double)bytes.length / 1024 / 1024;
                pSeriesAttachment.setAttachmentEnSize(StrUtil.format("{}MB", NumberUtil.roundStr(sizeInMB,2)));
            }
        } catch (Exception e) {

        }

        pSeriesAttachment.setCreateId(userService.getUserId());
        pSeriesAttachment.setCreateBy(userService.getNickname());
        return toAjax(pSeriesAttachmentMapper.insert(pSeriesAttachment));
    }

    /**
     * 修改系列附件
     */
    @Override
    public AjaxResult edit(@RequestBody PSeriesAttachment pSeriesAttachment)
    {
        try {
            if (StrUtil.isNotBlank(pSeriesAttachment.getAttachment())) {
                byte[] bytes = HttpUtil.downloadBytes(pSeriesAttachment.getAttachment());
                double sizeInMB=(double)bytes.length / 1024 / 1024;
                pSeriesAttachment.setAttachmentSize(StrUtil.format("{}MB", NumberUtil.roundStr(sizeInMB,2)));
            }

            if (StrUtil.isNotBlank(pSeriesAttachment.getAttachmentEn())) {
                byte[] bytes = HttpUtil.downloadBytes(pSeriesAttachment.getAttachmentEn());
                double sizeInMB=(double)bytes.length / 1024 / 1024;
                pSeriesAttachment.setAttachmentEnSize(StrUtil.format("{}MB", NumberUtil.roundStr(sizeInMB,2)));
            }
        } catch (Exception e) {

        }

        pSeriesAttachment.setUpdateId(userService.getUserId());
        pSeriesAttachment.setUpdateBy(userService.getNickname());
        return toAjax(pSeriesAttachmentMapper.updateById(pSeriesAttachment));
    }

    /**
     * 删除系列附件
     */
    @Override
    public AjaxResult remove(@PathVariable Integer[] ids)
    {
        return toAjax(pSeriesAttachmentMapper.deleteBatchIds(Arrays.asList(ids)));
    }

    @Override
    public PageResultUtils<PSeriesAttachment> page(@RequestBody PSeriesAttachment seriesAttachment) {

        QueryWrapper<PSeriesAttachment> queryWrapper = new QueryWrapper<>();
        queryWrapper.select(getColumns(seriesAttachment.getLanguage()));


        if (seriesAttachment.getMainClass() != null) {
            queryWrapper.eq("main_class", seriesAttachment.getMainClass());
        }

        if (StrUtil.isNotEmpty(seriesAttachment.getTitle())) {
            queryWrapper.and(x->x.like("title", seriesAttachment.getTitle()).or().like("title_en", seriesAttachment.getTitle()));
        }

        if (seriesAttachment.getBrand() != null) {
            queryWrapper.eq("brand", seriesAttachment.getBrand());
        }
        if (seriesAttachment.getSeries() != null) {
            queryWrapper.eq("series", seriesAttachment.getSeries());
        }

        queryWrapper.orderByDesc("update_time");


        Page page = new Page();
        page.setCurrent(seriesAttachment.getPageNum());
        page.setSize(seriesAttachment.getPageSize());

        Page<PSeriesAttachment> pages = pSeriesAttachmentMapper.selectPage(page, queryWrapper);

        return new PageResultUtils<PSeriesAttachment>((int) pages.getTotal(), (int) pages.getSize(), (int) pages.getPages(), (int) pages.getCurrent(), pages.getRecords());
    }

    @Override
    public R getListBySeries(@RequestBody PSeriesAttachment seriesAttachment) {
        QueryWrapper<PSeriesAttachment> queryWrapper = new QueryWrapper<>();
        queryWrapper.select(getColumns(seriesAttachment.getLanguage()));


        if (seriesAttachment.getMainClass() != null) {
            queryWrapper.eq("main_class", seriesAttachment.getMainClass());
        }

        if (StrUtil.isNotEmpty(seriesAttachment.getTitle())) {
            queryWrapper.and(x->x.like("title", seriesAttachment.getTitle()).or().like("title_en", seriesAttachment.getTitle()));
        }

        if (seriesAttachment.getBrand() != null) {
            queryWrapper.eq("brand", seriesAttachment.getBrand());
        }

        if (seriesAttachment.getSeries() != null) {
            queryWrapper.eq("series", seriesAttachment.getSeries());
        }
        queryWrapper.orderByDesc("update_time");
        return R.ok(pSeriesAttachmentMapper.selectList(queryWrapper));
    }
}
