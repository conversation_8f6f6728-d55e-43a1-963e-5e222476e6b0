package com.rw.product.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.rw.common.core.web.controller.BaseController;
import com.rw.common.core.web.domain.AjaxResult;
import com.rw.common.core.web.page.TableDataInfo;
import com.rw.product.mapper.PAttrMapper;
import com.rw.product.mapper.PDetailParametersMapper;
import com.rw.product.mapper.PProductMapper;
import com.rw.product.model.entity.*;
import com.rw.product.service.IPProductService;
import com.rw.product.service.UserService;
import com.rw.product.utils.SyncDataUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 配件和安防信息 包含 断路器,柜体,仪,按钮和指示灯,互感器,浪涌保护,铜排,插座,配件,ATS,摄像机管理,录像机管理,交换机管理,传输类管理,隔离开关,负荷开关,接触器,熔断器,继电器,断路器附件,UPS配件管理,安防配件管理Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-07-23
 */
@Slf4j
@RestController
@AllArgsConstructor
public class PProductServiceImpl extends BaseController implements IPProductService
{
    @Autowired
    UserService userService;
    @Autowired
    PProductMapper pProductMapper;
    @Autowired
    PAttrMapper attrMapper;

    @Autowired
    SyncDataUtil syncDataUtil;


    @Autowired
    PDetailParametersServiceImpl detailParametersService;

    /**
     * 查询配件和安防信息 包含 断路器,柜体,仪,按钮和指示灯,互感器,浪涌保护,铜排,插座,配件,ATS,摄像机管理,录像机管理,交换机管理,传输类管理,隔离开关,负荷开关,接触器,熔断器,继电器,断路器附件,UPS配件管理,安防配件管理列表
     */
    @Override
    public TableDataInfo list(PProduct pProduct)
    {
        startPage();
        QueryWrapper<PProduct> queryWrapper = new QueryWrapper<>();
        queryWrapper.setEntity(pProduct);
        queryWrapper.orderByDesc("id");
        List<PProduct> list =pProductMapper.selectList(queryWrapper);

        fillAttrTitle(list);

        return getDataTable(list);
    }


    /**
     * 组装属性名称
     */
    private void fillAttrTitle(List<PProduct> list ){

        if (CollUtil.isEmpty(list)) {
            return;
        }

        List<Integer> attrIdList=new ArrayList<>();
        //取出所有属性ID
        for (PProduct item : list) {
            if (item.getBrand()!=null && item.getBrand()>0) {
                attrIdList.add(item.getBrand());
            }
            if (item.getSeries()!=null && item.getSeries()>0) {
                attrIdList.add(item.getSeries());
            }
            if (item.getEnviroment()!=null && item.getEnviroment()>0) {
                attrIdList.add(item.getEnviroment());
            }
            if (item.getMethod()!=null && item.getMethod()>0) {
                attrIdList.add(item.getMethod());
            }
            if (item.getThickness()!=null && item.getThickness()>0) {
                attrIdList.add(item.getThickness());
            }
            if (item.getColor()!=null && item.getColor()>0) {
                attrIdList.add(item.getColor());
            }
            if (item.getMaterial()!=null && item.getMaterial()>0) {
                attrIdList.add(item.getMaterial());
            }
            if (item.getStructure()!=null && item.getStructure()>0) {
                attrIdList.add(item.getStructure());
            }
            if (item.getProtection()!=null && item.getProtection()>0) {
                attrIdList.add(item.getProtection());
            }
            if (item.getDoor()!=null && item.getDoor()>0) {
                attrIdList.add(item.getDoor());
            }
            if (item.getChannels()!=null && item.getChannels()>0) {
                attrIdList.add(item.getChannels());
            }
            if (item.getFun()!=null && item.getFun()>0) {
                attrIdList.add(item.getFun());
            }
            if (item.getMonitor()!=null && item.getMonitor()>0) {
                attrIdList.add(item.getMonitor());
            }
            if (item.getVoltageSort()!=null && item.getVoltageSort()>0) {
                attrIdList.add(item.getVoltageSort());
            }
            if (item.getVoltage()!=null && item.getVoltage()>0) {
                attrIdList.add(item.getVoltage());
            }
            if (item.getTurns()!=null && item.getTurns()>0) {
                attrIdList.add(item.getTurns());
            }
            if (item.getCircuit()!=null && item.getCircuit()>0) {
                attrIdList.add(item.getCircuit());
            }
            if (item.getPoles()!=null && item.getPoles()>0) {
                attrIdList.add(item.getPoles());
            }
            if (item.getPart()!=null && item.getPart()>0) {
                attrIdList.add(item.getPart());
            }
            if (item.getKCircuit()!=null && item.getKCircuit()>0) {
                attrIdList.add(item.getKCircuit());
            }
            if (item.getCapacity()!=null && item.getCapacity()>0) {
                attrIdList.add(item.getCapacity());
            }
            if (item.getIcs()!=null && item.getIcs()>0) {
                attrIdList.add(item.getIcs());
            }
            if (item.getIcu()!=null && item.getIcu()>0) {
                attrIdList.add(item.getIcu());
            }
            if (item.getAirSwitchType()!=null && item.getAirSwitchType()>0) {
                attrIdList.add(item.getAirSwitchType());
            }
            if (item.getAccessory()!=null && item.getAccessory()>0) {
                attrIdList.add(item.getAccessory());
            }
            if (item.getScale()!=null && item.getScale()>0) {
                attrIdList.add(item.getScale());
            }
        }



        if (CollUtil.isEmpty(attrIdList)) {
            return;
        }
        //去重
        attrIdList = attrIdList.stream().distinct().collect(Collectors.toList());

        //根据ID批量获取属性
        List<PAttr> allAttr = attrMapper.selectBatchIds(attrIdList);

        //转换为Map
        Map<Integer, PAttr> attrMap = allAttr.stream().collect(Collectors.toMap(x -> x.getId(), x -> x));

        //循环赋值
        for (PProduct item : list) {
            if (item.getBrand()!=null && item.getBrand()>0) {
                if (attrMap.containsKey(item.getBrand())) {
                    item.setBrandTitle(attrMap.get(item.getBrand()).getTitle());
                }
            }
            if (item.getSeries()!=null && item.getSeries()>0) {
                if (attrMap.containsKey(item.getSeries())) {
                    item.setSeriesTitle(attrMap.get(item.getSeries()).getTitle());
                }
            }


            if (item.getEnviroment()!=null && item.getEnviroment()>0) {
                if (attrMap.containsKey(item.getEnviroment())) {
                    item.setEnviromentTitle(attrMap.get(item.getEnviroment()).getTitle());
                }
            }
            if (item.getMethod()!=null && item.getMethod()>0) {
                if (attrMap.containsKey(item.getMethod())) {
                    item.setMethodTitle(attrMap.get(item.getMethod()).getTitle());
                }
            }
            if (item.getThickness()!=null && item.getThickness()>0) {
                if (attrMap.containsKey(item.getThickness())) {
                    item.setThicknessTitle(attrMap.get(item.getThickness()).getTitle());
                }
            }
            if (item.getColor()!=null && item.getColor()>0) {
                if (attrMap.containsKey(item.getColor())) {
                    item.setColorTitle(attrMap.get(item.getColor()).getTitle());
                }
            }
            if (item.getMaterial()!=null && item.getMaterial()>0) {
                if (attrMap.containsKey(item.getMaterial())) {
                    item.setMaterialTitle(attrMap.get(item.getMaterial()).getTitle());
                }
            }
            if (item.getStructure()!=null && item.getStructure()>0) {
                if (attrMap.containsKey(item.getStructure())) {
                    item.setStructureTitle(attrMap.get(item.getStructure()).getTitle());
                }
            }
            if (item.getProtection()!=null && item.getProtection()>0) {
                if (attrMap.containsKey(item.getProtection())) {
                    item.setProtectionTitle(attrMap.get(item.getProtection()).getTitle());
                }
            }
            if (item.getDoor()!=null && item.getDoor()>0) {
                if (attrMap.containsKey(item.getDoor())) {
                    item.setDoorTitle(attrMap.get(item.getDoor()).getTitle());
                }
            }
            if (item.getChannels()!=null && item.getChannels()>0) {
                if (attrMap.containsKey(item.getChannels())) {
                    item.setChannelsTitle(attrMap.get(item.getChannels()).getTitle());
                }
            }
            if (item.getFun()!=null && item.getFun()>0) {
                if (attrMap.containsKey(item.getFun())) {
                    item.setFunTitle(attrMap.get(item.getFun()).getTitle());
                }
            }
            if (item.getMonitor()!=null && item.getMonitor()>0) {
                if (attrMap.containsKey(item.getMonitor())) {
                    item.setMonitorTitle(attrMap.get(item.getMonitor()).getTitle());
                }
            }
            if (item.getVoltageSort()!=null && item.getVoltageSort()>0) {
                if (attrMap.containsKey(item.getVoltageSort())) {
                    item.setVoltageSortTitle(attrMap.get(item.getVoltageSort()).getTitle());
                }
            }
            if (item.getVoltage()!=null && item.getVoltage()>0) {
                if (attrMap.containsKey(item.getVoltage())) {
                    item.setVoltageTitle(attrMap.get(item.getVoltage()).getTitle());
                }
            }
            if (item.getTurns()!=null && item.getTurns()>0) {
                if (attrMap.containsKey(item.getTurns())) {
                    item.setTurnsTitle(attrMap.get(item.getTurns()).getTitle());
                }
            }
            if (item.getCircuit()!=null && item.getCircuit()>0) {
                if (attrMap.containsKey(item.getCircuit())) {
                    item.setCircuitTitle(attrMap.get(item.getCircuit()).getTitle());
                }
            }
            if (item.getPoles()!=null && item.getPoles()>0) {
                if (attrMap.containsKey(item.getPoles())) {
                    item.setPolesTitle(attrMap.get(item.getPoles()).getTitle());
                }
            }
            if (item.getPart()!=null && item.getPart()>0) {
                if (attrMap.containsKey(item.getPart())) {
                    item.setPartTitle(attrMap.get(item.getPart()).getTitle());
                }
            }
            if (item.getKCircuit()!=null && item.getKCircuit()>0) {
                if (attrMap.containsKey(item.getKCircuit())) {
                    item.setKCircuitTitle(attrMap.get(item.getKCircuit()).getTitle());
                }
            }
            if (item.getCapacity()!=null && item.getCapacity()>0) {
                if (attrMap.containsKey(item.getCapacity())) {
                    item.setCapacityTitle(attrMap.get(item.getCapacity()).getTitle());
                }
            }
            if (item.getIcs()!=null && item.getIcs()>0) {
                if (attrMap.containsKey(item.getIcs())) {
                    item.setIcsTitle(attrMap.get(item.getIcs()).getTitle());
                }
            }
            if (item.getIcu()!=null && item.getIcu()>0) {
                if (attrMap.containsKey(item.getIcu())) {
                    item.setIcuTitle(attrMap.get(item.getIcu()).getTitle());
                }
            }
            if (item.getAirSwitchType()!=null && item.getAirSwitchType()>0) {
                if (attrMap.containsKey(item.getAirSwitchType())) {
                    item.setAirSwitchTypeTitle(attrMap.get(item.getAirSwitchType()).getTitle());
                }
            }
            if (item.getAccessory()!=null && item.getAccessory()>0) {
                if (attrMap.containsKey(item.getAccessory())) {
                    item.setAccessoryTitle(attrMap.get(item.getAccessory()).getTitle());
                }
            }
            if (item.getScale()!=null && item.getScale()>0) {
                if (attrMap.containsKey(item.getScale())) {
                    item.setScaleTitle(attrMap.get(item.getScale()).getTitle());
                }
            }
        }




    }



    /**
     * 获取配件和安防信息 包含 断路器,柜体,仪,按钮和指示灯,互感器,浪涌保护,铜排,插座,配件,ATS,摄像机管理,录像机管理,交换机管理,传输类管理,隔离开关,负荷开关,接触器,熔断器,继电器,断路器附件,UPS配件管理,安防配件管理详细信息
     */
    @Override
    public AjaxResult getInfo(@PathVariable("id") Integer id)
    {
        PProduct data = pProductMapper.selectById(id);
        if (data!=null) {
            PDetailParameters parameters = new PDetailParameters();
            parameters.setProductId(data.getId());
            parameters.setMainClass(data.getMainClass());
            data.setDetailParameters(detailParametersService.getOneByProductIdAndMainClass(parameters));
        }
        return AjaxResult.success(data);
    }

    /**
     * 新增配件和安防信息 包含 断路器,柜体,仪,按钮和指示灯,互感器,浪涌保护,铜排,插座,配件,ATS,摄像机管理,录像机管理,交换机管理,传输类管理,隔离开关,负荷开关,接触器,熔断器,继电器,断路器附件,UPS配件管理,安防配件管理
     */
    @Override
    public AjaxResult add(@RequestBody PProduct pProduct)
    {
        pProduct.setCreateId(userService.getUserId());
        pProduct.setCreateBy(userService.getNickname());


        //同步数据
        pProduct.setId(Convert.toInt(syncDataUtil.post(SyncDataUtil.product_add, pProduct)));


        int result = pProductMapper.insert(pProduct);
        if (result>0) {
            if(pProduct.getDetailParameters()==null){
                pProduct.setDetailParameters(new PDetailParameters());
            }

            pProduct.getDetailParameters().setProductId(pProduct.getId());
            pProduct.getDetailParameters().setMainClass(pProduct.getMainClass());

            detailParametersService.addDetailParameters(pProduct.getDetailParameters());
        }

        return toAjax(result);
    }

    /**
     * 修改配件和安防信息 包含 断路器,柜体,仪,按钮和指示灯,互感器,浪涌保护,铜排,插座,配件,ATS,摄像机管理,录像机管理,交换机管理,传输类管理,隔离开关,负荷开关,接触器,熔断器,继电器,断路器附件,UPS配件管理,安防配件管理
     */
    @Override
    public AjaxResult edit(@RequestBody PProduct pProduct)
    {
        pProduct.setUpdateId(userService.getUserId());
        pProduct.setUpdateBy(userService.getNickname());


        //同步数据
        syncDataUtil.post(SyncDataUtil.product_update, pProduct);

        int result = pProductMapper.updateById(pProduct);
        if (result>0) {
            pProduct.getDetailParameters().setProductId(pProduct.getId());
            pProduct.getDetailParameters().setMainClass(pProduct.getMainClass());


            detailParametersService.addDetailParameters(pProduct.getDetailParameters());
        }

        return toAjax(result);
    }

    /**
     * 删除配件和安防信息 包含 断路器,柜体,仪,按钮和指示灯,互感器,浪涌保护,铜排,插座,配件,ATS,摄像机管理,录像机管理,交换机管理,传输类管理,隔离开关,负荷开关,接触器,熔断器,继电器,断路器附件,UPS配件管理,安防配件管理
     */
    @Override
    public AjaxResult remove(@PathVariable Integer[] ids)
    {
        return toAjax(pProductMapper.deleteBatchIds(Arrays.asList(ids)));
    }

    @Override
    public AjaxResult updateIsShow(@RequestBody PProduct pProduct) {
        PProduct syncModel = pProductMapper.selectById(pProduct.getId());
        syncModel.setUpdateId(userService.getUserId());
        syncModel.setUpdateBy(userService.getNickname());
        syncModel.setIsShow(pProduct.getIsShow());
        //同步数据
        syncDataUtil.post(SyncDataUtil.ups_update, syncModel);


        QueryWrapper<PProduct> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("id", syncModel.getId());

        PProduct model = new PProduct();
        model.setIsShow(syncModel.getIsShow());
        model.setUpdateId(syncModel.getUpdateId());
        model.setUpdateBy(syncModel.getUpdateBy());

        return toAjax( pProductMapper.update(model,queryWrapper));
    }
}
