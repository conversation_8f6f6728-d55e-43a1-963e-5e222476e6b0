package com.rw.product.utils;

import cn.hutool.core.util.CharsetUtil;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.util.Arrays;
import java.util.Base64;

/**
 * AES加解密
 * 窦海超  2016年5月25日 17:54:03删除
 * <AUTHOR>
 */
public class AES {
	private final static String AES_KEY = "k;)*(+nmjdsf$#@d";
	private final static String UTF_8 = "UTF-8";
	/**
	 * AES加密
	 * 
	 * @param str
	 *            待加密字符串
	 * @return 加密后字符串
	 */
	public static String aesEncrypt(String str) {
		return aesEncrypt(AES_KEY,str);
	}
	/**
	 * AES解密
	 * 
	 * @param str
	 *            待解密字符串
	 * @return 解密后字符串
	 */
	public static String aesDecrypt(String str) {
		return aesDecrypt(AES_KEY,str);
	}
	/**
	 * AES加密
	 * 
	 * @param str
	 *            待加密字符串
	 * @return 加密后字符串
	 */
	public static String aesEncrypt(String aes_KEY,String str) {
		try {
			if (str==null||str.isEmpty()) {
				return "";
			}
			str=new String(str.getBytes(UTF_8), UTF_8);
			String password = aes_KEY;
			SecretKeySpec skeySpec = new SecretKeySpec(password.getBytes(), "AES");
			Cipher cipher = Cipher.getInstance("AES/ECB/PKCS5Padding");
			cipher.init(Cipher.ENCRYPT_MODE, skeySpec);
			byte[] bs=cipher.doFinal(str.getBytes("UTF-8"));
			byte[] bs64= Base64.getEncoder().encode(bs);
			String strTmp = new String(bs64);
			return strTmp;

		} catch (Exception e) {
			throw new RuntimeException("AES加密时异常"+e.getMessage());
		}
	}

	/**
	 * AES解密
	 * 
	 * @param str
	 *            待解密字符串
	 * @return 解密后字符串
	 */
	public static String aesDecrypt(String aes_KEY,String str) {
		try {
			if (str==null||str.isEmpty()) {
				return "";
			}
			String password = aes_KEY;
			SecretKeySpec skeySpec = new SecretKeySpec(password.getBytes(), "AES");
			Cipher cipher = Cipher.getInstance("AES/ECB/PKCS5Padding");
			cipher.init(Cipher.DECRYPT_MODE, skeySpec);
			byte [] bs= Base64.getDecoder().decode(str.replace(" ","+"));
			String strTmp = new String(cipher.doFinal(bs),"UTF-8");
			return strTmp;

		} catch (Exception ex) {
			throw new RuntimeException("AES解密时异常"+ex.getMessage());
		}
	}
	/**
	 * AES解密
	 *
	 * @param str
	 *            待解密字符串
	 * @return 解密后字符串
	 */
	public static String aesNOPaddingDecrypt(String secretKey,String vector,String str) {
		try {
			if (str==null||str.isEmpty()) {
				return "";
			}
			SecretKeySpec skeySpec = new SecretKeySpec(secretKey.getBytes(), "AES");
			IvParameterSpec ivspec = new IvParameterSpec(vector.getBytes());
			Cipher cipher = Cipher.getInstance("AES/CBC/NOPadding");
			cipher.init(Cipher.DECRYPT_MODE, skeySpec,ivspec);
			byte[] result = cipher.doFinal(cn.hutool.core.codec.Base64.decode(str.getBytes(CharsetUtil.UTF_8)));
			return new String(result, CharsetUtil.UTF_8).trim();

		} catch (Exception ex) {
			throw new RuntimeException("AES解密时异常"+ex.getMessage());
		}
	}

 public static void main(String[] args) {
	 String data = "外卖场景,知识问答场景";
	 System.out.println(Arrays.asList(data.split(",")));

	/* String data = "请问你想吃啥什么";
	 String regex = "([\\u4e00-\\u9fa5]+:[\\u4e00-\\u9fa5|\\w]+);"; // 匹配中文字符和分号

	 // 使用Hutool的ReUtil工具进行匹配
	 List<String> all = ReUtil.findAll(regex, data, 0);

	 // 输出匹配到的结果
	 for (String match : all) {
		 System.out.println(match.substring(0, match.length() - 1)); // 去除分号
	 }
*/
	 //生成公私钥证书
	/* RSA rsa = SecureUtil.rsa();


	 String publicKey = rsa.getPublicKeyBase64();
	 System.out.println("公钥："+publicKey);
	 String privateKey = rsa.getPrivateKeyBase64();
	 System.out.println("私钥："+privateKey);
	 String s = rsa.encryptHex("早班不不不", KeyType.PublicKey);
	 System.out.println("加密结果：" + s);
	 String s1 = rsa.decryptStr(s, KeyType.PrivateKey);
	 System.out.println("解密结果：" + s1);*/

	/* String str = "输出为:#1空null#2汉堡#3保利东郡#2可乐";
	 String pattern = "#(\\d+)([^#]*)";

	 // 提取匹配内容
	 List<String> allMatches = ReUtil.findAll(pattern, str, 0);
	 for (String s : allMatches) {
		 System.out.println(s);
	 }*/

	 /*String str = "八里庄北里129号院-9号楼 (c座1402)";
	 String s = str.split("\\(")[0];
	 System.out.println(s);*/

	/* String encryData = "xWmiIKwNsvsop/S0499iLEPzKhU4HBtc6krQ16jjkQlqQ7TwNVfzUYHTS9L7firJNUgU1qCpC2vik+6CCwzkYH+aCMusU9UQT8zsdL1Lbiez7gQnni0ANNd2Jai6suhsjxOr+7dmMAWePboJxVdSLg==";

	 String s = aesDecrypt("", encryData);

	 System.out.println(s);

	 JSONObject obj = JSONUtil.createObj();
	 obj.putOpt("token", "AgHsIxyF3ZNqkGB76EkRTDWY51B0OGRAFqnNoPRAE7oEr1SYmvmXumGIzakLeJ7J1RdZyfVy--ag0wAAAADKHgAApapHH9YbJe_DVtMA20uM-FKXUgBJhA7sFF_ECXqtkJYfjkUMw-BtZ9RB49dcolol");
	 obj.putOpt("mtCToken", "AgHsIxyF3ZNqkGB76EkRTDWY51B0OGRAFqnNoPRAE7oEr1SYmvmXumGIzakLeJ7J1RdZyfVy--ag0wAAAADKHgAApapHH9YbJe_DVtMA20uM-FKXUgBJhA7sFF_ECXqtkJYfjkUMw-BtZ9RB49dcolol");
	 String s1 = aesEncrypt("", obj.toString());

	 System.out.println(s1);*/
/*	 JSONObject obj = JSONUtil.createObj();
	 obj.putOpt("message", "帮我点一份南城乡的肥牛饭");
	 obj.putOpt("token", "AgEeJWQd8s0tun1GrOGbMHw7ON1uvk1ixfvjQqRAdQY5rXpUD10ROY5-ts0wbYzPCRWRQxwexutZTwAAAADKHgAAz3jVyLRTjP99l5T9Rq_OoxNurYlimLp9sLx3dus7YaF1YKCnv92x2jLufJMRJG7g");
	 obj.putOpt("mtCToken", "AgEeJWQd8s0tun1GrOGbMHw7ON1uvk1ixfvjQqRAdQY5rXpUD10ROY5-ts0wbYzPCRWRQxwexutZTwAAAADKHgAAz3jVyLRTjP99l5T9Rq_OoxNurYlimLp9sLx3dus7YaF1YKCnv92x2jLufJMRJG7g");
	 obj.putOpt("longitude", "116.501523");
	 obj.putOpt("latitude", "39.928803");
	 obj.putOpt("password", "");
	 obj.putOpt("contextMessage", "");
	 obj.putOpt("type", "1");
	 obj.putOpt("qId", "0");

	 String s1 = aesEncrypt("0;&*(+nl0d%f$#@d", obj.toString());

	 System.out.println(s1);*/
/*	 String data = "";
	 System.out.println(aesDecrypt("0;&*(+nl0d%f$#@d", data));*/

	 //app传参加解密
/*	 JSONObject appObj = JSONUtil.createObj();
	 appObj.putOpt("token", "AgEeJWQd8s0tun1GrOGbMHw7ON1uvk1ixfvjQqRAdQY5rXpUD10ROY5-ts0wbYzPCRWRQxwexutZTwAAAADKHgAAz3jVyLRTjP99l5T9Rq_OoxNurYlimLp9sLx3dus7YaF1YKCnv92x2jLufJMRJG7g");
	 appObj.putOpt("mtCToken", "AgEeJWQd8s0tun1GrOGbMHw7ON1uvk1ixfvjQqRAdQY5rXpUD10ROY5-ts0wbYzPCRWRQxwexutZTwAAAADKHgAAz3jVyLRTjP99l5T9Rq_OoxNurYlimLp9sLx3dus7YaF1YKCnv92x2jLufJMRJG7g");
	 appObj.putOpt("passWord", "246810");
	 appObj.putOpt("longitude", "116.501523");
	 appObj.putOpt("latitude", "39.928803");
	 String s1 = aesEncrypt("0;&*(+nl0d%f$#@d", appObj.toString());
	 System.out.println("加密值:"+s1);*/



 }
}