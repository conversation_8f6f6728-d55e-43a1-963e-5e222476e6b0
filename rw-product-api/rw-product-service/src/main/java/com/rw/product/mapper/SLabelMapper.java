package com.rw.product.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.rw.product.model.entity.PAir;
import com.rw.product.model.entity.SLabel;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 方案标签Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-07-23
 */
@Repository
public interface SLabelMapper extends BaseMapper<SLabel>
{
    List<String> getClasssifyIdByLabelClass(@Param("labelClass") Integer labelClass);
}
