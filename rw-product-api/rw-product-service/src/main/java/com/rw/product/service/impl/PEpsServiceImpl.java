package com.rw.product.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rw.common.core.domain.R;
import com.rw.common.core.utils.PageResultUtils;
import com.rw.common.core.web.controller.BaseController;
import com.rw.common.core.web.domain.AjaxResult;
import com.rw.common.core.web.page.TableDataInfo;
import com.rw.product.mapper.PAttrMapper;
import com.rw.product.mapper.PDetailParametersMapper;
import com.rw.product.mapper.PEpsMapper;
import com.rw.product.model.entity.*;
import com.rw.product.service.IPEpsService;
import com.rw.product.service.UserService;
import com.rw.product.utils.SyncDataUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * EPS 包含 EPS管理Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-07-23
 */
@Slf4j
@RestController
@AllArgsConstructor
public class PEpsServiceImpl extends BaseController implements IPEpsService
{
    @Autowired
    UserService userService;
    @Autowired
    PEpsMapper pEpsMapper;
    @Autowired
    PAttrMapper attrMapper;


    @Autowired
    SyncDataUtil syncDataUtil;


    @Autowired
    PDetailParametersServiceImpl detailParametersService;

    /**
     * 查询EPS 包含 EPS管理列表
     */
    @Override
    public TableDataInfo list(PEps pEps)
    {
        startPage();
        QueryWrapper<PEps> queryWrapper = new QueryWrapper<>();
        queryWrapper.setEntity(pEps);
        queryWrapper.orderByDesc("id");
        List<PEps> list =pEpsMapper.selectList(queryWrapper);


        fillAttrTitle(list);

        return getDataTable(list);
    }



    /**
     * 组装属性名称
     */
    private void fillAttrTitle(List<PEps> list ){

        if (CollUtil.isEmpty(list)) {
            return;
        }

        List<Integer> attrIdList=new ArrayList<>();
        //取出所有属性ID
        for (PEps item : list) {
            if (item.getBrand()!=null && item.getBrand()>0) {
                attrIdList.add(item.getBrand());
            }
            if (item.getSeries()!=null && item.getSeries()>0) {
                attrIdList.add(item.getSeries());
            }
            if (item.getCapacity()!=null && item.getCapacity()>0) {
                attrIdList.add(item.getCapacity());
            }
            if (item.getClassify()!=null && item.getClassify()>0) {
                attrIdList.add(item.getClassify());
            }
            if (item.getVoltageSort()!=null && item.getVoltageSort()>0) {
                attrIdList.add(item.getVoltageSort());
            }
        }


        if (CollUtil.isEmpty(attrIdList)) {
            return;
        }
        //去重
        attrIdList = attrIdList.stream().distinct().collect(Collectors.toList());

        //根据ID批量获取属性
        List<PAttr> allAttr = attrMapper.selectBatchIds(attrIdList);

        //转换为Map
        Map<Integer, PAttr> attrMap = allAttr.stream().collect(Collectors.toMap(x -> x.getId(), x -> x));

        //循环赋值
        for (PEps item : list) {
            if (item.getBrand()!=null && item.getBrand()>0) {
                if (attrMap.containsKey(item.getBrand())) {
                    item.setBrandTitle(attrMap.get(item.getBrand()).getTitle());
                }
            }
            if (item.getSeries()!=null && item.getSeries()>0) {
                if (attrMap.containsKey(item.getSeries())) {
                    item.setSeriesTitle(attrMap.get(item.getSeries()).getTitle());
                }
            }

            if (item.getCapacity()!=null && item.getCapacity()>0) {
                if (attrMap.containsKey(item.getCapacity())) {
                    item.setCapacityTitle(attrMap.get(item.getCapacity()).getTitle());
                }
            }

            if (item.getClassify()!=null && item.getClassify()>0) {
                if (attrMap.containsKey(item.getClassify())) {
                    item.setClassifyTitle(attrMap.get(item.getClassify()).getTitle());
                }
            }

            if (item.getVoltageSort()!=null && item.getVoltageSort()>0) {
                if (attrMap.containsKey(item.getVoltageSort())) {
                    item.setVoltageSortTitle(attrMap.get(item.getVoltageSort()).getTitle());
                }
            }

        }


    }


    /**
     * 获取EPS 包含 EPS管理详细信息
     */
    @Override
    public AjaxResult getInfo(@PathVariable("id") Integer id)
    {

        PEps data = pEpsMapper.selectById(id);
        if (data!=null) {
            PDetailParameters parameters = new PDetailParameters();
            parameters.setProductId(data.getId());
            parameters.setMainClass(data.getMainClass());
            data.setDetailParameters(detailParametersService.getOneByProductIdAndMainClass(parameters));
        }

        return AjaxResult.success(data);
    }

    /**
     * 新增EPS 包含 EPS管理
     */
    @Override
    public AjaxResult add(@RequestBody PEps pEps)
    {
        pEps.setCreateId(userService.getUserId());
        pEps.setCreateBy(userService.getNickname());


        //同步数据
        pEps.setId(Convert.toInt(syncDataUtil.post(SyncDataUtil.eps_add, pEps)));


        int result = pEpsMapper.insert(pEps);
        if (result>0) {
            if(pEps.getDetailParameters()==null){
                pEps.setDetailParameters(new PDetailParameters());
            }

            pEps.getDetailParameters().setProductId(pEps.getId());
            pEps.getDetailParameters().setMainClass(pEps.getMainClass());
            detailParametersService.addDetailParameters(pEps.getDetailParameters());
        }

        return toAjax(result);
    }

    /**
     * 修改EPS 包含 EPS管理
     */
    @Override
    public AjaxResult edit(@RequestBody PEps pEps)
    {
        pEps.setUpdateId(userService.getUserId());
        pEps.setUpdateBy(userService.getNickname());


        //同步数据
        syncDataUtil.post(SyncDataUtil.eps_update, pEps);


        int result = pEpsMapper.updateById(pEps);
        if (result>0) {
            pEps.getDetailParameters().setProductId(pEps.getId());
            pEps.getDetailParameters().setMainClass(pEps.getMainClass());

            detailParametersService.addDetailParameters(pEps.getDetailParameters());
        }

        return toAjax(result);
    }

    /**
     * 删除EPS 包含 EPS管理
     */
    @Override
    public AjaxResult remove(@PathVariable Integer[] ids)
    {
        return toAjax(pEpsMapper.deleteBatchIds(Arrays.asList(ids)));
    }

    @Override
    public AjaxResult updateIsShow(@RequestBody PEps pEps) {
        PEps syncModel = pEpsMapper.selectById(pEps.getId());
        syncModel.setUpdateId(userService.getUserId());
        syncModel.setUpdateBy(userService.getNickname());
        syncModel.setIsShow(pEps.getIsShow());
        //同步数据
        syncDataUtil.post(SyncDataUtil.eps_update, syncModel);


        QueryWrapper<PEps> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("id", syncModel.getId());

        PEps model = new PEps();
        model.setIsShow(syncModel.getIsShow());
        model.setUpdateId(syncModel.getUpdateId());
        model.setUpdateBy(syncModel.getUpdateBy());

        return toAjax( pEpsMapper.update(model,queryWrapper));
    }


    @Override
    public PageResultUtils<PEps> page(@RequestBody PEps pEps) {
        Page page = new Page();
        page.setCurrent(pEps.getPageNum());
        page.setSize(pEps.getPageSize());
        IPage<PEps> newsPage = pEpsMapper.page(page, pEps);
        return new PageResultUtils<PEps>((int) newsPage.getTotal(), (int) newsPage.getSize(), (int) newsPage.getPages(), (int) newsPage.getCurrent(), newsPage.getRecords());
    }

    @Override
    public R getInfoById(@RequestBody PEps pEps) {

        PEps data = pEpsMapper.getInfoById(pEps);
        if (data!=null) {
            PDetailParameters parameters = new PDetailParameters();
            parameters.setProductId(data.getId());
            parameters.setMainClass(data.getMainClass());
            parameters.setLanguage(pEps.getLanguage());

            data.setDetailParameters(detailParametersService.getOneByProductIdAndMainClass(parameters));
        }

        return R.ok(data);
    }
}
