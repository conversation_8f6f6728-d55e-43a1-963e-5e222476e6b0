package com.rw.product.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.rw.common.core.domain.R;
import com.rw.common.core.web.controller.BaseController;
import com.rw.product.mapper.*;
import com.rw.product.model.entity.*;
import com.rw.product.service.ISyncDataService;
import com.rw.product.utils.SyncDataUtil;
import com.rw.product.utils.uploadFile.FileUploadUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 同步数据
 */
@Slf4j
@RestController
@AllArgsConstructor
public class SyncDataServiceImpl extends BaseController implements ISyncDataService {

    @Autowired
    private PAirMapper airMapper;
    @Autowired
    private PAttrMapper attrMapper;
    @Autowired
    private PEpsMapper epsMapper;
    @Autowired
    private PPemonitorMapper pemonitorMapper;
    @Autowired
    private PProductMapper productMapper;
    @Autowired
    private PRoomMapper roomMapper;
    @Autowired
    private PSingleMapper singleMapper;
    @Autowired
    private PUpsMapper upsMapper;

    @Autowired
    private SLabelMapper labelMapper;
    @Autowired
    private SBoxlistMapper boxlistMapper;
    @Autowired
    private SBoxlistDetailMapper boxlistDetailMapper;
    @Autowired
    private SProlistMapper prolistMapper;
    @Autowired
    private SProlistDetailsMapper prolistDetailsMapper;

    @Autowired
    SyncDataUtil syncDataUtil;

    @Override
    public R t() {
        return R.ok();
    }

    @Override
    public R test(@RequestBody PAir air) {
        air.setId(200);
        return R.ok(air);
    }

    @Override
    public R addAir(@RequestBody PAir air) {
        air.setImg(FileUploadUtil.uploadFile2AliOssSplit(air.getImg()));
        air.setImgs(FileUploadUtil.uploadFile2AliOssSplit(air.getImgs()));
        air.setImgsEn(FileUploadUtil.uploadFile2AliOssSplit(air.getImgsEn()));


        //如果存在就更新
        if (air.getId()!=null&&air.getId()>0) {
            if (airMapper.selectById(air.getId())!=null) {
                return updateAir(air);
            }
        }


        return R.ok(airMapper.insert(air));
    }

    @Override
    public R updateAir(@RequestBody PAir air) {
        if (air.getId() == null || air.getId() <= 0) {
            return R.fail("请填写主键ID");
        }
        air.setImg(FileUploadUtil.uploadFile2AliOssSplit(air.getImg()));
        air.setImgs(FileUploadUtil.uploadFile2AliOssSplit(air.getImgs()));
        air.setImgsEn(FileUploadUtil.uploadFile2AliOssSplit(air.getImgsEn()));
        return R.ok(airMapper.updateById(air));
    }

    @Override
    public R addAttr(@RequestBody PAttr attr) {


        //如果存在就更新
        if (attr.getId()!=null&&attr.getId()>0) {
            if (attrMapper.selectById(attr.getId())!=null) {
                return updateAttr(attr);
            }
        }

        return R.ok(attrMapper.insert(attr));
    }

    @Override
    public R updateAttr(@RequestBody PAttr attr) {
        if (attr.getId() == null || attr.getId() <= 0) {
            return R.fail("请填写主键ID");
        }
        return R.ok(attrMapper.updateById(attr));
    }

    @Override
    public R addEps(@RequestBody PEps eps) {

        eps.setImg(FileUploadUtil.uploadFile2AliOssSplit(eps.getImg()));
        eps.setImgs(FileUploadUtil.uploadFile2AliOssSplit(eps.getImgs()));
        eps.setImgsEn(FileUploadUtil.uploadFile2AliOssSplit(eps.getImgsEn()));



        //如果存在就更新
        if (eps.getId()!=null&&eps.getId()>0) {
            if (epsMapper.selectById(eps.getId())!=null) {
                return updateEps(eps);
            }
        }



        return R.ok(epsMapper.insert(eps));
    }

    @Override
    public R updateEps(PEps eps) {
        if (eps.getId() == null || eps.getId() <= 0) {
            return R.fail("请填写主键ID");
        }

        eps.setImg(FileUploadUtil.uploadFile2AliOssSplit(eps.getImg()));
        eps.setImgs(FileUploadUtil.uploadFile2AliOssSplit(eps.getImgs()));
        eps.setImgsEn(FileUploadUtil.uploadFile2AliOssSplit(eps.getImgsEn()));
        return R.ok(epsMapper.updateById(eps));
    }

    @Override
    public R addPemonitor(@RequestBody PPemonitor pemonitor) {

        pemonitor.setImg(FileUploadUtil.uploadFile2AliOssSplit(pemonitor.getImg()));
        pemonitor.setImgs(FileUploadUtil.uploadFile2AliOssSplit(pemonitor.getImgs()));
        pemonitor.setImgsEn(FileUploadUtil.uploadFile2AliOssSplit(pemonitor.getImgsEn()));

        //如果存在就更新
        if (pemonitor.getId()!=null&&pemonitor.getId()>0) {
            if (pemonitorMapper.selectById(pemonitor.getId())!=null) {
                return updatePemonitor(pemonitor);
            }
        }


        return R.ok(pemonitorMapper.insert(pemonitor));
    }

    @Override
    public R updatePemonitor(@RequestBody PPemonitor pemonitor) {
        if (pemonitor.getId() == null || pemonitor.getId() <= 0) {
            return R.fail("请填写主键ID");
        }

        pemonitor.setImg(FileUploadUtil.uploadFile2AliOssSplit(pemonitor.getImg()));
        pemonitor.setImgs(FileUploadUtil.uploadFile2AliOssSplit(pemonitor.getImgs()));
        pemonitor.setImgsEn(FileUploadUtil.uploadFile2AliOssSplit(pemonitor.getImgsEn()));
        return R.ok(pemonitorMapper.updateById(pemonitor));
    }

    @Override
    public R addUps(@RequestBody PUps ups) {


        ups.setImg(FileUploadUtil.uploadFile2AliOssSplit(ups.getImg()));
        ups.setImgs(FileUploadUtil.uploadFile2AliOssSplit(ups.getImgs()));
        ups.setImgsEn(FileUploadUtil.uploadFile2AliOssSplit(ups.getImgsEn()));

        //如果存在就更新
        if (ups.getId()!=null&&ups.getId()>0) {
            if (upsMapper.selectById(ups.getId())!=null) {
                return updateUps(ups);
            }
        }


        return R.ok(upsMapper.insert(ups));
    }

    @Override
    public R updateUps(@RequestBody PUps ups) {
        if (ups.getId() == null || ups.getId() <= 0) {
            return R.fail("请填写主键ID");
        }
        ups.setImg(FileUploadUtil.uploadFile2AliOssSplit(ups.getImg()));
        ups.setImgs(FileUploadUtil.uploadFile2AliOssSplit(ups.getImgs()));
        ups.setImgsEn(FileUploadUtil.uploadFile2AliOssSplit(ups.getImgsEn()));
        return R.ok(upsMapper.updateById(ups));
    }

    @Override
    public R addProduct(@RequestBody PProduct product) {

        product.setImg(FileUploadUtil.uploadFile2AliOssSplit(product.getImg()));
        product.setImgs(FileUploadUtil.uploadFile2AliOssSplit(product.getImgs()));
        product.setImgsEn(FileUploadUtil.uploadFile2AliOssSplit(product.getImgsEn()));


        //如果存在就更新
        if (product.getId()!=null&&product.getId()>0) {
            if (productMapper.selectById(product.getId())!=null) {
                return updateProduct(product);
            }
        }


        return R.ok(productMapper.insert(product));
    }

    @Override
    public R updateProduct(@RequestBody PProduct product) {

        if (product.getId() == null || product.getId() <= 0) {
            return R.fail("请填写主键ID");
        }
        product.setImg(FileUploadUtil.uploadFile2AliOssSplit(product.getImg()));
        product.setImgs(FileUploadUtil.uploadFile2AliOssSplit(product.getImgs()));
        product.setImgsEn(FileUploadUtil.uploadFile2AliOssSplit(product.getImgsEn()));
        return R.ok(productMapper.updateById(product));
    }

    @Override
    public R addRoom(@RequestBody PRoom room) {

        room.setImg(FileUploadUtil.uploadFile2AliOssSplit(room.getImg()));
        room.setImgs(FileUploadUtil.uploadFile2AliOssSplit(room.getImgs()));
        room.setImgsEn(FileUploadUtil.uploadFile2AliOssSplit(room.getImgsEn()));


        //如果存在就更新
        if (room.getId()!=null&&room.getId()>0) {
            if (roomMapper.selectById(room.getId())!=null) {
                return updateRoom(room);
            }
        }


        return R.ok(roomMapper.insert(room));
    }

    @Override
    public R updateRoom(@RequestBody PRoom room) {
        if (room.getId() == null || room.getId() <= 0) {
            return R.fail("请填写主键ID");
        }
        room.setImg(FileUploadUtil.uploadFile2AliOssSplit(room.getImg()));
        room.setImgs(FileUploadUtil.uploadFile2AliOssSplit(room.getImgs()));
        room.setImgsEn(FileUploadUtil.uploadFile2AliOssSplit(room.getImgsEn()));
        return R.ok(roomMapper.updateById(room));
    }

    @Override
    public R addSingle(@RequestBody PSingle single) {

        single.setImg(FileUploadUtil.uploadFile2AliOssSplit(single.getImg()));
        single.setImgs(FileUploadUtil.uploadFile2AliOssSplit(single.getImgs()));
        single.setImgsEn(FileUploadUtil.uploadFile2AliOssSplit(single.getImgsEn()));


        //如果存在就更新
        if (single.getId()!=null&&single.getId()>0) {
            if (singleMapper.selectById(single.getId())!=null) {
                return updateSingle(single);
            }
        }


        return R.ok(singleMapper.insert(single));
    }

    @Override
    public R updateSingle(@RequestBody PSingle single) {
        if (single.getId() == null || single.getId() <= 0) {
            return R.fail("请填写主键ID");
        }
        single.setImg(FileUploadUtil.uploadFile2AliOssSplit(single.getImg()));
        single.setImgs(FileUploadUtil.uploadFile2AliOssSplit(single.getImgs()));
        single.setImgsEn(FileUploadUtil.uploadFile2AliOssSplit(single.getImgsEn()));
        return R.ok(singleMapper.updateById(single));
    }


    @Override
    public R addLabel(@RequestBody SLabel label) {

        //如果存在就更新
        if (label.getId()!=null&&label.getId()>0) {
            if (labelMapper.selectById(label.getId())!=null) {
                return updateLabel(label);
            }
        }


        return R.ok(labelMapper.insert(label));
    }

    @Override
    public R updateLabel(@RequestBody SLabel label) {
        if (label.getId() == null || label.getId() <= 0) {
            return R.fail("请填写主键ID");
        }

        LambdaUpdateWrapper<SLabel> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(SLabel::getId, label.getId());
        if (StrUtil.isNotBlank(label.getName())) {
            updateWrapper.set(SLabel::getName, label.getName());
        }
        if (StrUtil.isNotBlank(label.getNameEn())) {
            updateWrapper.set(SLabel::getNameEn, label.getNameEn());
        }
        if (label.getClasssifyId() != null) {
            updateWrapper.set(SLabel::getClasssifyId, label.getClasssifyId());
        }
        if (label.getLabelClass() != null) {
            updateWrapper.set(SLabel::getLabelClass, label.getLabelClass());
        }
        if (label.getSort() != null) {
            updateWrapper.set(SLabel::getSort, label.getSort());
        }
        if (label.getIsShow() != null) {
            updateWrapper.set(SLabel::getIsShow, label.getIsShow());
        }
        if (label.getHits() != null) {
            updateWrapper.setSql("hits = hits + 1");
        }

        return R.ok(labelMapper.update(null, updateWrapper));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public R addBoxlist(@RequestBody SBoxlist boxlist) {

        boxlist.setCadPath(FileUploadUtil.uploadFile2AliOssSplit(boxlist.getCadPath()));
        boxlist.setPdfPath(FileUploadUtil.uploadFile2AliOssSplit(boxlist.getPdfPath()));
        boxlist.setRarPath(FileUploadUtil.uploadFile2AliOssSplit(boxlist.getRarPath()));
        boxlist.setImgPath(FileUploadUtil.uploadFile2AliOssSplit(boxlist.getImgPath()));


        //如果存在就更新
        if (boxlist.getListNo()!=null&&boxlist.getListNo()>0) {
            QueryWrapper<SBoxlist> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("list_no", boxlist.getListNo());
            if (boxlistMapper.selectCount(queryWrapper)>0) {
                return updateBoxlist(boxlist);
            }
        }



        int insert = boxlistMapper.insert(boxlist);
        if (insert > 0 && CollUtil.isNotEmpty(boxlist.getDetailList())) {
            for (SBoxlistDetail detail : boxlist.getDetailList()) {

                detail.setImgPath(FileUploadUtil.uploadFile2AliOssSplit(detail.getImgPath()));
                boxlistDetailMapper.insert(detail);
            }
        }
        return R.ok(insert);
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public R updateBoxlist(@RequestBody SBoxlist boxlist) {
        if (boxlist.getListNo() == null || boxlist.getListNo() <= 0) {
            return R.fail("请填写listno");
        }


        boxlist.setCadPath(FileUploadUtil.uploadFile2AliOssSplit(boxlist.getCadPath()));
        boxlist.setPdfPath(FileUploadUtil.uploadFile2AliOssSplit(boxlist.getPdfPath()));
        boxlist.setRarPath(FileUploadUtil.uploadFile2AliOssSplit(boxlist.getRarPath()));
        boxlist.setImgPath(FileUploadUtil.uploadFile2AliOssSplit(boxlist.getImgPath()));

        QueryWrapper<SBoxlist> sBoxlistQueryWrapper = new QueryWrapper<>();
        sBoxlistQueryWrapper.eq("list_no", boxlist.getListNo());
        int result = boxlistMapper.update(boxlist, sBoxlistQueryWrapper);
        if (result > 0) {
            if (CollUtil.isNotEmpty(boxlist.getDetailList())) {
                QueryWrapper<SBoxlistDetail> queryWrapper = new QueryWrapper<>();
                queryWrapper.eq("list_no", boxlist.getListNo());
                boxlistDetailMapper.delete(queryWrapper);


                for (SBoxlistDetail detail : boxlist.getDetailList()) {

                    detail.setImgPath(FileUploadUtil.uploadFile2AliOssSplit(detail.getImgPath()));
                    boxlistDetailMapper.insert(detail);
                }
            }

        }


        return R.ok(result);
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public R delBoxlist(@RequestBody SBoxlist boxlist) {

        QueryWrapper<SBoxlist> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("list_no", boxlist.getListNo());
        boxlistMapper.delete(queryWrapper);

        QueryWrapper<SBoxlistDetail> queryWrapper1 = new QueryWrapper<>();
        queryWrapper1.eq("list_no", boxlist.getListNo());
        boxlistDetailMapper.delete(queryWrapper1);

        return R.ok();
    }

    @Override
    public R addBoxlistDetail(@RequestBody SBoxlistDetail boxlistDetail) {

        //如果存在就更新
        if (boxlistDetail.getId()!=null&&boxlistDetail.getId()>0) {
            if (boxlistDetailMapper.selectById(boxlistDetail.getId())!=null) {
                return updateBoxlistDetail(boxlistDetail);
            }
        }

        return R.ok(boxlistDetailMapper.insert(boxlistDetail));
    }

    @Override
    public R updateBoxlistDetail(@RequestBody SBoxlistDetail boxlistDetail) {
        if (boxlistDetail.getId() == null || boxlistDetail.getId() <= 0) {
            return R.fail("请填写id");
        }
        return R.ok(boxlistDetailMapper.updateById(boxlistDetail));
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public R addProlist(@RequestBody SProlist prolist) {


        prolist.setCadPath(FileUploadUtil.uploadFile2AliOssSplit(prolist.getCadPath()));
        prolist.setPdfPath(FileUploadUtil.uploadFile2AliOssSplit(prolist.getPdfPath()));
        prolist.setRarPath(FileUploadUtil.uploadFile2AliOssSplit(prolist.getRarPath()));
        prolist.setImgPath(FileUploadUtil.uploadFile2AliOssSplit(prolist.getImgPath()));


        //如果存在就更新
        if (prolist.getListNo()!=null&&prolist.getListNo()>0) {
            QueryWrapper<SProlist> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("list_no", prolist.getListNo());
            if (prolistMapper.selectCount(queryWrapper)>0) {
                return updateProlist(prolist);
            }
        }


        int insert = prolistMapper.insert(prolist);
        if (insert > 0 && CollUtil.isNotEmpty(prolist.getDetailList())) {
            for (SProlistDetails detail : prolist.getDetailList()) {


                detail.setImgPath(FileUploadUtil.uploadFile2AliOssSplit(detail.getImgPath()));
                prolistDetailsMapper.insert(detail);
            }
        }
        return R.ok(insert);
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public R updateProlist(@RequestBody SProlist prolist) {
        if (prolist.getListNo() == null || prolist.getListNo() <= 0) {
            return R.fail("请填写listno");
        }

        prolist.setCadPath(FileUploadUtil.uploadFile2AliOssSplit(prolist.getCadPath()));
        prolist.setPdfPath(FileUploadUtil.uploadFile2AliOssSplit(prolist.getPdfPath()));
        prolist.setRarPath(FileUploadUtil.uploadFile2AliOssSplit(prolist.getRarPath()));
        prolist.setImgPath(FileUploadUtil.uploadFile2AliOssSplit(prolist.getImgPath()));

        QueryWrapper<SProlist> sProlistQueryWrapper = new QueryWrapper<>();
        sProlistQueryWrapper.eq("list_no", prolist.getListNo());

        int result = prolistMapper.update(prolist, sProlistQueryWrapper);
        if (result > 0) {

            if (CollUtil.isNotEmpty(prolist.getDetailList())) {

                QueryWrapper<SProlistDetails> queryWrapper = new QueryWrapper<>();
                queryWrapper.eq("pro_no", prolist.getListNo());
                prolistDetailsMapper.delete(queryWrapper);


                for (SProlistDetails detail : prolist.getDetailList()) {

                    detail.setImgPath(FileUploadUtil.uploadFile2AliOssSplit(detail.getImgPath()));
                    detail.setProNo(prolist.getListNo());
                    prolistDetailsMapper.insert(detail);
                }
            }

        }

        return R.ok(result);
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public R delProlist(@RequestBody SProlist prolist) {
        if (prolist.getListNo() == null || prolist.getListNo() <= 0) {
            return R.fail("请填写listno");
        }
        QueryWrapper<SProlist> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("list_no", prolist.getListNo());
        prolistMapper.delete(queryWrapper);


        QueryWrapper<SProlistDetails> queryWrapper1 = new QueryWrapper<>();
        queryWrapper1.eq("pro_no", prolist.getListNo());
        prolistDetailsMapper.delete(queryWrapper1);


        return R.ok();

    }

    @Override
    public R addProlistDetail(@RequestBody SProlistDetails prolistDetails) {

        //如果存在就更新
        if (prolistDetails.getId()!=null&&prolistDetails.getId()>0) {
            if (prolistDetailsMapper.selectById(prolistDetails.getId())!=null) {
                return updateProlistDetail(prolistDetails);
            }
        }

        return R.ok(prolistDetailsMapper.insert(prolistDetails));
    }

    @Override
    public R updateProlistDetail(@RequestBody SProlistDetails prolistDetails) {
        if (prolistDetails.getId() == null || prolistDetails.getId() <= 0) {
            return R.fail("请填写id");
        }
        return R.ok(prolistDetailsMapper.updateById(prolistDetails));
    }

    @Override
    public R allImg() {


        LambdaQueryWrapper<PAir> pAirLambdaQueryWrapper = new LambdaQueryWrapper<>();
        pAirLambdaQueryWrapper.ne(PAir::getImg,"").or().ne(PAir::getImgs,"");
        List<PAir> pAirList = airMapper.selectList(pAirLambdaQueryWrapper);
        if (CollUtil.isNotEmpty(pAirList)) {
            PAir model = new PAir();
            for (PAir item : pAirList) {
                model.setId(item.getId());
                model.setImg(item.getImg());
                model.setImgs(item.getImgs());
                syncDataUtil.post(SyncDataUtil.air_update,model);
            }
        }

        LambdaQueryWrapper<PEps> epsLambdaQueryWrapper = new LambdaQueryWrapper<>();
        epsLambdaQueryWrapper.ne(PEps::getImg,"").or().ne(PEps::getImgs,"");
        List<PEps> epsList = epsMapper.selectList(epsLambdaQueryWrapper);
        if (CollUtil.isNotEmpty(epsList)) {
            PEps model = new PEps();
            for (PEps item : epsList) {
                model.setId(item.getId());
                model.setImg(item.getImg());
                model.setImgs(item.getImgs());
                syncDataUtil.post(SyncDataUtil.eps_update,model);
            }
        }

        LambdaQueryWrapper<PPemonitor> pemonitorLambdaQueryWrapper = new LambdaQueryWrapper<>();
        pemonitorLambdaQueryWrapper.ne(PPemonitor::getImg,"").or().ne(PPemonitor::getImgs,"");
        List<PPemonitor> pemonitorList = pemonitorMapper.selectList(pemonitorLambdaQueryWrapper);
        if (CollUtil.isNotEmpty(pemonitorList)) {
            PPemonitor model = new PPemonitor();
            for (PPemonitor item : pemonitorList) {
                model.setId(item.getId());
                model.setImg(item.getImg());
                model.setImgs(item.getImgs());
                syncDataUtil.post(SyncDataUtil.pemonitor_update,model);
            }
        }

        LambdaQueryWrapper<PProduct> productLambdaQueryWrapper = new LambdaQueryWrapper<>();
        productLambdaQueryWrapper.ne(PProduct::getImg,"").or().ne(PProduct::getImgs,"");
        List<PProduct> productList = productMapper.selectList(productLambdaQueryWrapper);
        if (CollUtil.isNotEmpty(productList)) {
            PProduct model = new PProduct();
            for (PProduct item : productList) {
                model.setId(item.getId());
                model.setImg(item.getImg());
                model.setImgs(item.getImgs());
                syncDataUtil.post(SyncDataUtil.product_update,model);
            }
        }

        LambdaQueryWrapper<PRoom> roomLambdaQueryWrapper = new LambdaQueryWrapper<>();
        roomLambdaQueryWrapper.ne(PRoom::getImg,"").or().ne(PRoom::getImgs,"");
        List<PRoom> roomList = roomMapper.selectList(roomLambdaQueryWrapper);
        if (CollUtil.isNotEmpty(roomList)) {
            PRoom model = new PRoom();
            for (PRoom item : roomList) {
                model.setId(item.getId());
                model.setImg(item.getImg());
                model.setImgs(item.getImgs());
                syncDataUtil.post(SyncDataUtil.room_update,model);
            }
        }

        LambdaQueryWrapper<PSingle> singleLambdaQueryWrapper = new LambdaQueryWrapper<>();
        singleLambdaQueryWrapper.ne(PSingle::getImg,"").or().ne(PSingle::getImgs,"");
        List<PSingle> singleList = singleMapper.selectList(singleLambdaQueryWrapper);
        if (CollUtil.isNotEmpty(singleList)) {
            PSingle model = new PSingle();
            for (PSingle item : singleList) {
                model.setId(item.getId());
                model.setImg(item.getImg());
                model.setImgs(item.getImgs());
                syncDataUtil.post(SyncDataUtil.single_update,model);
            }
        }

        LambdaQueryWrapper<PUps> upsLambdaQueryWrapper = new LambdaQueryWrapper<>();
        upsLambdaQueryWrapper.ne(PUps::getImg,"").or().ne(PUps::getImgs,"");
        List<PUps> upsList = upsMapper.selectList(upsLambdaQueryWrapper);
        if (CollUtil.isNotEmpty(upsList)) {
            PUps model = new PUps();
            for (PUps item : upsList) {
                model.setId(item.getId());
                model.setImg(item.getImg());
                model.setImgs(item.getImgs());
                syncDataUtil.post(SyncDataUtil.ups_update,model);
            }
        }

        LambdaQueryWrapper<SBoxlist> boxlistLambdaQueryWrapper = new LambdaQueryWrapper<>();
        boxlistLambdaQueryWrapper.ne(SBoxlist::getCadPath,"").or().ne(SBoxlist::getPdfPath,"").or().ne(SBoxlist::getRarPath,"").or().ne(SBoxlist::getImgPath,"");
        List<SBoxlist> boxlistList = boxlistMapper.selectList(boxlistLambdaQueryWrapper);
        if (CollUtil.isNotEmpty(boxlistList)) {
            SBoxlist model = new SBoxlist();
            for (SBoxlist item : boxlistList) {
                model.setId(item.getId());
                model.setCadPath(item.getCadPath());
                model.setPdfPath(item.getPdfPath());
                model.setRarPath(item.getRarPath());
                model.setImgPath(item.getImgPath());
                syncDataUtil.post(SyncDataUtil.boxlist_update,model);
            }
        }


        LambdaQueryWrapper<SProlist> prolistLambdaQueryWrapper = new LambdaQueryWrapper<>();
        prolistLambdaQueryWrapper.ne(SProlist::getCadPath,"").or().ne(SProlist::getPdfPath,"").or().ne(SProlist::getRarPath,"").or().ne(SProlist::getImgPath,"");
        List<SProlist> prolistList = prolistMapper.selectList(prolistLambdaQueryWrapper);
        if (CollUtil.isNotEmpty(prolistList)) {
            SProlist model = new SProlist();
            for (SProlist item : prolistList) {
                model.setId(item.getId());
                model.setCadPath(item.getCadPath());
                model.setPdfPath(item.getPdfPath());
                model.setRarPath(item.getRarPath());
                model.setImgPath(item.getImgPath());
                syncDataUtil.post(SyncDataUtil.prolist_update,model);
            }
        }
        
        return R.ok();
    }

}
