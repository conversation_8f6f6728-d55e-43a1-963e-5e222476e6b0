package com.rw.product.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;

import com.rw.common.core.domain.R;
import com.rw.common.core.web.controller.BaseController;
import com.rw.common.core.web.domain.AjaxResult;
import com.rw.common.core.web.page.TableDataInfo;
import com.rw.product.mapper.PAttrMapper;
import com.rw.product.model.entity.PAir;
import com.rw.product.model.entity.PAttr;
import com.rw.product.service.IPAttrService;
import com.rw.product.service.UserService;
import com.rw.product.utils.ProductConditionsUtil;
import com.rw.product.utils.SyncDataUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.checkerframework.checker.units.qual.A;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 属性 存储各类别的属性 比如：品牌 系列 容量 压力等等Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-07-23
 */
@Slf4j
@RestController
@AllArgsConstructor
public class PAttrServiceImpl extends BaseController implements IPAttrService
{
    @Autowired
    UserService userService;
    @Autowired
    PAttrMapper pAttrMapper;


    @Autowired
    private ProductConditionsUtil productConditionsUtil;

    @Autowired
    SyncDataUtil syncDataUtil;

    /**
     * 查询列
     * @param language
     * @return
     */
    private String getColumns(String language){
        String columns=" id, title, father_id, main_class, sort, is_show, del_flag, create_id, create_by,create_time, update_id, update_by, update_time, remark";
        if ("en".equals(language)) {
            columns = "id, title_en as title, father_id, main_class, sort, is_show, del_flag, create_id, create_by,create_time, update_id, update_by, update_time, remark";
        }
        return columns;
    }


    @Override
    public AjaxResult listAll(PAttr pAttr) {
        QueryWrapper<PAttr> queryWrapper = new QueryWrapper<>();
        queryWrapper.setEntity(pAttr);
        queryWrapper.orderByAsc("id");
        List<PAttr> list =pAttrMapper.selectList(queryWrapper);
        return AjaxResult.success(list);
    }

    /**
     * 查询属性 存储各类别的属性 比如：品牌 系列 容量 压力等等列表
     */
    @Override
    public TableDataInfo list(PAttr pAttr)
    {
        QueryWrapper<PAttr> queryWrapper = new QueryWrapper<>();
        queryWrapper.setEntity(pAttr);
        queryWrapper.orderByAsc("id");
        List<PAttr> list =pAttrMapper.selectList(queryWrapper);

        //如果查询数据为空直接返回
        if (CollUtil.isEmpty(list)) {
            return getDataTable(list);
        }


        //过滤出一级
        List<PAttr> one = list.stream().filter(x -> x.getFatherId().equals(0)).collect(Collectors.toList());

        //如果没有一级数据，直接返回结果
        if (CollUtil.isEmpty(one)) {
            return getDataTable(list);
        }

        long total = one.size();


        //过滤后的结果集
        List<PAttr> resultList = CollUtil.newArrayList();

        //内存分页，只按一级分
        int startIndex = (pAttr.getPageNum() - 1) * pAttr.getPageSize();
        int endIndex = Math.min(startIndex + pAttr.getPageSize(), one.size());
        one=one.subList(startIndex, endIndex);


        for (PAttr item : one) {
            List<PAttr> child = fillChild(list, item);
            resultList.add(item);
            resultList.addAll(child);
        }


        return getDataTable(resultList,total);
    }


    private List<PAttr> fillChild(List<PAttr> source,PAttr data){

        List<PAttr> result = new ArrayList<>();

        List<PAttr> collect = source.stream().filter(x -> x.getFatherId().equals(data.getId())).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(collect)) {
            result.addAll(collect);

            for (PAttr item : collect) {
                List<PAttr> child = fillChild(source, item);
                if (CollUtil.isNotEmpty(child)) {
                    result.addAll(child);
                }
            }
        }

        return result;
    }


    /**
     * 获取属性 存储各类别的属性 比如：品牌 系列 容量 压力等等详细信息
     */
    @Override
    public AjaxResult getInfo(@PathVariable("id") Integer id)
    {
        return AjaxResult.success(pAttrMapper.selectById(id));
    }

    /**
     * 新增属性 存储各类别的属性 比如：品牌 系列 容量 压力等等
     */
    @Override
    public AjaxResult add(@RequestBody PAttr pAttr)
    {
        pAttr.setCreateId(userService.getUserId());
        pAttr.setCreateBy(userService.getNickname());

        //同步数据
        pAttr.setId(Convert.toInt(syncDataUtil.post(SyncDataUtil.attr_add, pAttr)));

        return toAjax(pAttrMapper.insert(pAttr));
    }

    /**
     * 修改属性 存储各类别的属性 比如：品牌 系列 容量 压力等等
     */
    @Override
    public AjaxResult edit(@RequestBody PAttr pAttr)
    {
        pAttr.setUpdateId(userService.getUserId());
        pAttr.setUpdateBy(userService.getNickname());


        //同步数据
        syncDataUtil.post(SyncDataUtil.attr_update, pAttr);


        return toAjax(pAttrMapper.updateById(pAttr));
    }

    /**
     * 删除属性 存储各类别的属性 比如：品牌 系列 容量 压力等等
     */
    @Override
    public AjaxResult remove(@PathVariable Integer[] ids)
    {
        return toAjax(pAttrMapper.deleteBatchIds(Arrays.asList(ids)));
    }

    @Override
    public AjaxResult getAllAttrGroupByMainClass(Integer mainClass) {
        return AjaxResult.success(productConditionsUtil.getProductParameters(mainClass));
    }

    @Override
    public AjaxResult getAttrByFatherId(Integer fatherId) {
        QueryWrapper<PAttr> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("father_id", fatherId);
        queryWrapper.eq("is_show", 1);
        return AjaxResult.success(pAttrMapper.selectList(queryWrapper));
    }

    @Override
    public AjaxResult getAccessoryRelatedAttrByFatherId(Integer fatherId) {
        QueryWrapper<PAttr> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("father_id", fatherId);
        queryWrapper.eq("is_show", 1);
        queryWrapper.like("title", "品牌");



        PAttr pAttr = pAttrMapper.selectOne(queryWrapper);
        if(pAttr!=null){
            queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("father_id", pAttr.getId());
            queryWrapper.eq("is_show", 1);
            return AjaxResult.success(pAttrMapper.selectList(queryWrapper));
        }
        return AjaxResult.success(new ArrayList<>());
    }

    @Override
    public R getAttrList(@RequestBody PAttr pAttr) {
        QueryWrapper<PAttr> queryWrapper = new QueryWrapper<>();
        queryWrapper.select(getColumns(pAttr.getLanguage()));
        if (pAttr.getFatherId()!=null) {
            queryWrapper.eq("father_id", pAttr.getFatherId());
        }
        if (pAttr.getIsShow()!=null) {
            queryWrapper.eq("is_show", pAttr.getIsShow());
        }
        queryWrapper.orderByAsc("sort").orderByDesc("create_time");
        return R.ok(pAttrMapper.selectList(queryWrapper));
    }
}
