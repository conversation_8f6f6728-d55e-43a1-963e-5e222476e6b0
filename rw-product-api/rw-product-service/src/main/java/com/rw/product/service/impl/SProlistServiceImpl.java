package com.rw.product.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.rw.common.core.web.controller.BaseController;
import com.rw.common.core.web.domain.AjaxResult;
import com.rw.common.core.web.page.TableDataInfo;
import com.rw.product.mapper.SBoxlistMapper;
import com.rw.product.mapper.SProlistDetailsMapper;
import com.rw.product.mapper.SProlistMapper;
import com.rw.product.model.entity.*;
import com.rw.product.model.enums.ProlistStatusEnum;
import com.rw.product.service.ISProlistService;
import com.rw.product.service.UserService;
import com.rw.product.utils.SerialNumberUtil;
import com.rw.product.utils.SyncDataUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import static com.rw.product.model.constant.ProductMainClassConstants.air;

/**
 * 方案组合 包含 一级配电柜,机房能源配置Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-07-23
 */
@Slf4j
@RestController
@AllArgsConstructor
public class SProlistServiceImpl extends BaseController implements ISProlistService
{
    @Autowired
    UserService userService;
    @Autowired
    SProlistMapper sProlistMapper;
    @Autowired
    SProlistDetailsMapper prolistDetailsMapper;
    @Autowired
    SBoxlistMapper boxlistMapper;

    @Autowired
    SerialNumberUtil serialNumberUtil;


    @Autowired
    SyncDataUtil syncDataUtil;

    /**
     * 查询方案组合 包含 一级配电柜,机房能源配置列表
     */
    @Override
    public TableDataInfo list(SProlist sProlist)
    {
        startPage();
        QueryWrapper<SProlist> queryWrapper = new QueryWrapper<>();

        queryWrapper.eq("classify", sProlist.getClassify());
        if(sProlist.getListNo()!=null && sProlist.getListNo()>0){
            queryWrapper.eq("list_no", sProlist.getListNo());
        }

        if (StrUtil.isNotBlank(sProlist.getTitle())) {
            queryWrapper.like("title", sProlist.getTitle());
        }

        //如果标签不为空
        if (StrUtil.isNotBlank(sProlist.getLabelId())) {

            List<String> conditions = new ArrayList<>();
            for (String id : sProlist.getLabelId().split(",")) {
                conditions.add("find_in_set("+id+",label_id)>0");
            }
            String orConditions = String.join(" or ", conditions);

            queryWrapper.apply("( "+orConditions+" )");
        }


        queryWrapper.orderByDesc("id");


        List<SProlist> list =sProlistMapper.selectList(queryWrapper);
        return getDataTable(list);
    }


    /**
     * 获取方案组合 包含 一级配电柜,机房能源配置详细信息
     */
    @Override
    public AjaxResult getInfo(@PathVariable("id") Integer id)
    {

        SProlist prolist = sProlistMapper.selectById(id);

        QueryWrapper<SProlistDetails> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("pro_no",prolist.getListNo());
        List<SProlistDetails> detailList = prolistDetailsMapper.selectList(queryWrapper);

        prolist.setDetailList(getBoxlistInfo(detailList));



        return AjaxResult.success(prolist);
    }




    private List<SProlistDetails> getBoxlistInfo(List<SProlistDetails> detailList){


        if (CollUtil.isEmpty(detailList)) {
            return detailList;
        }

        List<Long> listno = detailList.stream().map(x -> x.getListNo()).collect(Collectors.toList());


        QueryWrapper<SBoxlist> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("list_no",listno);
        List<SBoxlist> boxlist = boxlistMapper.selectList(queryWrapper);
        if (CollUtil.isEmpty(boxlist)) {
            return detailList;
        }

        for (SBoxlist item : boxlist) {
            List<SProlistDetails> currentPrelistDetailList = detailList.stream().filter(x -> x.getListNo().equals(item.getListNo())).collect(Collectors.toList());
            if (CollUtil.isEmpty(currentPrelistDetailList)) {
                continue;
            }
            for (SProlistDetails details : currentPrelistDetailList) {
                details.setBoxlistTitle(item.getTitle());
                details.setEleprice(item.getEleprice());
                details.setImgPath(item.getImgPath());
                details.setTotalPrice(NumberUtil.mul(details.getNum(),details.getEleprice()));
            }
        }
        return detailList;
    }






    /**
     * 新增方案组合 包含 一级配电柜,机房能源配置
     */
    @Override
    public AjaxResult add(@RequestBody SProlist sProlist)
    {
        sProlist.setCreateId(userService.getUserId());
        sProlist.setCreateBy(userService.getNickname());
        sProlist.setListNo(serialNumberUtil.getProlistNo());
        //默认审核通过
        sProlist.setStatus(ProlistStatusEnum.review_passed.getCode());

        if (CollUtil.isNotEmpty(sProlist.getDetailList())) {
            for (SProlistDetails item : sProlist.getDetailList()) {
                item.setCreateId(userService.getUserId());
                item.setCreateBy(userService.getNickname());
                item.setProNo(sProlist.getListNo());
            }
        }

        //同步数据
        sProlist.setId(Convert.toInt(syncDataUtil.post(SyncDataUtil.prolist_add, sProlist)));


        int result = sProlistMapper.insert(sProlist);
        if (result>0) {
            if (CollUtil.isNotEmpty(sProlist.getDetailList())) {
                for (SProlistDetails item : sProlist.getDetailList()) {
                    prolistDetailsMapper.insert(item);
                }
            }
        }


        return toAjax(result);
    }

    /**
     * 修改方案组合 包含 一级配电柜,机房能源配置
     */
    @Override
    public AjaxResult edit(@RequestBody SProlist sProlist)
    {
        sProlist.setUpdateId(userService.getUserId());
        sProlist.setUpdateBy(userService.getNickname());

        if (CollUtil.isNotEmpty(sProlist.getDetailList())) {
            for (SProlistDetails item : sProlist.getDetailList()) {
                item.setCreateId(userService.getUserId());
                item.setCreateBy(userService.getNickname());
                item.setId(null);
                item.setProNo(sProlist.getListNo());
            }
        }

        //同步数据
        syncDataUtil.post(SyncDataUtil.prolist_update, sProlist);

        int result = sProlistMapper.updateById(sProlist);
        if (result>0) {
            QueryWrapper<SProlistDetails> deleteQueryWrapper = new QueryWrapper<>();
            deleteQueryWrapper.eq("pro_no", sProlist.getListNo());
            prolistDetailsMapper.delete(deleteQueryWrapper);


            if (CollUtil.isNotEmpty(sProlist.getDetailList())) {
                for (SProlistDetails item : sProlist.getDetailList()) {
                    prolistDetailsMapper.insert(item);
                }
            }
        }

        return toAjax(result);
    }

    /**
     * 删除方案组合 包含 一级配电柜,机房能源配置
     */
    @Override
    public AjaxResult remove(@PathVariable Integer[] ids)
    {
        return toAjax(sProlistMapper.deleteBatchIds(Arrays.asList(ids)));
    }
}
