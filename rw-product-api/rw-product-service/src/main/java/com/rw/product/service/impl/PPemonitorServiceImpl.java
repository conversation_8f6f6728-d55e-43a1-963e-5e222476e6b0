package com.rw.product.service.impl;

import cn.hutool.core.convert.Convert;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rw.common.core.domain.R;
import com.rw.common.core.web.controller.BaseController;
import com.rw.common.core.web.domain.AjaxResult;
import com.rw.common.core.web.page.TableDataInfo;
import com.rw.product.mapper.PDetailParametersMapper;
import com.rw.product.mapper.PPemonitorMapper;
import com.rw.product.model.entity.*;
import com.rw.product.service.IPPemonitorService;
import com.rw.product.service.UserService;
import com.rw.product.utils.SyncDataUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.List;

/**
 * 动环产品 包含 监控中心,配电系统,UPS系统,空调系统,漏水系统,温湿度检测,视频监控,门禁系统,消防系统Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-07-23
 */
@Slf4j
@RestController
@AllArgsConstructor
public class PPemonitorServiceImpl extends BaseController implements IPPemonitorService
{
    @Autowired
    UserService userService;
    @Autowired
    PPemonitorMapper pPemonitorMapper;


    @Autowired
    SyncDataUtil syncDataUtil;


    @Autowired
    PDetailParametersServiceImpl detailParametersService;

    /**
     * 查询动环产品 包含 监控中心,配电系统,UPS系统,空调系统,漏水系统,温湿度检测,视频监控,门禁系统,消防系统列表
     */
    @Override
    public TableDataInfo list(PPemonitor pPemonitor)
    {
        startPage();
        QueryWrapper<PPemonitor> queryWrapper = new QueryWrapper<>();
        queryWrapper.setEntity(pPemonitor);
        queryWrapper.orderByDesc("id");
        List<PPemonitor> list =pPemonitorMapper.selectList(queryWrapper);
        return getDataTable(list);
    }


    /**
     * 获取动环产品 包含 监控中心,配电系统,UPS系统,空调系统,漏水系统,温湿度检测,视频监控,门禁系统,消防系统详细信息
     */
    @Override
    public AjaxResult getInfo(@PathVariable("id") Integer id)
    {
        PPemonitor data = pPemonitorMapper.selectById(id);
        if (data!=null) {
            PDetailParameters parameters = new PDetailParameters();
            parameters.setProductId(data.getId());
            parameters.setMainClass(data.getMainClass());
            data.setDetailParameters(detailParametersService.getOneByProductIdAndMainClass(parameters));
        }
        return AjaxResult.success(data);
    }

    /**
     * 新增动环产品 包含 监控中心,配电系统,UPS系统,空调系统,漏水系统,温湿度检测,视频监控,门禁系统,消防系统
     */
    @Override
    public AjaxResult add(@RequestBody PPemonitor pPemonitor)
    {
        pPemonitor.setCreateId(userService.getUserId());
        pPemonitor.setCreateBy(userService.getNickname());

        //同步数据
        pPemonitor.setId(Convert.toInt(syncDataUtil.post(SyncDataUtil.pemonitor_add, pPemonitor)));


        int result = pPemonitorMapper.insert(pPemonitor);
        if (result>0) {
            if(pPemonitor.getDetailParameters()==null){
                pPemonitor.setDetailParameters(new PDetailParameters());
            }

            pPemonitor.getDetailParameters().setProductId(pPemonitor.getId());
            pPemonitor.getDetailParameters().setMainClass(pPemonitor.getMainClass());

            detailParametersService.addDetailParameters(pPemonitor.getDetailParameters());
        }


        return toAjax(result);
    }

    /**
     * 修改动环产品 包含 监控中心,配电系统,UPS系统,空调系统,漏水系统,温湿度检测,视频监控,门禁系统,消防系统
     */
    @Override
    public AjaxResult edit(@RequestBody PPemonitor pPemonitor)
    {
        pPemonitor.setUpdateId(userService.getUserId());
        pPemonitor.setUpdateBy(userService.getNickname());
        //同步数据
        syncDataUtil.post(SyncDataUtil.pemonitor_update, pPemonitor);


        int result = pPemonitorMapper.updateById(pPemonitor);
        if (result>0) {
            pPemonitor.getDetailParameters().setProductId(pPemonitor.getId());
            pPemonitor.getDetailParameters().setMainClass(pPemonitor.getMainClass());

            detailParametersService.addDetailParameters(pPemonitor.getDetailParameters());
        }


        return toAjax(result);
    }

    /**
     * 删除动环产品 包含 监控中心,配电系统,UPS系统,空调系统,漏水系统,温湿度检测,视频监控,门禁系统,消防系统
     */
    @Override
    public AjaxResult remove(@PathVariable Integer[] ids)
    {
        return toAjax(pPemonitorMapper.deleteBatchIds(Arrays.asList(ids)));
    }

    @Override
    public AjaxResult updateIsShow(@RequestBody PPemonitor pPemonitor) {
        PPemonitor syncModel = pPemonitorMapper.selectById(pPemonitor.getId());
        syncModel.setUpdateId(userService.getUserId());
        syncModel.setUpdateBy(userService.getNickname());
        syncModel.setIsShow(pPemonitor.getIsShow());
        //同步数据
        syncDataUtil.post(SyncDataUtil.pemonitor_update, syncModel);


        QueryWrapper<PPemonitor> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("id", syncModel.getId());

        PPemonitor model = new PPemonitor();
        model.setIsShow(syncModel.getIsShow());
        model.setUpdateId(syncModel.getUpdateId());
        model.setUpdateBy(syncModel.getUpdateBy());

        return toAjax( pPemonitorMapper.update(model,queryWrapper));
    }

    @Override
    public R page(@RequestBody PPemonitor pPemonitor) {

        Page page = new Page();
        page.setCurrent(pPemonitor.getPageNum());
        page.setSize(pPemonitor.getPageSize());

        return R.ok(pPemonitorMapper.page(page, pPemonitor));
    }

    @Override
    public R getInfoById(@RequestBody PPemonitor pPemonitor) {

        PPemonitor data = pPemonitorMapper.getInfoById(pPemonitor);
        if (data!=null) {
            PDetailParameters parameters = new PDetailParameters();
            parameters.setProductId(data.getId());
            parameters.setMainClass(data.getMainClass());
            data.setDetailParameters(detailParametersService.getOneByProductIdAndMainClass(parameters));
        }

        return R.ok(data);
    }
}
