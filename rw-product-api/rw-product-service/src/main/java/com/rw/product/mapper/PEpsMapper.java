package com.rw.product.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rw.product.model.entity.PAir;
import com.rw.product.model.entity.PEps;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
/**
 * EPS 包含 EPS管理Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-07-23
 */
@Repository
public interface PEpsMapper extends BaseMapper<PEps>
{
    IPage<PEps> page(Page page, @Param("req") PEps pEps);

    PEps getInfoById(@Param("req") PEps pEps);
}
