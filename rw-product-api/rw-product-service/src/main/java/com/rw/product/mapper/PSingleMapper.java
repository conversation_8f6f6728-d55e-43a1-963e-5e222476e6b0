package com.rw.product.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rw.product.model.entity.PSingle;
import com.rw.product.model.entity.PUps;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
/**
 * 非成套产品 包含 稳压电源管理 逆变器管理Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-07-23
 */
@Repository
public interface PSingleMapper extends BaseMapper<PSingle>
{
    IPage<PSingle> page(Page page, @Param("req") PSingle pSingle);

    PSingle getInfoById(@Param("req") PSingle pSingle);
}
