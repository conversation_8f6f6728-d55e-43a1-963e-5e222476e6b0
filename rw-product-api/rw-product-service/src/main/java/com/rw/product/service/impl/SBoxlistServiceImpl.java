package com.rw.product.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rw.common.core.domain.R;
import com.rw.common.core.utils.PageResultUtils;
import com.rw.common.core.utils.uuid.Seq;
import com.rw.common.core.web.controller.BaseController;
import com.rw.common.core.web.domain.AjaxResult;
import com.rw.common.core.web.page.TableDataInfo;
import com.rw.product.mapper.*;
import com.rw.product.model.constant.ProductMainClassConstants;
import com.rw.product.model.entity.*;
import com.rw.product.model.enums.BoxlistStatusEnum;
import com.rw.product.model.req.BoxList10ByProductIdReq;
import com.rw.product.service.ISBoxlistService;
import com.rw.product.service.UserService;
import com.rw.product.utils.SerialNumberUtil;
import com.rw.product.utils.SyncDataUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 方案 包含 配电柜,UPS,空调,安防,EPS,动环Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-07-23
 */
@Slf4j
@RestController
@AllArgsConstructor
public class SBoxlistServiceImpl extends BaseController implements ISBoxlistService
{
    @Autowired
    UserService userService;
    @Autowired
    SBoxlistMapper sBoxlistMapper;
    @Autowired
    private SLabelServiceImpl labelService;
    @Autowired
    private SBoxlistDetailMapper boxlistDetailMapper;

    @Autowired
    PAttrMapper attrMapper;

    @Autowired
    PAirMapper airMapper;
    @Autowired
    PEpsMapper epsMapper;
    @Autowired
    PPemonitorMapper pemonitorMapper;
    @Autowired
    PProductMapper productMapper;
    @Autowired
    PRoomMapper roomMapper;
    @Autowired
    PSingleMapper singleMapper;
    @Autowired
    PUpsMapper upsMapper;
    @Autowired
    SerialNumberUtil serialNumberUtil;


    @Autowired
    PAirServiceImpl airService;


    @Autowired
    SyncDataUtil syncDataUtil;


    /**
     * 查询方案 包含 配电柜,UPS,空调,安防,EPS,动环列表
     */
    @Override
    public TableDataInfo list(SBoxlist sBoxlist)
    {
        startPage();
        QueryWrapper<SBoxlist> queryWrapper = new QueryWrapper<>();

        if (sBoxlist.getClassify()!=null && sBoxlist.getClassify()>0) {
            queryWrapper.eq("classify", sBoxlist.getClassify());
        }
        if (sBoxlist.getStatus()!=null) {
            queryWrapper.eq("status", sBoxlist.getStatus());
        }

        if(sBoxlist.getListNo()!=null && sBoxlist.getListNo()>0){
            queryWrapper.eq("list_no", sBoxlist.getListNo());
        }

        if (StrUtil.isNotBlank(sBoxlist.getTitle())) {
            queryWrapper.like("title", sBoxlist.getTitle());
        }

        //如果标签不为空
        if (StrUtil.isNotBlank(sBoxlist.getLabelId())) {

            List<String> conditions = new ArrayList<>();
            for (String id : sBoxlist.getLabelId().split(",")) {
                conditions.add("find_in_set("+id+",label_id)>0");
            }
            String orConditions = String.join(" or ", conditions);

            queryWrapper.apply("( "+orConditions+" )");
        }


        queryWrapper.orderByDesc("id");


        List<SBoxlist> list =sBoxlistMapper.selectList(queryWrapper);
        return getDataTable(list);
    }


    /**
     * 获取方案 包含 配电柜,UPS,空调,安防,EPS,动环详细信息
     */
    @Override
    public AjaxResult getInfo(@PathVariable("id") Integer id)
    {
        SBoxlist boxlist = sBoxlistMapper.selectById(id);


        QueryWrapper<SBoxlistDetail> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("list_no",boxlist.getListNo());
        List<SBoxlistDetail> detailList = boxlistDetailMapper.selectList(queryWrapper);

        boxlist.setDetailList(getProductInfo(detailList));

        return AjaxResult.success(boxlist);
    }




    private List<SBoxlistDetail> getProductInfo(List<SBoxlistDetail> detailList){


        if (CollUtil.isEmpty(detailList)) {
            return detailList;
        }


        List<SBoxlistDetail> air = detailList.stream().filter(x -> ProductMainClassConstants.air.contains(Convert.toStr(x.getMainClass()))).collect(Collectors.toList());
        List<SBoxlistDetail> eps = detailList.stream().filter(x -> ProductMainClassConstants.eps.contains(Convert.toStr(x.getMainClass()))).collect(Collectors.toList());
        List<SBoxlistDetail> pemonitor = detailList.stream().filter(x -> ProductMainClassConstants.pemonitor.contains(Convert.toStr(x.getMainClass()))).collect(Collectors.toList());
        List<SBoxlistDetail> product = detailList.stream().filter(x -> ProductMainClassConstants.product.contains(Convert.toStr(x.getMainClass()))).collect(Collectors.toList());
        List<SBoxlistDetail> room = detailList.stream().filter(x -> ProductMainClassConstants.room.contains(Convert.toStr(x.getMainClass()))).collect(Collectors.toList());
        List<SBoxlistDetail> single = detailList.stream().filter(x -> ProductMainClassConstants.single.contains(Convert.toStr(x.getMainClass()))).collect(Collectors.toList());
        List<SBoxlistDetail> ups = detailList.stream().filter(x -> ProductMainClassConstants.ups.contains(Convert.toStr(x.getMainClass()))).collect(Collectors.toList());

        List<PAttr> attrList=new ArrayList<>();

        //如果包含指定分类产品
        if (CollUtil.isNotEmpty(air)) {
            //过滤出产品ID
            List<Integer> productIds = air.stream().map(x -> x.getProductId()).collect(Collectors.toList());
            //查询产品
            List<PAir> airList = airMapper.selectBatchIds(productIds);
            //如果产品不为空
            if (CollUtil.isNotEmpty(airList)) {

                //查询品牌
                List<Integer> brandIdList = airList.stream().map(x -> x.getBrand()).collect(Collectors.toList());
                if(CollUtil.isNotEmpty(brandIdList)){
                    attrList = attrMapper.selectBatchIds(brandIdList);
                }


                //循环查询出来的产品
                for (PAir item : airList) {
                    //品牌
                    PAttr pAttr = attrList.stream().filter(x -> x.getId().equals(item.getBrand())).findFirst().orElse(new PAttr());

                    //过滤出方案上相同的产品
                    List<SBoxlistDetail> details = detailList.stream().filter(x -> x.getProductId().equals(item.getId())).collect(Collectors.toList());
                    //赋值产品属性
                    if (CollUtil.isNotEmpty(details)) {
                        for (SBoxlistDetail detail : details) {

                            detail.setProductTitle(item.getTitle());
                            detail.setImgPath(item.getImg());
                            detail.setPrice(item.getPrice());
                            detail.setPriceRetail(item.getPriceRetail());
                            detail.setTotalPrice(NumberUtil.mul(detail.getNum(),detail.getPriceRetail()));
                            detail.setBrandTitle(pAttr.getTitle());

                        }
                    }
                }
            }
        }

        //如果包含指定分类产品
        if (CollUtil.isNotEmpty(eps)) {
            //过滤出产品ID
            List<Integer> productIds = eps.stream().map(x -> x.getProductId()).collect(Collectors.toList());
            //查询产品
            List<PEps> epsList = epsMapper.selectBatchIds(productIds);
            //如果产品不为空
            if (CollUtil.isNotEmpty(epsList)) {
                //查询品牌
                List<Integer> brandIdList = epsList.stream().map(x -> x.getBrand()).collect(Collectors.toList());
                if(CollUtil.isNotEmpty(brandIdList)){
                    attrList = attrMapper.selectBatchIds(brandIdList);
                }


                //循环查询出来的产品
                for (PEps item : epsList) {
                    //品牌
                    PAttr pAttr = attrList.stream().filter(x -> x.getId().equals(item.getBrand())).findFirst().orElse(new PAttr());



                    //过滤出方案上相同的产品
                    List<SBoxlistDetail> details = detailList.stream().filter(x -> x.getProductId().equals(item.getId())).collect(Collectors.toList());
                    //赋值产品属性
                    if (CollUtil.isNotEmpty(details)) {
                        for (SBoxlistDetail detail : details) {
                            detail.setProductTitle(item.getTitle());
                            detail.setImgPath(item.getImg());
                            detail.setPrice(item.getPrice());
                            detail.setPriceRetail(item.getPriceRetail());
                            detail.setTotalPrice(NumberUtil.mul(detail.getNum(),detail.getPriceRetail()));
                            detail.setBrandTitle(pAttr.getTitle());
                        }
                    }
                }
            }
        }

        //如果包含指定分类产品
        if (CollUtil.isNotEmpty(pemonitor)) {
            //过滤出产品ID
            List<Integer> productIds = pemonitor.stream().map(x -> x.getProductId()).collect(Collectors.toList());
            //查询产品
            List<PPemonitor> pemonitorList = pemonitorMapper.selectBatchIds(productIds);
            //如果产品不为空
            if (CollUtil.isNotEmpty(pemonitorList)) {

                //循环查询出来的产品
                for (PPemonitor item : pemonitorList) {

                    //过滤出方案上相同的产品
                    List<SBoxlistDetail> details = detailList.stream().filter(x -> x.getProductId().equals(item.getId())).collect(Collectors.toList());
                    //赋值产品属性
                    if (CollUtil.isNotEmpty(details)) {
                        for (SBoxlistDetail detail : details) {
                            detail.setProductTitle(item.getTitle());
                            detail.setPrice(item.getPriceRetail());
                            detail.setPriceRetail(item.getPriceRetail());
                            detail.setTotalPrice(NumberUtil.mul(detail.getNum(),detail.getPriceRetail()));
                            detail.setBrandTitle(item.getBrand());
                        }
                    }
                }
            }
        }

        //如果包含指定分类产品
        if (CollUtil.isNotEmpty(product)) {
            //过滤出产品ID
            List<Integer> productIds = product.stream().map(x -> x.getProductId()).collect(Collectors.toList());
            //查询产品
            List<PProduct> productList = productMapper.selectBatchIds(productIds);
            //如果产品不为空
            if (CollUtil.isNotEmpty(productList)) {

                //查询品牌
                List<Integer> brandIdList = productList.stream().map(x -> x.getBrand()).collect(Collectors.toList());
                if(CollUtil.isNotEmpty(brandIdList)){
                    attrList = attrMapper.selectBatchIds(brandIdList);
                }

                //循环查询出来的产品
                for (PProduct item : productList) {
                    //品牌
                    PAttr pAttr = attrList.stream().filter(x -> x.getId().equals(item.getBrand())).findFirst().orElse(new PAttr());

                    //过滤出方案上相同的产品
                    List<SBoxlistDetail> details = detailList.stream().filter(x -> x.getProductId().equals(item.getId())).collect(Collectors.toList());
                    //赋值产品属性
                    if (CollUtil.isNotEmpty(details)) {
                        for (SBoxlistDetail detail : details) {
                            detail.setProductTitle(item.getTitle());
                            detail.setImgPath(item.getImg());
                            detail.setPrice(item.getPrice());
                            detail.setPriceRetail(item.getPriceRetail());
                            detail.setTotalPrice(NumberUtil.mul(detail.getNum(),detail.getPriceRetail()));
                            detail.setBrandTitle(pAttr.getTitle());
                        }
                    }
                }
            }
        }

        //如果包含指定分类产品
        if (CollUtil.isNotEmpty(room)) {
            //过滤出产品ID
            List<Integer> productIds = room.stream().map(x -> x.getProductId()).collect(Collectors.toList());
            //查询产品
            List<PRoom> roomList = roomMapper.selectBatchIds(productIds);
            //如果产品不为空
            if (CollUtil.isNotEmpty(roomList)) {
                //查询品牌
                List<Integer> brandIdList = roomList.stream().map(x -> x.getBrand()).collect(Collectors.toList());
                if(CollUtil.isNotEmpty(brandIdList)){
                    attrList = attrMapper.selectBatchIds(brandIdList);
                }

                //循环查询出来的产品
                for (PRoom item : roomList) {
                    //品牌
                    PAttr pAttr = attrList.stream().filter(x -> x.getId().equals(item.getBrand())).findFirst().orElse(new PAttr());

                    //过滤出方案上相同的产品
                    List<SBoxlistDetail> details = detailList.stream().filter(x -> x.getProductId().equals(item.getId())).collect(Collectors.toList());
                    //赋值产品属性
                    if (CollUtil.isNotEmpty(details)) {
                        for (SBoxlistDetail detail : details) {
                            detail.setProductTitle(item.getTitle());
                            detail.setImgPath(item.getImg());
                            detail.setPrice(item.getPrice());
                            detail.setPriceRetail(item.getPriceRetail());
                            detail.setTotalPrice(NumberUtil.mul(detail.getNum(),detail.getPriceRetail()));
                            detail.setBrandTitle(pAttr.getTitle());
                        }
                    }
                }
            }
        }

        //如果包含指定分类产品
        if (CollUtil.isNotEmpty(single)) {
            //过滤出产品ID
            List<Integer> productIds = single.stream().map(x -> x.getProductId()).collect(Collectors.toList());
            //查询产品
            List<PSingle> singleList = singleMapper.selectBatchIds(productIds);
            //如果产品不为空
            if (CollUtil.isNotEmpty(singleList)) {
                //查询品牌
                List<Integer> brandIdList = singleList.stream().map(x -> x.getBrand()).collect(Collectors.toList());
                if(CollUtil.isNotEmpty(brandIdList)){
                    attrList = attrMapper.selectBatchIds(brandIdList);
                }

                //循环查询出来的产品
                for (PSingle item : singleList) {
                    //品牌
                    PAttr pAttr = attrList.stream().filter(x -> x.getId().equals(item.getBrand())).findFirst().orElse(new PAttr());

                    //过滤出方案上相同的产品
                    List<SBoxlistDetail> details = detailList.stream().filter(x -> x.getProductId().equals(item.getId())).collect(Collectors.toList());
                    //赋值产品属性
                    if (CollUtil.isNotEmpty(details)) {
                        for (SBoxlistDetail detail : details) {
                            detail.setProductTitle(item.getTitle());
                            detail.setImgPath(item.getImg());
                            detail.setPrice(item.getPrice());
                            detail.setPriceRetail(item.getPriceRetail());
                            detail.setTotalPrice(NumberUtil.mul(detail.getNum(),detail.getPriceRetail()));
                            detail.setBrandTitle(pAttr.getTitle());
                        }
                    }
                }
            }
        }

        //如果包含指定分类产品
        if (CollUtil.isNotEmpty(ups)) {
            //过滤出产品ID
            List<Integer> productIds = ups.stream().map(x -> x.getProductId()).collect(Collectors.toList());
            //查询产品
            List<PUps> upsList = upsMapper.selectBatchIds(productIds);
            //如果产品不为空
            if (CollUtil.isNotEmpty(upsList)) {
                //查询品牌
                List<Integer> brandIdList = upsList.stream().map(x -> x.getBrand()).collect(Collectors.toList());
                if(CollUtil.isNotEmpty(brandIdList)){
                    attrList = attrMapper.selectBatchIds(brandIdList);
                }


                //循环查询出来的产品
                for (PUps item : upsList) {
                    //品牌
                    PAttr pAttr = attrList.stream().filter(x -> x.getId().equals(item.getBrand())).findFirst().orElse(new PAttr());

                    //过滤出方案上相同的产品
                    List<SBoxlistDetail> details = detailList.stream().filter(x -> x.getProductId().equals(item.getId())).collect(Collectors.toList());
                    //赋值产品属性
                    if (CollUtil.isNotEmpty(details)) {
                        for (SBoxlistDetail detail : details) {
                            detail.setProductTitle(item.getTitle());
                            detail.setImgPath(item.getImg());
                            detail.setPrice(item.getPrice());
                            detail.setPriceRetail(item.getPriceRetail());
                            detail.setTotalPrice(NumberUtil.mul(detail.getNum(),detail.getPriceRetail()));
                            detail.setBrandTitle(pAttr.getTitle());
                        }
                    }
                }
            }
        }

        return detailList;
    }


    /**
     * 计算方案总金额
     * @return
     */
    private BigDecimal calculateTheAmount(SBoxlist sBoxlist){

        BigDecimal eleprice=BigDecimal.ZERO;

        if (CollUtil.isNotEmpty(sBoxlist.getDetailList())) {
            for (SBoxlistDetail detail : sBoxlist.getDetailList()) {
                eleprice=NumberUtil.add(eleprice,NumberUtil.mul(detail.getNum(),detail.getPrice()));
            }
        }

        if (ProductMainClassConstants.ups_ups.equals(Convert.toInt(sBoxlist.getClassify()))) {
            eleprice = NumberUtil.add(eleprice, sBoxlist.getMprice(), sBoxlist.getAcprice(), sBoxlist.getTax(),
                    sBoxlist.getProfit(), sBoxlist.getHandlingFee(), sBoxlist.getInstall(), sBoxlist.getDebugFee(),
                    sBoxlist.getFreight(), sBoxlist.getSheetMetalCost(), sBoxlist.getPanelCost(),
                    sBoxlist.getPackingFee());

        }

        return eleprice;
    }


    /**
     * 新增方案 包含 配电柜,UPS,空调,安防,EPS,动环
     */
    @Override
    public AjaxResult add(@RequestBody SBoxlist sBoxlist)
    {
        sBoxlist.setCreateId(userService.getUserId());
        sBoxlist.setCreateBy(userService.getNickname());
        //计算总金额
        sBoxlist.setEleprice(calculateTheAmount(sBoxlist));
        sBoxlist.setListNo(serialNumberUtil.getBoxlistNo());

        if (CollUtil.isNotEmpty(sBoxlist.getDetailList())) {
            for (SBoxlistDetail item : sBoxlist.getDetailList()) {
                item.setCreateId(userService.getUserId());
                item.setCreateBy(userService.getNickname());
                item.setListNo(sBoxlist.getListNo());
            }
        }


        //同步数据
        sBoxlist.setId(Convert.toInt(syncDataUtil.post(SyncDataUtil.boxlist_add, sBoxlist)));



        int result = sBoxlistMapper.insert(sBoxlist);
        if (result>0) {
            if (CollUtil.isNotEmpty(sBoxlist.getDetailList())) {
                for (SBoxlistDetail item : sBoxlist.getDetailList()) {
                    boxlistDetailMapper.insert(item);
                }
            }
        }

        return toAjax(result);
    }

    /**
     * 修改方案 包含 配电柜,UPS,空调,安防,EPS,动环
     */
    @Override
    public AjaxResult edit(@RequestBody SBoxlist sBoxlist)
    {
        sBoxlist.setUpdateId(userService.getUserId());
        sBoxlist.setUpdateBy(userService.getNickname());

        //计算总金额
        sBoxlist.setEleprice(calculateTheAmount(sBoxlist));


        if (CollUtil.isNotEmpty(sBoxlist.getDetailList())) {
            for (SBoxlistDetail item : sBoxlist.getDetailList()) {
                item.setId(null);
                item.setListNo(sBoxlist.getListNo());
                item.setCreateId(userService.getUserId());
                item.setCreateBy(userService.getNickname());
            }
        }


        //同步数据
        syncDataUtil.post(SyncDataUtil.boxlist_update, sBoxlist);





        int result = sBoxlistMapper.updateById(sBoxlist);
        if (result>0) {
            QueryWrapper<SBoxlistDetail> deleteQueryWrapper = new QueryWrapper<>();
            deleteQueryWrapper.eq("list_no", sBoxlist.getListNo());
            boxlistDetailMapper.delete(deleteQueryWrapper);

            if (CollUtil.isNotEmpty(sBoxlist.getDetailList())) {
                for (SBoxlistDetail item : sBoxlist.getDetailList()) {
                    boxlistDetailMapper.insert(item);
                }
            }


        }

        return toAjax(result);
    }

    /**
     * 删除方案 包含 配电柜,UPS,空调,安防,EPS,动环
     */
    @Override
    public AjaxResult remove(@PathVariable Integer[] ids)
    {
        return toAjax(sBoxlistMapper.deleteBatchIds(Arrays.asList(ids)));
    }

    @Override
    public AjaxResult passed(Integer id) {
        QueryWrapper<SBoxlist> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("id",id);
        queryWrapper.eq("status",BoxlistStatusEnum.under_review.getCode());

        SBoxlist entity = new SBoxlist();
        entity.setStatus(BoxlistStatusEnum.review_passed.getCode());


        //同步数据
        SBoxlist syncDataReq = new SBoxlist();
        syncDataReq.setId(id);
        syncDataReq.setStatus(BoxlistStatusEnum.review_passed.getCode());
        syncDataUtil.post(SyncDataUtil.boxlist_update, syncDataReq);



        return toAjax( sBoxlistMapper.update(entity, queryWrapper));
    }

    @Override
    public AjaxResult overrule(Integer id) {

        QueryWrapper<SBoxlist> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("id",id);
        queryWrapper.eq("status",BoxlistStatusEnum.under_review.getCode());

        SBoxlist entity = new SBoxlist();
        entity.setStatus(BoxlistStatusEnum.reject.getCode());



        //同步数据
        SBoxlist syncDataReq = new SBoxlist();
        syncDataReq.setId(id);
        syncDataReq.setStatus(BoxlistStatusEnum.reject.getCode());
        syncDataUtil.post(SyncDataUtil.boxlist_update, syncDataReq);

        return toAjax( sBoxlistMapper.update(entity, queryWrapper));
    }

    @Override
    public R getBoxList10ByProductId(@RequestBody BoxList10ByProductIdReq req) {

        List<SBoxlist> boxlists = sBoxlistMapper.getBoxList10ByProductId(req);


        if(boxlists!=null && boxlists.size()>0){
            List<Integer> labelIdList=new ArrayList<>();

            //循环结果
            for (SBoxlist item : boxlists) {
                //如果标签不为空
                if (StrUtil.isNotBlank(item.getLabelId())) {
                    //标签逗号分割放到labelIdList中
                    labelIdList.addAll(StrUtil.split(item.getLabelId(),",").stream().map(x->Convert.toInt(x)).collect(Collectors.toList()));
                }
            }

            //批量查询标签
            if (CollUtil.isNotEmpty(labelIdList)) {
                List<SLabel> sLabels = labelService.getBatchLabelById(labelIdList,req.getLanguage());
                if (CollUtil.isNotEmpty(sLabels)) {
                    Map<Integer, SLabel> labelMap = sLabels.stream().collect(Collectors.toMap(x -> x.getId(), x -> x));

                    //循环结果，组装标签名称
                    for (SBoxlist item : boxlists) {
                        //如果标签不为空
                        if (StrUtil.isNotBlank(item.getLabelId())) {
                            List<String> labelIdName=new ArrayList<>();

                            //逗号分割
                            String[] split = item.getLabelId().split(",");
                            //循环放到labelIdList中，后面批量查询标签
                            for (String s : split) {
                                if (labelMap.containsKey(Convert.toInt(s))) {
                                    labelIdName.add(labelMap.get(Convert.toInt(s)).getName());
                                }
                            }
                            item.setLabelIdName(StrUtil.join(" | ", labelIdName));
                        }
                    }
                }
            }
        }


        return R.ok(boxlists);
    }

    @Override
    public PageResultUtils<SBoxlist> page(@RequestBody SBoxlist sBoxlist) {
        Page page = new Page();
        page.setCurrent(sBoxlist.getPageNum());
        page.setSize(sBoxlist.getPageSize());
        IPage<SBoxlist> iPage = sBoxlistMapper.page(page, sBoxlist);
        if(iPage!=null && CollUtil.isNotEmpty(iPage.getRecords())){
            List<Integer> labelIdList=new ArrayList<>();

            //循环结果
            for (SBoxlist item : iPage.getRecords()) {
                //如果标签不为空
                if (StrUtil.isNotBlank(item.getLabelId())) {
                    //标签逗号分割放到labelIdList中
                    labelIdList.addAll(StrUtil.split(item.getLabelId(),",").stream().map(x->Convert.toInt(x)).collect(Collectors.toList()));
                }
            }

            //批量查询标签
            if (CollUtil.isNotEmpty(labelIdList)) {
                List<SLabel> sLabels = labelService.getBatchLabelById(labelIdList,sBoxlist.getLanguage());
                if (CollUtil.isNotEmpty(sLabels)) {
                    Map<Integer, SLabel> labelMap = sLabels.stream().collect(Collectors.toMap(x -> x.getId(), x -> x));

                    //循环结果，组装标签名称
                    for (SBoxlist item : iPage.getRecords()) {
                        //如果标签不为空
                        if (StrUtil.isNotBlank(item.getLabelId())) {
                            List<String> labelIdName=new ArrayList<>();

                            //逗号分割
                            String[] split = item.getLabelId().split(",");
                            //循环放到labelIdList中，后面批量查询标签
                            for (String s : split) {
                                if (labelMap.containsKey(Convert.toInt(s))) {
                                    labelIdName.add(labelMap.get(Convert.toInt(s)).getName());
                                }
                            }
                            item.setLabelIdName(StrUtil.join("/", labelIdName));
                        }
                    }
                }
            }
        }
        return new PageResultUtils<SBoxlist>((int) iPage.getTotal(), (int) iPage.getSize(), (int) iPage.getPages(), (int) iPage.getCurrent(), iPage.getRecords());
    }

    @Override
    public R getInfoById(@RequestBody SBoxlist sBoxlist) {

        SBoxlist info = sBoxlistMapper.getInfoById(sBoxlist);
        if (info!=null) {
            QueryWrapper<SBoxlistDetail> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("list_no",info.getListNo());
            List<SBoxlistDetail> detailList = boxlistDetailMapper.selectList(queryWrapper);
            info.setDetailList(detailList);
        }
        return R.ok(info);
    }



}
