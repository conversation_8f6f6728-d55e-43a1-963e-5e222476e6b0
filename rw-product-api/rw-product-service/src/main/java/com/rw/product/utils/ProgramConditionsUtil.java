package com.rw.product.utils;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.rw.common.redis.service.RedisService;
import com.rw.product.mapper.SLabelMapper;
import com.rw.product.model.entity.SLabel;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.ResourceBundle;

/**
 * 方案筛选条件
 */
@Service
public class ProgramConditionsUtil {
    /**
     * @Fields rm : Redis的业务 数据输入输出
     */
    @Autowired
    private RedisService rm;

    @Autowired
    private SLabelMapper labelMapper;


    /**
     * @Fields bundle : 方案功能参数文件
     */
    private static final ResourceBundle PROGRAMME_LABEL = ResourceBundle.getBundle("label");


    public Map<String, Object> getProgrammeLabel(Integer classify) {

        Map<String, Object> map = new HashMap<String, Object>();
        String[] fields;

        if (classify == 5) {
            fields = PROGRAMME_LABEL.getString(1 + ".label").split(",");
        } else {
            fields = PROGRAMME_LABEL.getString(classify + ".label").split(",");
        }

        for (String field : fields) {
            SLabel sLabel = new SLabel();
            sLabel.setLabelClass(classify);
            if (classify == 5) {
                sLabel.setClasssifyId(Integer.parseInt(PROGRAMME_LABEL.getString(1 + "." + field + ".label")));
            } else {
                sLabel.setClasssifyId(Integer.parseInt(PROGRAMME_LABEL.getString(classify + "." + field + ".label")));
            }
            List<SLabel> labels =this.getLabelList(sLabel);
            map.put(field + "Label", labels);
        }
        return map;
    }



    /**
     * 获取属性
     *
     * @param label
     * @return
     */
    private List<SLabel> getLabelList(SLabel label) {
        QueryWrapper<SLabel> queryWrapper = new QueryWrapper<>();
        queryWrapper.setEntity(label);
        queryWrapper.orderByAsc("sort").orderByAsc("name");
        return labelMapper.selectList(queryWrapper);
    }



    /**
     * 获取分类的品牌ID
     * @param classify
     * @return
     */
    public int getBrandId(int classify) {
        int brandId=0;
        try {
            if (classify == 5) {
                brandId=Integer.parseInt(PROGRAMME_LABEL.getString(1 + ".brand.label"));
            } else {
                brandId=Integer.parseInt(PROGRAMME_LABEL.getString(classify + ".brand.label"));
            }
        } catch (Exception e) {
            //如果报错，说明没有声明这个属性的ID，就是没有这个品牌
        }
        return brandId;
    }


}
