package com.rw.product.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rw.common.core.domain.R;
import com.rw.common.core.utils.PageResultUtils;
import com.rw.common.core.web.controller.BaseController;
import com.rw.common.core.web.domain.AjaxResult;
import com.rw.common.core.web.page.TableDataInfo;
import com.rw.product.mapper.PAttrMapper;
import com.rw.product.mapper.PDetailParametersMapper;
import com.rw.product.mapper.PSingleMapper;
import com.rw.product.model.entity.*;
import com.rw.product.service.IPSingleService;
import com.rw.product.service.UserService;
import com.rw.product.utils.SyncDataUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 非成套产品 包含 稳压电源管理 逆变器管理Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-07-23
 */
@Slf4j
@RestController
@AllArgsConstructor
public class PSingleServiceImpl extends BaseController implements IPSingleService
{
    @Autowired
    UserService userService;
    @Autowired
    PSingleMapper pSingleMapper;
    @Autowired
    PAttrMapper attrMapper;

    @Autowired
    SyncDataUtil syncDataUtil;

    @Autowired
    PDetailParametersServiceImpl detailParametersService;

    /**
     * 查询非成套产品 包含 稳压电源管理 逆变器管理列表
     */
    @Override
    public TableDataInfo list(PSingle pSingle)
    {
        startPage();
        QueryWrapper<PSingle> queryWrapper = new QueryWrapper<>();
        queryWrapper.setEntity(pSingle);
        queryWrapper.orderByDesc("id");
        List<PSingle> list =pSingleMapper.selectList(queryWrapper);

        fillAttrTitle(list);
        return getDataTable(list);
    }


    /**
     * 组装属性名称
     */
    private void fillAttrTitle(List<PSingle> list ){

        if (CollUtil.isEmpty(list)) {
            return;
        }

        List<Integer> attrIdList=new ArrayList<>();
        //取出所有属性ID
        for (PSingle item : list) {
            if (item.getBrand()!=null && item.getBrand()>0) {
                attrIdList.add(item.getBrand());
            }
            if (item.getSeries()!=null && item.getSeries()>0) {
                attrIdList.add(item.getSeries());
            }
            if (item.getCapacity()!=null && item.getCapacity()>0) {
                attrIdList.add(item.getCapacity());
            }
            if (item.getPrinciple()!=null && item.getPrinciple()>0) {
                attrIdList.add(item.getPrinciple());
            }
            if (item.getAccuracy()!=null && item.getAccuracy()>0) {
                attrIdList.add(item.getAccuracy());
            }
            if (item.getVoltage()!=null && item.getVoltage()>0) {
                attrIdList.add(item.getVoltage());
            }
            if (item.getInput()!=null && item.getInput()>0) {
                attrIdList.add(item.getInput());
            }
        }


        if (CollUtil.isEmpty(attrIdList)) {
            return;
        }
        //去重
        attrIdList = attrIdList.stream().distinct().collect(Collectors.toList());

        //根据ID批量获取属性
        List<PAttr> allAttr = attrMapper.selectBatchIds(attrIdList);

        //转换为Map
        Map<Integer, PAttr> attrMap = allAttr.stream().collect(Collectors.toMap(x -> x.getId(), x -> x));

        //循环赋值
        for (PSingle item : list) {
            if (item.getBrand()!=null && item.getBrand()>0) {
                if (attrMap.containsKey(item.getBrand())) {
                    item.setBrandTitle(attrMap.get(item.getBrand()).getTitle());
                }
            }
            if (item.getSeries()!=null && item.getSeries()>0) {
                if (attrMap.containsKey(item.getSeries())) {
                    item.setSeriesTitle(attrMap.get(item.getSeries()).getTitle());
                }
            }

            if (item.getCapacity()!=null && item.getCapacity()>0) {
                if (attrMap.containsKey(item.getCapacity())) {
                    item.setCapacityTitle(attrMap.get(item.getCapacity()).getTitle());
                }
            }

            if (item.getPrinciple()!=null && item.getPrinciple()>0) {
                if (attrMap.containsKey(item.getPrinciple())) {
                    item.setPrincipleTitle(attrMap.get(item.getPrinciple()).getTitle());
                }
            }
            if (item.getAccuracy()!=null && item.getAccuracy()>0) {
                if (attrMap.containsKey(item.getAccuracy())) {
                    item.setAccuracyTitle(attrMap.get(item.getAccuracy()).getTitle());
                }
            }

            if (item.getVoltage()!=null && item.getVoltage()>0) {
                if (attrMap.containsKey(item.getVoltage())) {
                    item.setVoltageTitle(attrMap.get(item.getVoltage()).getTitle());
                }
            }

            if (item.getInput()!=null && item.getInput()>0) {
                if (attrMap.containsKey(item.getInput())) {
                    item.setInputTitle(attrMap.get(item.getInput()).getTitle());
                }
            }

        }


    }


    /**
     * 获取非成套产品 包含 稳压电源管理 逆变器管理详细信息
     */
    @Override
    public AjaxResult getInfo(@PathVariable("id") Integer id)
    {
        PSingle data = pSingleMapper.selectById(id);
        if (data!=null) {
            PDetailParameters parameters = new PDetailParameters();
            parameters.setProductId(data.getId());
            parameters.setMainClass(data.getMainClass());
            data.setDetailParameters(detailParametersService.getOneByProductIdAndMainClass(parameters));
        }
        return AjaxResult.success(data);
    }

    /**
     * 新增非成套产品 包含 稳压电源管理 逆变器管理
     */
    @Override
    public AjaxResult add(@RequestBody PSingle pSingle)
    {
        pSingle.setCreateId(userService.getUserId());
        pSingle.setCreateBy(userService.getNickname());

        //同步数据
        pSingle.setId(Convert.toInt(syncDataUtil.post(SyncDataUtil.single_add, pSingle)));

        int result = pSingleMapper.insert(pSingle);
        if (result>0) {
            if(pSingle.getDetailParameters()==null){
                pSingle.setDetailParameters(new PDetailParameters());
            }

            pSingle.getDetailParameters().setProductId(pSingle.getId());
            pSingle.getDetailParameters().setMainClass(pSingle.getMainClass());
            detailParametersService.addDetailParameters(pSingle.getDetailParameters());
        }

        return toAjax(result);
    }

    /**
     * 修改非成套产品 包含 稳压电源管理 逆变器管理
     */
    @Override
    public AjaxResult edit(@RequestBody PSingle pSingle)
    {
        pSingle.setUpdateId(userService.getUserId());
        pSingle.setUpdateBy(userService.getNickname());
        //同步数据
        syncDataUtil.post(SyncDataUtil.single_update, pSingle);


        int result = pSingleMapper.updateById(pSingle);
        if (result>0) {
            pSingle.getDetailParameters().setProductId(pSingle.getId());
            pSingle.getDetailParameters().setMainClass(pSingle.getMainClass());


            detailParametersService.addDetailParameters(pSingle.getDetailParameters());
        }

        return toAjax(result);
    }

    /**
     * 删除非成套产品 包含 稳压电源管理 逆变器管理
     */
    @Override
    public AjaxResult remove(@PathVariable Integer[] ids)
    {
        return toAjax(pSingleMapper.deleteBatchIds(Arrays.asList(ids)));
    }

    @Override
    public AjaxResult updateIsShow(@RequestBody PSingle pSingle) {

        PSingle syncModel = pSingleMapper.selectById(pSingle.getId());
        syncModel.setUpdateId(userService.getUserId());
        syncModel.setUpdateBy(userService.getNickname());
        syncModel.setIsShow(pSingle.getIsShow());
        //同步数据
        syncDataUtil.post(SyncDataUtil.ups_update, syncModel);


        QueryWrapper<PSingle> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("id", syncModel.getId());

        PSingle model = new PSingle();
        model.setIsShow(syncModel.getIsShow());
        model.setUpdateId(syncModel.getUpdateId());
        model.setUpdateBy(syncModel.getUpdateBy());

        return toAjax( pSingleMapper.update(model,queryWrapper));
    }

    @Override
    public PageResultUtils<PSingle> page(@RequestBody PSingle pSingle) {
        Page page = new Page();
        page.setCurrent(pSingle.getPageNum());
        page.setSize(pSingle.getPageSize());
        IPage<PSingle> newsPage = pSingleMapper.page(page, pSingle);
        return new PageResultUtils<PSingle>((int) newsPage.getTotal(), (int) newsPage.getSize(), (int) newsPage.getPages(), (int) newsPage.getCurrent(), newsPage.getRecords());
    }

    @Override
    public R getInfoById(@RequestBody PSingle pSingle) {

        PSingle data = pSingleMapper.getInfoById(pSingle);
        if (data!=null) {
            PDetailParameters parameters = new PDetailParameters();
            parameters.setProductId(data.getId());
            parameters.setMainClass(data.getMainClass());
            data.setDetailParameters(detailParametersService.getOneByProductIdAndMainClass(parameters));
        }

        return R.ok(data);
    }
}
