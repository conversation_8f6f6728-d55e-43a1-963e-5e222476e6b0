package com.rw.product.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rw.common.core.domain.R;
import com.rw.common.core.utils.PageResultUtils;
import com.rw.common.core.web.controller.BaseController;
import com.rw.common.core.web.domain.AjaxResult;
import com.rw.common.core.web.page.TableDataInfo;
import com.rw.product.mapper.PAirMapper;
import com.rw.product.mapper.PAttrMapper;
import com.rw.product.mapper.PDetailParametersMapper;
import com.rw.product.model.entity.*;
import com.rw.product.service.IPAirService;
import com.rw.product.service.UserService;
import com.rw.product.utils.SyncDataUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 空调 包含 室外机管理,室内机管理,气液管管理,水管管理,制冷剂管理,空调配件管理Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-07-23
 */
@Slf4j
@RestController
@AllArgsConstructor
public class PAirServiceImpl extends BaseController implements IPAirService
{
    @Autowired
    UserService userService;
    @Autowired
    PAirMapper pAirMapper;
    @Autowired
    PAttrMapper attrMapper;

    @Autowired
    SyncDataUtil syncDataUtil;

    @Autowired
    PDetailParametersServiceImpl detailParametersService;

    /**
     * 查询空调 包含 室外机管理,室内机管理,气液管管理,水管管理,制冷剂管理,空调配件管理列表
     */
    @Override
    public TableDataInfo list(PAir pAir)
    {
        startPage();
        QueryWrapper<PAir> queryWrapper = new QueryWrapper<>();
        queryWrapper.setEntity(pAir);
        queryWrapper.orderByDesc("id");
        List<PAir> list =pAirMapper.selectList(queryWrapper);

        fillAttrTitle(list);
        return getDataTable(list);
    }


    /**
     * 组装属性名称
     */
    private void fillAttrTitle(List<PAir> list ){

        if (CollUtil.isEmpty(list)) {
            return;
        }

        List<Integer> attrIdList=new ArrayList<>();
        //取出所有属性ID
        for (PAir item : list) {
            if (item.getBrand()!=null && item.getBrand()>0) {
                attrIdList.add(item.getBrand());
            }
            if (item.getSeries()!=null && item.getSeries()>0) {
                attrIdList.add(item.getSeries());
            }
            if (item.getAirSupply()!=null && item.getAirSupply()>0) {
                attrIdList.add(item.getAirSupply());
            }
            if (item.getHeating()!=null && item.getHeating()>0) {
                attrIdList.add(item.getHeating());
            }
            if (item.getRefrigerating()!=null && item.getRefrigerating()>0) {
                attrIdList.add(item.getRefrigerating());
            }
            if (item.getElectricType()!=null && item.getElectricType()>0) {
                attrIdList.add(item.getElectricType());
            }
            if (item.getAirType()!=null && item.getAirType()>0) {
                attrIdList.add(item.getAirType());
            }
            if (item.getCoolingType()!=null && item.getCoolingType()>0) {
                attrIdList.add(item.getCoolingType());
            }
            if (item.getAccessory()!=null && item.getAccessory()>0) {
                attrIdList.add(item.getAccessory());
            }

            if (StrUtil.isNotBlank(item.getLength()) && NumberUtil.isNumber(item.getLength()) && Convert.toInt(item.getLength())>0) {
                attrIdList.add(Convert.toInt(item.getLength()));
            }

        }


        if (CollUtil.isEmpty(attrIdList)) {
            return;
        }
        //去重
        attrIdList = attrIdList.stream().distinct().collect(Collectors.toList());

        //根据ID批量获取属性
        List<PAttr> allAttr = attrMapper.selectBatchIds(attrIdList);

        //转换为Map
        Map<Integer, PAttr> attrMap = allAttr.stream().collect(Collectors.toMap(x -> x.getId(), x -> x));

        //循环赋值
        for (PAir item : list) {
            if (item.getBrand()!=null && item.getBrand()>0) {
                if (attrMap.containsKey(item.getBrand())) {
                    item.setBrandTitle(attrMap.get(item.getBrand()).getTitle());
                }
            }
            if (item.getSeries()!=null && item.getSeries()>0) {
                if (attrMap.containsKey(item.getSeries())) {
                    item.setSeriesTitle(attrMap.get(item.getSeries()).getTitle());
                }
            }

            if (item.getAirSupply()!=null && item.getAirSupply()>0) {
                if (attrMap.containsKey(item.getAirSupply())) {
                    item.setAirSupplyTitle(attrMap.get(item.getAirSupply()).getTitle());
                }
            }

            if (item.getHeating()!=null && item.getHeating()>0) {
                if (attrMap.containsKey(item.getHeating())) {
                    item.setHeatingTitle(attrMap.get(item.getHeating()).getTitle());
                }
            }

            if (item.getRefrigerating()!=null && item.getRefrigerating()>0) {
                if (attrMap.containsKey(item.getRefrigerating())) {
                    item.setRefrigeratingTitle(attrMap.get(item.getRefrigerating()).getTitle());
                }
            }

            if (item.getElectricType()!=null && item.getElectricType()>0) {
                if (attrMap.containsKey(item.getElectricType())) {
                    item.setElectricTypeTitle(attrMap.get(item.getElectricType()).getTitle());
                }
            }

            if (item.getAirType()!=null && item.getAirType()>0) {
                if (attrMap.containsKey(item.getAirType())) {
                    item.setAirTypeTitle(attrMap.get(item.getAirType()).getTitle());
                }
            }

            if (item.getCoolingType()!=null && item.getCoolingType()>0) {
                if (attrMap.containsKey(item.getCoolingType())) {
                    item.setCoolingTypeTitle(attrMap.get(item.getCoolingType()).getTitle());
                }
            }

            if (item.getAccessory()!=null && item.getAccessory()>0) {
                if (attrMap.containsKey(item.getAccessory())) {
                    item.setAccessoryTitle(attrMap.get(item.getAccessory()).getTitle());
                }
            }



            if (StrUtil.isNotBlank(item.getLength()) && NumberUtil.isNumber(item.getLength()) && Convert.toInt(item.getLength())>0) {
                if (attrMap.containsKey(Convert.toInt(item.getLength()))) {
                    item.setLengthTitle(attrMap.get(Convert.toInt(item.getLength())).getTitle());
                }

            }



        }


    }


    /**
     * 获取空调 包含 室外机管理,室内机管理,气液管管理,水管管理,制冷剂管理,空调配件管理详细信息
     */
    @Override
    public AjaxResult getInfo(@PathVariable("id") Integer id)
    {
        PAir data = pAirMapper.selectById(id);
        if (data!=null) {
            PDetailParameters parameters = new PDetailParameters();
            parameters.setProductId(data.getId());
            parameters.setMainClass(data.getMainClass());
            data.setDetailParameters(detailParametersService.getOneByProductIdAndMainClass(parameters));
        }
        return AjaxResult.success(data);
    }

    /**
     * 新增空调 包含 室外机管理,室内机管理,气液管管理,水管管理,制冷剂管理,空调配件管理
     */
    @Override
    public AjaxResult add(@RequestBody PAir pAir)
    {
        pAir.setCreateId(userService.getUserId());
        pAir.setCreateBy(userService.getNickname());

        //同步数据
        pAir.setId(Convert.toInt(syncDataUtil.post(SyncDataUtil.air_add, pAir)));

        int result = pAirMapper.insert(pAir);
        if (result>0) {
            if(pAir.getDetailParameters()==null){
                pAir.setDetailParameters(new PDetailParameters());
            }
            pAir.getDetailParameters().setProductId(pAir.getId());
            pAir.getDetailParameters().setMainClass(pAir.getMainClass());

            detailParametersService.addDetailParameters(pAir.getDetailParameters());
        }

        return toAjax(result);
    }

    /**
     * 修改空调 包含 室外机管理,室内机管理,气液管管理,水管管理,制冷剂管理,空调配件管理
     */
    @Override
    public AjaxResult edit(@RequestBody PAir pAir)
    {
        pAir.setUpdateId(userService.getUserId());
        pAir.setUpdateBy(userService.getNickname());

        //同步数据
        syncDataUtil.post(SyncDataUtil.air_update, pAir);


        int result = pAirMapper.updateById(pAir);
        if (result>0) {
            pAir.getDetailParameters().setProductId(pAir.getId());
            pAir.getDetailParameters().setMainClass(pAir.getMainClass());

            detailParametersService.addDetailParameters(pAir.getDetailParameters());

        }

        return toAjax(result);
    }

    /**
     * 删除空调 包含 室外机管理,室内机管理,气液管管理,水管管理,制冷剂管理,空调配件管理
     */
    @Override
    public AjaxResult remove(@PathVariable Integer[] ids)
    {
        return toAjax(pAirMapper.deleteBatchIds(Arrays.asList(ids)));
    }


    @Override
    public AjaxResult updateIsShow(@RequestBody PAir pAir) {
        PAir syncModel = pAirMapper.selectById(pAir.getId());
        syncModel.setUpdateId(userService.getUserId());
        syncModel.setUpdateBy(userService.getNickname());
        syncModel.setIsShow(pAir.getIsShow());
        //同步数据
        syncDataUtil.post(SyncDataUtil.air_update, syncModel);


        QueryWrapper<PAir> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("id", syncModel.getId());

        PAir model = new PAir();
        model.setIsShow(syncModel.getIsShow());
        model.setUpdateId(syncModel.getUpdateId());
        model.setUpdateBy(syncModel.getUpdateBy());

        return toAjax( pAirMapper.update(model,queryWrapper));
    }






    @Override
    public PageResultUtils<PAir> page(@RequestBody PAir pAir) {
        Page page = new Page();
        page.setCurrent(pAir.getPageNum());
        page.setSize(pAir.getPageSize());

        IPage<PAir> newsPage = pAirMapper.page(page, pAir);
        return new PageResultUtils<PAir>((int) newsPage.getTotal(), (int) newsPage.getSize(), (int) newsPage.getPages(), (int) newsPage.getCurrent(), newsPage.getRecords());
    }

    @Override
    public R getInfoById(@RequestBody PAir pAir) {

        PAir data = pAirMapper.getInfoById(pAir);
        if (data!=null) {
            PDetailParameters parameters = new PDetailParameters();
            parameters.setProductId(data.getId());
            parameters.setMainClass(data.getMainClass());
            data.setDetailParameters(detailParametersService.getOneByProductIdAndMainClass(parameters));
        }

        return R.ok(data);
    }
}
