package com.rw.product.utils;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.rw.common.core.exception.ServiceException;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.formula.functions.T;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;

@Slf4j
@Service
public class SyncDataUtil {

    @Value("${spring.profiles.active}")
    private String active;
    private static String env;


    //@Value("${sync.url}")
    private String loadUrl;

    // 请求地址
    private static String url = "";

 //   @PostConstruct
//    public void getUrl() {
//        url = this.loadUrl;
//    }

    @PostConstruct
    public void getEnv() {
        env = this.active;
    }

    public static String test = "/test";

    public static String air_add = "/air/add";
    public static String air_update = "/air/update";

    public static String attr_add = "/attr/add";
    public static String attr_update = "/attr/update";

    public static String eps_add = "/eps/add";
    public static String eps_update = "/eps/update";

    public static String pemonitor_add = "/pemonitor/add";
    public static String pemonitor_update = "/pemonitor/update";

    public static String product_add = "/product/add";
    public static String product_update = "/product/update";

    public static String room_add = "/room/add";
    public static String room_update = "/room/update";

    public static String single_add = "/single/add";
    public static String single_update = "/single/update";

    public static String ups_add = "/ups/add";
    public static String ups_update = "/ups/update";

    public static String label_add = "/label/add";
    public static String label_update = "/label/update";

    public static String boxlist_add = "/boxlist/add";
    public static String boxlist_update = "/boxlist/update";
    public static String boxlist_del = "/boxlist/delBoxlist";

    public static String prolist_add = "/prolist/add";
    public static String prolist_update = "/prolist/update";
    public static String prolist_del = "/prolist/delProlist";


    public <T> Long post(String method, T data) {

        if ("dev".equals(env)) {
            return null;
        }

        try {
            // 同步
            String postResult = HttpUtil.post(url + method, JSONUtil.toJsonStr(data));

            // 如果返回值为空直接异常
            if (postResult == null || postResult.isEmpty()) {
                throw new ServiceException(StrUtil.format("同步{}数据响应值为空", method));
            }


            // 转换为 JSONObject
            JSONObject jsonObject = JSONUtil.parseObj(postResult);


            int code = jsonObject.getInt("code");
            Object responseData = jsonObject.get("data");

            // 根据响应状态进行处理
            if (code != 200 || responseData == null) {
                throw new ServiceException(StrUtil.format("同步{}数据响应码或响应值错误", method));
            }

           return Convert.toLong(responseData);

        } catch (Exception e) {
            // 记录异常信息
            log.error("同步数据失败{},{},{}", method, JSONUtil.toJsonStr(data), e);
        }
        throw new ServiceException(StrUtil.format("同步{}数据失败", method));
    }


    //
    //public <T> T post(String method, T data) {
    //
    //    try {
    //        // 同步
    //        String postResult = HttpUtil.post(url + method, JSONUtil.toJsonStr(data));
    //
    //        // 如果返回值为空直接异常
    //        if (postResult == null || postResult.isEmpty()) {
    //            throw new ServiceException(StrUtil.format("同步{}数据响应值为空", method));
    //        }
    //
    //
    //        // 转换为 JSONObject
    //        JSONObject jsonObject = JSONUtil.parseObj(postResult);
    //
    //
    //        int code = jsonObject.getInt("code");
    //        Object responseData = jsonObject.get("data");
    //
    //        // 根据响应状态进行处理
    //        if (code != 200 || responseData == null) {
    //            throw new ServiceException(StrUtil.format("同步{}数据响应码或响应值错误", method));
    //        }
    //
    //        ObjectMapper objectMapper = new ObjectMapper();
    //        return objectMapper.readValue(JSONUtil.toJsonStr(responseData), (Class<T>) data.getClass());
    //
    //    } catch (Exception e) {
    //        // 记录异常信息
    //        log.error("同步数据失败{},{},{}", method, JSONUtil.toJsonStr(data), e);
    //    }
    //    throw new ServiceException(StrUtil.format("同步{}数据失败", method));
    //}
}