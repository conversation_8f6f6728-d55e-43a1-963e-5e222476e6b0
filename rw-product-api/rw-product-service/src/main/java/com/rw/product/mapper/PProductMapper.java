package com.rw.product.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.rw.product.model.entity.PProduct;
import org.springframework.stereotype.Repository;
/**
 * 配件和安防信息 包含 断路器,柜体,仪,按钮和指示灯,互感器,浪涌保护,铜排,插座,配件,ATS,摄像机管理,录像机管理,交换机管理,传输类管理,隔离开关,负荷开关,接触器,熔断器,继电器,断路器附件,UPS配件管理,安防配件管理Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-07-23
 */
@Repository
public interface PProductMapper extends BaseMapper<PProduct>
{

}
