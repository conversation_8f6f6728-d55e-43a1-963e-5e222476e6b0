package com.rw.product.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.rw.common.core.web.controller.BaseController;
import com.rw.common.core.web.domain.AjaxResult;
import com.rw.common.core.web.page.TableDataInfo;
import com.rw.product.mapper.SProlistDetailsMapper;
import com.rw.product.model.entity.SProlistDetails;
import com.rw.product.service.ISProlistDetailsService;
import com.rw.product.service.UserService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.List;
/**
 * 方案组合详情Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-07-23
 */
@Slf4j
@RestController
@AllArgsConstructor
public class SProlistDetailsServiceImpl extends BaseController implements ISProlistDetailsService
{
    @Autowired
    UserService userService;
    @Autowired
    SProlistDetailsMapper sProlistDetailsMapper;

    /**
     * 查询方案组合详情列表
     */
    @Override
    public TableDataInfo list(SProlistDetails sProlistDetails)
    {
        startPage();
        QueryWrapper<SProlistDetails> queryWrapper = new QueryWrapper<>();
        queryWrapper.setEntity(sProlistDetails);
        queryWrapper.orderByDesc("id");
        List<SProlistDetails> list =sProlistDetailsMapper.selectList(queryWrapper);
        return getDataTable(list);
    }


    /**
     * 获取方案组合详情详细信息
     */
    @Override
    public AjaxResult getInfo(@PathVariable("id") Integer id)
    {
        return AjaxResult.success(sProlistDetailsMapper.selectById(id));
    }

    /**
     * 新增方案组合详情
     */
    @Override
    public AjaxResult add(@RequestBody SProlistDetails sProlistDetails)
    {
        sProlistDetails.setCreateId(userService.getUserId());
        sProlistDetails.setCreateBy(userService.getNickname());
        return toAjax(sProlistDetailsMapper.insert(sProlistDetails));
    }

    /**
     * 修改方案组合详情
     */
    @Override
    public AjaxResult edit(@RequestBody SProlistDetails sProlistDetails)
    {
        sProlistDetails.setUpdateId(userService.getUserId());
        sProlistDetails.setUpdateBy(userService.getNickname());
        return toAjax(sProlistDetailsMapper.updateById(sProlistDetails));
    }

    /**
     * 删除方案组合详情
     */
    @Override
    public AjaxResult remove(@PathVariable Integer[] ids)
    {
        return toAjax(sProlistDetailsMapper.deleteBatchIds(Arrays.asList(ids)));
    }
}
