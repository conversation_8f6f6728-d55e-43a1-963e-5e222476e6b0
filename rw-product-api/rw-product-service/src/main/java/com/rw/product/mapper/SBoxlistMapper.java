package com.rw.product.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rw.product.model.entity.PAir;
import com.rw.product.model.entity.PEps;
import com.rw.product.model.entity.SBoxlist;
import com.rw.product.model.entity.SBoxlistDetail;
import com.rw.product.model.req.BoxList10ByProductIdReq;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 方案 包含 配电柜,UPS,空调,安防,EPS,动环Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-07-23
 */
@Repository
public interface SBoxlistMapper extends BaseMapper<SBoxlist>
{


    List<SBoxlist> getBoxList10ByProductId(@Param("req")BoxList10ByProductIdReq req);


    IPage<SBoxlist> page(Page page, @Param("req") SBoxlist sBoxlist);

    SBoxlist getInfoById(@Param("req") SBoxlist sBoxlist);
}
