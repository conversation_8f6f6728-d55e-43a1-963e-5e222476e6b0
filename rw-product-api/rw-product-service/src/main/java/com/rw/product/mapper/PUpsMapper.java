package com.rw.product.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rw.product.model.entity.PUps;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
/**
 * UPS信息 包含 UPS管理 电池管理  电池柜管理 电池线管理Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-07-23
 */
@Repository
public interface PUpsMapper extends BaseMapper<PUps>
{

    IPage<PUps> page(Page page, @Param("req") PUps req);
    PUps getInfoById(@Param("req") PUps req);
}
