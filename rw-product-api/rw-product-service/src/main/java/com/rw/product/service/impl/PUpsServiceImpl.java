package com.rw.product.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rw.common.core.domain.R;
import com.rw.common.core.utils.PageResultUtils;
import com.rw.common.core.web.controller.BaseController;
import com.rw.common.core.web.domain.AjaxResult;
import com.rw.common.core.web.page.TableDataInfo;
import com.rw.product.mapper.PAttrMapper;
import com.rw.product.mapper.PUpsMapper;
import com.rw.product.model.entity.PAttr;
import com.rw.product.model.entity.PDetailParameters;
import com.rw.product.model.entity.PUps;
import com.rw.product.service.IPUpsService;
import com.rw.product.service.UserService;
import com.rw.product.utils.SyncDataUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * UPS信息 包含 UPS管理 电池管理  电池柜管理 电池线管理Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-07-23
 */
@Slf4j
@RestController
@AllArgsConstructor
public class PUpsServiceImpl extends BaseController implements IPUpsService
{
    @Autowired
    UserService userService;
    @Autowired
    PUpsMapper pUpsMapper;
    @Autowired
    PAttrMapper attrMapper;

    @Autowired
    SyncDataUtil syncDataUtil;

    @Autowired
    PDetailParametersServiceImpl detailParametersService;

    /**
     * 查询UPS信息 包含 UPS管理 电池管理  电池柜管理 电池线管理列表
     */
    @Override
    public TableDataInfo list(PUps pUps)
    {
        startPage();
        QueryWrapper<PUps> queryWrapper = new QueryWrapper<>();
        queryWrapper.setEntity(pUps);
        queryWrapper.orderByDesc("id");
        List<PUps> list =pUpsMapper.selectList(queryWrapper);


        fillAttrTitle(list);


        return getDataTable(list);
    }


    /**
     * 组装属性名称
     */
    private void fillAttrTitle(List<PUps> list ){

        if (CollUtil.isEmpty(list)) {
            return;
        }

        List<Integer> attrIdList=new ArrayList<>();
        //取出所有属性ID
        for (PUps item : list) {
            if (item.getBrand()!=null && item.getBrand()>0) {
                attrIdList.add(item.getBrand());
            }
            if (item.getSeries()!=null && item.getSeries()>0) {
                attrIdList.add(item.getSeries());
            }
            if (item.getCapacity()!=null && item.getCapacity()>0) {
                attrIdList.add(item.getCapacity());
            }
            if (item.getVoltage()!=null && item.getVoltage()>0) {
                attrIdList.add(item.getVoltage());
            }
            if (item.getVoltageSort()!=null && item.getVoltageSort()>0) {
                attrIdList.add(item.getVoltageSort());
            }
            if (item.getBatteryType()!=null && item.getBatteryType()>0) {
                attrIdList.add(item.getBatteryType());
            }
            if (item.getBatteryForm()!=null && item.getBatteryForm()>0) {
                attrIdList.add(item.getBatteryForm());
            }
            if (item.getLineDiameter()!=null && item.getLineDiameter()>0) {
                attrIdList.add(item.getLineDiameter());
            }
            if (item.getHourRate()!=null && item.getHourRate()>0) {
                attrIdList.add(item.getHourRate());
            }
            if (item.getStructure()!=null && item.getStructure()>0) {
                attrIdList.add(item.getStructure());
            }
        }


        if (CollUtil.isEmpty(attrIdList)) {
            return;
        }
        //去重
        attrIdList = attrIdList.stream().distinct().collect(Collectors.toList());

        //根据ID批量获取属性
        List<PAttr> allAttr = attrMapper.selectBatchIds(attrIdList);

        //转换为Map
        Map<Integer, PAttr> attrMap = allAttr.stream().collect(Collectors.toMap(x -> x.getId(), x -> x));

        //循环赋值
        for (PUps item : list) {
            if (item.getBrand()!=null && item.getBrand()>0) {
                if (attrMap.containsKey(item.getBrand())) {
                    item.setBrandTitle(attrMap.get(item.getBrand()).getTitle());
                }
            }
            if (item.getSeries()!=null && item.getSeries()>0) {
                if (attrMap.containsKey(item.getSeries())) {
                    item.setSeriesTitle(attrMap.get(item.getSeries()).getTitle());
                }
            }

            if (item.getCapacity()!=null && item.getCapacity()>0) {
                if (attrMap.containsKey(item.getCapacity())) {
                    item.setCapacityTitle(attrMap.get(item.getCapacity()).getTitle());
                }
            }
            if (item.getVoltageSort()!=null && item.getVoltageSort()>0) {
                if (attrMap.containsKey(item.getVoltageSort())) {
                    item.setVoltageSortTitle(attrMap.get(item.getVoltageSort()).getTitle());
                }
            }

            if (item.getVoltage()!=null && item.getVoltage()>0) {
                if (attrMap.containsKey(item.getVoltage())) {
                    item.setVoltageTitle(attrMap.get(item.getVoltage()).getTitle());
                }
            }

            if (item.getBatteryType()!=null && item.getBatteryType()>0) {
                if (attrMap.containsKey(item.getBatteryType())) {
                    item.setBatteryTypeTitle(attrMap.get(item.getBatteryType()).getTitle());
                }
            }
            if (item.getBatteryForm()!=null && item.getBatteryForm()>0) {
                if (attrMap.containsKey(item.getBatteryForm())) {
                    item.setBatteryFormTitle(attrMap.get(item.getBatteryForm()).getTitle());
                }
            }
            if (item.getLineDiameter()!=null && item.getLineDiameter()>0) {
                if (attrMap.containsKey(item.getLineDiameter())) {
                    item.setLineDiameterTitle(attrMap.get(item.getLineDiameter()).getTitle());
                }
            }
            if (item.getHourRate()!=null && item.getHourRate()>0) {
                if (attrMap.containsKey(item.getHourRate())) {
                    item.setHourRateTitle(attrMap.get(item.getHourRate()).getTitle());
                }
            }
            if (item.getStructure()!=null && item.getStructure()>0) {
                if (attrMap.containsKey(item.getStructure())) {
                    item.setStructureTitle(attrMap.get(item.getStructure()).getTitle());
                }
            }
        }


    }




    /**
     * 获取UPS信息 包含 UPS管理 电池管理  电池柜管理 电池线管理详细信息
     */
    @Override
    public AjaxResult getInfo(@PathVariable("id") Integer id)
    {
        PUps data = pUpsMapper.selectById(id);
        if (data!=null) {
            PDetailParameters parameters = new PDetailParameters();
            parameters.setProductId(data.getId());
            parameters.setMainClass(data.getMainClass());
            data.setDetailParameters(detailParametersService.getOneByProductIdAndMainClass(parameters));
        }
        return AjaxResult.success(data);
    }

    /**
     * 新增UPS信息 包含 UPS管理 电池管理  电池柜管理 电池线管理
     */
    @Override
    public AjaxResult add(@RequestBody PUps pUps)
    {
        pUps.setCreateId(userService.getUserId());
        pUps.setCreateBy(userService.getNickname());

        //同步数据
        pUps.setId(Convert.toInt(syncDataUtil.post(SyncDataUtil.ups_add, pUps)));


        int result = pUpsMapper.insert(pUps);
        if (result>0) {
            if(pUps.getDetailParameters()==null){
                pUps.setDetailParameters(new PDetailParameters());
            }
            pUps.getDetailParameters().setProductId(pUps.getId());
            pUps.getDetailParameters().setMainClass(pUps.getMainClass());
            detailParametersService.addDetailParameters(pUps.getDetailParameters());
        }

        return toAjax(result);
    }

    /**
     * 修改UPS信息 包含 UPS管理 电池管理  电池柜管理 电池线管理
     */
    @Override
    public AjaxResult edit(@RequestBody PUps pUps)
    {
        pUps.setUpdateId(userService.getUserId());
        pUps.setUpdateBy(userService.getNickname());
        //同步数据
        syncDataUtil.post(SyncDataUtil.ups_update, pUps);


        int result = pUpsMapper.updateById(pUps);
        if (result>0) {
            pUps.getDetailParameters().setProductId(pUps.getId());
            pUps.getDetailParameters().setMainClass(pUps.getMainClass());

            detailParametersService.addDetailParameters(pUps.getDetailParameters());
        }

        return toAjax(result);

    }

    /**
     * 删除UPS信息 包含 UPS管理 电池管理  电池柜管理 电池线管理
     */
    @Override
    public AjaxResult remove(@PathVariable Integer[] ids)
    {
        return toAjax(pUpsMapper.deleteBatchIds(Arrays.asList(ids)));
    }

    @Override
    public AjaxResult updateIsShow(@RequestBody PUps pUps) {

        PUps syncModel = pUpsMapper.selectById(pUps.getId());
        syncModel.setUpdateId(userService.getUserId());
        syncModel.setUpdateBy(userService.getNickname());
        syncModel.setIsShow(pUps.getIsShow());

        //同步数据
        syncDataUtil.post(SyncDataUtil.ups_update, syncModel);


        QueryWrapper<PUps> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("id", syncModel.getId());

        PUps model = new PUps();
        model.setIsShow(syncModel.getIsShow());
        model.setUpdateId(syncModel.getUpdateId());
        model.setUpdateBy(syncModel.getUpdateBy());

        return toAjax( pUpsMapper.update(model,queryWrapper));
    }

    @Override
    public PageResultUtils<PUps> page(@RequestBody PUps pUps) {
        Page page = new Page();
        page.setCurrent(pUps.getPageNum());
        page.setSize(pUps.getPageSize());
        IPage<PUps> newsPage = pUpsMapper.page(page, pUps);
        return new PageResultUtils<PUps>((int) newsPage.getTotal(), (int) newsPage.getSize(), (int) newsPage.getPages(), (int) newsPage.getCurrent(), newsPage.getRecords());
    }

    @Override
    public R getInfoById(@RequestBody PUps pUps) {

        PUps data = pUpsMapper.getInfoById(pUps);
        if (data!=null) {
            PDetailParameters parameters = new PDetailParameters();
            parameters.setProductId(data.getId());
            parameters.setMainClass(data.getMainClass());
            data.setDetailParameters(detailParametersService.getOneByProductIdAndMainClass(parameters));
        }

        return R.ok(data);
    }
}
