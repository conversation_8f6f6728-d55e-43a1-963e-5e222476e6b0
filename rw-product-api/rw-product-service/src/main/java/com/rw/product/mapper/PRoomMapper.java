package com.rw.product.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rw.product.model.entity.PRoom;
import com.rw.product.model.entity.PSingle;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
/**
 * 整体机房管理 包含 STS管理 PDU管理Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-07-23
 */
@Repository
public interface PRoomMapper extends BaseMapper<PRoom>
{
    IPage<PRoom> page(Page page, @Param("req") PRoom pRoom);
    PRoom getInfoById(@Param("req") PRoom pRoom);
}
