package com.rw.product.utils;

import cn.hutool.core.convert.Convert;
import com.rw.common.redis.service.RedisService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class SerialNumberUtil {

    private String  boxlist_no_key="boxlist_no";

    private String  prolist_no_key="prolist_no";


    /**
     * @Fields rm : Redis的业务 数据输入输出
     */
    @Autowired
    private RedisService rm;


    public Long getBoxlistNo(){
        int init=1000000;
        Object boxlistNo = rm.getCacheObject(boxlist_no_key);
        if(boxlistNo==null || Convert.toInt(boxlistNo)<init){
            rm.setCacheObject(boxlist_no_key,init);
        }
        return rm.incr(boxlist_no_key, 1L);
    }

    public Long getProlistNo(){
        int init=10000;
        Object prolistNo = rm.getCacheObject(prolist_no_key);
        if(prolistNo==null || Convert.toInt(prolistNo)<init){
            rm.setCacheObject(prolist_no_key,init);
        }
        return rm.incr(prolist_no_key, 1L);
    }

}
