package com.rw.product.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rw.product.model.entity.PAir;
import com.rw.product.model.entity.PRoom;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
/**
 * 空调 包含 室外机管理,室内机管理,气液管管理,水管管理,制冷剂管理,空调配件管理Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-07-23
 */
@Repository
public interface PAirMapper extends BaseMapper<PAir>
{

    IPage<PAir> page(Page page, @Param("req") PAir pAir);

    PAir getInfoById(@Param("req") PAir pAir);
}
