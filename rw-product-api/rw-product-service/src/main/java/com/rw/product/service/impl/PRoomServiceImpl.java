package com.rw.product.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rw.common.core.domain.R;
import com.rw.common.core.utils.PageResultUtils;
import com.rw.common.core.web.controller.BaseController;
import com.rw.common.core.web.domain.AjaxResult;
import com.rw.common.core.web.page.TableDataInfo;
import com.rw.product.mapper.PAttrMapper;
import com.rw.product.mapper.PDetailParametersMapper;
import com.rw.product.mapper.PRoomMapper;
import com.rw.product.model.entity.*;
import com.rw.product.service.IPRoomService;
import com.rw.product.service.UserService;
import com.rw.product.utils.SyncDataUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 整体机房管理 包含 STS管理 PDU管理Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-07-23
 */
@Slf4j
@RestController
@AllArgsConstructor
public class PRoomServiceImpl extends BaseController implements IPRoomService
{
    @Autowired
    UserService userService;
    @Autowired
    PRoomMapper pRoomMapper;
    @Autowired
    PAttrMapper attrMapper;


    @Autowired
    SyncDataUtil syncDataUtil;


    @Autowired
    PDetailParametersServiceImpl detailParametersService;

    /**
     * 查询整体机房管理 包含 STS管理 PDU管理列表
     */
    @Override
    public TableDataInfo list(PRoom pRoom)
    {
        startPage();
        QueryWrapper<PRoom> queryWrapper = new QueryWrapper<>();
        queryWrapper.setEntity(pRoom);
        queryWrapper.orderByDesc("id");
        List<PRoom> list =pRoomMapper.selectList(queryWrapper);


        fillAttrTitle(list);

        return getDataTable(list);
    }




    /**
     * 组装属性名称
     */
    private void fillAttrTitle(List<PRoom> list ){

        if (CollUtil.isEmpty(list)) {
            return;
        }

        List<Integer> attrIdList=new ArrayList<>();
        //取出所有属性ID
        for (PRoom item : list) {
            if (item.getBrand()!=null && item.getBrand()>0) {
                attrIdList.add(item.getBrand());
            }
            if (item.getSeries()!=null && item.getSeries()>0) {
                attrIdList.add(item.getSeries());
            }
            if (item.getCapacity()!=null && item.getCapacity()>0) {
                attrIdList.add(item.getCapacity());
            }
            if (item.getClassify()!=null && item.getClassify()>0) {
                attrIdList.add(item.getClassify());
            }
            if (item.getVoltage()!=null && item.getVoltage()>0) {
                attrIdList.add(item.getVoltage());
            }
            if (item.getRoles()!=null && item.getRoles()>0) {
                attrIdList.add(item.getRoles());
            }
        }


        if (CollUtil.isEmpty(attrIdList)) {
            return;
        }
        //去重
        attrIdList = attrIdList.stream().distinct().collect(Collectors.toList());

        //根据ID批量获取属性
        List<PAttr> allAttr = attrMapper.selectBatchIds(attrIdList);

        //转换为Map
        Map<Integer, PAttr> attrMap = allAttr.stream().collect(Collectors.toMap(x -> x.getId(), x -> x));

        //循环赋值
        for (PRoom item : list) {
            if (item.getBrand()!=null && item.getBrand()>0) {
                if (attrMap.containsKey(item.getBrand())) {
                    item.setBrandTitle(attrMap.get(item.getBrand()).getTitle());
                }
            }
            if (item.getSeries()!=null && item.getSeries()>0) {
                if (attrMap.containsKey(item.getSeries())) {
                    item.setSeriesTitle(attrMap.get(item.getSeries()).getTitle());
                }
            }

            if (item.getCapacity()!=null && item.getCapacity()>0) {
                if (attrMap.containsKey(item.getCapacity())) {
                    item.setCapacityTitle(attrMap.get(item.getCapacity()).getTitle());
                }
            }

            if (item.getClassify()!=null && item.getClassify()>0) {
                if (attrMap.containsKey(item.getClassify())) {
                    item.setClassifyTitle(attrMap.get(item.getClassify()).getTitle());
                }
            }

            if (item.getVoltage()!=null && item.getVoltage()>0) {
                if (attrMap.containsKey(item.getVoltage())) {
                    item.setVoltageTitle(attrMap.get(item.getVoltage()).getTitle());
                }
            }

            if (item.getRoles()!=null && item.getRoles()>0) {
                if (attrMap.containsKey(item.getRoles())) {
                    item.setRolesTitle(attrMap.get(item.getRoles()).getTitle());
                }
            }

        }


    }

    /**
     * 获取整体机房管理 包含 STS管理 PDU管理详细信息
     */
    @Override
    public AjaxResult getInfo(@PathVariable("id") Integer id)
    {
        PRoom data = pRoomMapper.selectById(id);
        if (data!=null) {
            PDetailParameters parameters = new PDetailParameters();
            parameters.setProductId(data.getId());
            parameters.setMainClass(data.getMainClass());
            data.setDetailParameters(detailParametersService.getOneByProductIdAndMainClass(parameters));
        }
        return AjaxResult.success(data);
    }

    /**
     * 新增整体机房管理 包含 STS管理 PDU管理
     */
    @Override
    public AjaxResult add(@RequestBody PRoom pRoom)
    {
        pRoom.setCreateId(userService.getUserId());
        pRoom.setCreateBy(userService.getNickname());

        //同步数据
        pRoom.setId(Convert.toInt(syncDataUtil.post(SyncDataUtil.room_add, pRoom)));

        int result = pRoomMapper.insert(pRoom);
        if (result>0) {
            if(pRoom.getDetailParameters()==null){
                pRoom.setDetailParameters(new PDetailParameters());
            }

            pRoom.getDetailParameters().setProductId(pRoom.getId());
            pRoom.getDetailParameters().setMainClass(pRoom.getMainClass());

            detailParametersService.addDetailParameters(pRoom.getDetailParameters());
        }

        return toAjax(result);
    }

    /**
     * 修改整体机房管理 包含 STS管理 PDU管理
     */
    @Override
    public AjaxResult edit(@RequestBody PRoom pRoom)
    {
        pRoom.setUpdateId(userService.getUserId());
        pRoom.setUpdateBy(userService.getNickname());


        //同步数据
        syncDataUtil.post(SyncDataUtil.room_update, pRoom);

        int result = pRoomMapper.updateById(pRoom);
        if (result>0) {
            pRoom.getDetailParameters().setProductId(pRoom.getId());
            pRoom.getDetailParameters().setMainClass(pRoom.getMainClass());


            detailParametersService.addDetailParameters(pRoom.getDetailParameters());
        }

        return toAjax(result);
    }

    /**
     * 删除整体机房管理 包含 STS管理 PDU管理
     */
    @Override
    public AjaxResult remove(@PathVariable Integer[] ids)
    {
        return toAjax(pRoomMapper.deleteBatchIds(Arrays.asList(ids)));
    }

    @Override
    public AjaxResult updateIsShow(@RequestBody PRoom pRoom) {

        PRoom syncModel = pRoomMapper.selectById(pRoom.getId());
        syncModel.setUpdateId(userService.getUserId());
        syncModel.setUpdateBy(userService.getNickname());
        syncModel.setIsShow(pRoom.getIsShow());
        //同步数据
        syncDataUtil.post(SyncDataUtil.ups_update, syncModel);


        QueryWrapper<PRoom> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("id", syncModel.getId());

        PRoom model = new PRoom();
        model.setIsShow(syncModel.getIsShow());
        model.setUpdateId(syncModel.getUpdateId());
        model.setUpdateBy(syncModel.getUpdateBy());

        return toAjax( pRoomMapper.update(model,queryWrapper));
    }

    @Override
    public PageResultUtils<PRoom> page(@RequestBody PRoom pRoom) {
        Page page = new Page();
        page.setCurrent(pRoom.getPageNum());
        page.setSize(pRoom.getPageSize());
        IPage<PRoom> newsPage = pRoomMapper.page(page, pRoom);
        return new PageResultUtils<PRoom>((int) newsPage.getTotal(), (int) newsPage.getSize(), (int) newsPage.getPages(), (int) newsPage.getCurrent(), newsPage.getRecords());
    }

    @Override
    public R getInfoById(@RequestBody PRoom pRoom) {
        PRoom data = pRoomMapper.getInfoById(pRoom);
        if (data!=null) {
            PDetailParameters parameters = new PDetailParameters();
            parameters.setProductId(data.getId());
            parameters.setMainClass(data.getMainClass());
            data.setDetailParameters(detailParametersService.getOneByProductIdAndMainClass(parameters));
        }

        return R.ok(data);
    }
}
