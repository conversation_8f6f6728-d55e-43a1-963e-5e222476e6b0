package com.rw.product.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.rw.common.core.domain.R;
import com.rw.common.core.web.controller.BaseController;
import com.rw.common.core.web.domain.AjaxResult;
import com.rw.common.core.web.page.TableDataInfo;
import com.rw.product.mapper.SLabelMapper;
import com.rw.product.model.entity.PAttr;
import com.rw.product.model.entity.PEps;
import com.rw.product.model.entity.SLabel;
import com.rw.product.service.ISLabelService;
import com.rw.product.service.UserService;
import com.rw.product.utils.ProgramConditionsUtil;
import com.rw.product.utils.SyncDataUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.List;
/**
 * 方案标签Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-07-23
 */
@Slf4j
@RestController
@AllArgsConstructor
public class SLabelServiceImpl extends BaseController implements ISLabelService
{
    @Autowired
    UserService userService;
    @Autowired
    SLabelMapper sLabelMapper;

    @Autowired
    ProgramConditionsUtil programConditionsUtil;


    @Autowired
    SyncDataUtil syncDataUtil;

    /**
     * 查询列
     * @param language
     * @return
     */
    private String getColumns(String language){
        String columns="id, name, classsify_id, label_class, sort, is_show, hits, del_flag, create_id, create_by,create_time, update_id, update_by, update_time, remark";
        if ("en".equals(language)) {
            columns = "id, name_en as name, classsify_id, label_class, sort, is_show, hits, del_flag, create_id, create_by,create_time, update_id, update_by, update_time, remark";
        }
        return columns;
    }


    /**
     * 查询方案标签列表
     */
    @Override
    public AjaxResult list(SLabel sLabel)
    {
        QueryWrapper<SLabel> queryWrapper = new QueryWrapper<>();
        queryWrapper.setEntity(sLabel);
        queryWrapper.orderByDesc("id");
        List<SLabel> list =sLabelMapper.selectList(queryWrapper);
        return AjaxResult.success(list);
    }


    /**
     * 获取方案标签详细信息
     */
    @Override
    public AjaxResult getInfo(@PathVariable("id") Integer id)
    {
        return AjaxResult.success(sLabelMapper.selectById(id));
    }

    /**
     * 新增方案标签
     */
    @Override
    public AjaxResult add(@RequestBody SLabel sLabel)
    {
        sLabel.setCreateId(userService.getUserId());
        sLabel.setCreateBy(userService.getNickname());

        //同步数据
        sLabel.setId(Convert.toInt(syncDataUtil.post(SyncDataUtil.label_add, sLabel)));

        return toAjax(sLabelMapper.insert(sLabel));
    }

    /**
     * 修改方案标签
     */
    @Override
    public AjaxResult edit(@RequestBody SLabel sLabel)
    {
        sLabel.setUpdateId(userService.getUserId());
        sLabel.setUpdateBy(userService.getNickname());


        //同步数据
        syncDataUtil.post(SyncDataUtil.label_update, sLabel);

        return toAjax(sLabelMapper.updateById(sLabel));
    }

    /**
     * 删除方案标签
     */
    @Override
    public AjaxResult remove(@PathVariable Integer[] ids)
    {
        return toAjax(sLabelMapper.deleteBatchIds(Arrays.asList(ids)));
    }

    @Override
    public R getLabelList(@RequestBody SLabel sLabel) {
        QueryWrapper<SLabel> queryWrapper = new QueryWrapper<>();
        queryWrapper.select(getColumns(sLabel.getLanguage()));

        if (sLabel.getClasssifyId()!=null) {
            queryWrapper.eq("classsify_id", sLabel.getClasssifyId());
        }
        if (sLabel.getLabelClass()!=null) {
            queryWrapper.eq("label_class", sLabel.getLabelClass());
        }
        if (sLabel.getIsShow()!=null) {
            queryWrapper.eq("is_show", sLabel.getIsShow());
        }
        queryWrapper.orderByAsc("sort").orderByDesc("create_time");
        return R.ok(sLabelMapper.selectList(queryWrapper));
    }

    @Override
    public AjaxResult getAllLabelByClassif(Integer classif) {
        return AjaxResult.success(programConditionsUtil.getProgrammeLabel(classif));
    }

    @Override
    public AjaxResult getLabelDistinctByClassif(Integer classif) {

        List<String> list = sLabelMapper.getClasssifyIdByLabelClass(classif);

        return AjaxResult.success(list);
    }


    /**
     * 根据ID批量查询标签
     * @param ids
     * @param language
     * @return
     */
    public List<SLabel> getBatchLabelById(List<Integer> ids,String language) {
        ids=CollUtil.distinct(ids);
        QueryWrapper<SLabel> queryWrapper = new QueryWrapper<>();
        queryWrapper.select(getColumns(language));
        queryWrapper.in("id",ids);
        return sLabelMapper.selectList(queryWrapper);
    }

}
