package com.rw.product.service;

import com.rw.product.model.Constant;
import com.rw.product.model.entity.SBoxlistDetail;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import com.rw.common.log.annotation.Log;
import com.rw.common.log.enums.BusinessType;
import com.rw.common.core.web.page.TableDataInfo;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import com.rw.common.core.web.domain.AjaxResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * 方案详情接口
 * 
 * <AUTHOR>
 * @date 2024-07-23
 */
@Api(tags = "方案详情:SBoxlistDetail")
@FeignClient(Constant.APPLICATION_NAME)
public interface ISBoxlistDetailService {

    @ApiOperation(value = "查询方案详情列表", response = AjaxResult.class)
    @GetMapping("detail/list")
    TableDataInfo list(SBoxlistDetail sBoxlistDetail);


    @ApiOperation(value = "获取方案详情详细信息", response = AjaxResult.class)
    @GetMapping(value = "detail/{id}")
    AjaxResult getInfo(@PathVariable(value = "id") Integer id);

    @ApiOperation(value = "新增方案详情", response = AjaxResult.class)
    @Log(title = "方案详情", businessType = BusinessType.INSERT)
    @PostMapping(value = "detail")
    AjaxResult add(@RequestBody SBoxlistDetail sBoxlistDetail);

    @ApiOperation(value = "修改方案详情", response = AjaxResult.class)
    @Log(title = "方案详情", businessType = BusinessType.UPDATE)
    @PutMapping(value = "detail")
    AjaxResult edit(@RequestBody SBoxlistDetail sBoxlistDetail);

    @ApiOperation(value = "删除方案详情", response = AjaxResult.class)
    @Log(title = "方案详情", businessType = BusinessType.DELETE)
    @DeleteMapping("detail/{ids}")
    AjaxResult remove(@PathVariable(value = "ids") Integer[] ids);
}
