package com.rw.product.service;

import com.rw.common.core.domain.R;
import com.rw.product.model.Constant;
import com.rw.product.model.entity.PAir;
import com.rw.product.model.entity.PEps;
import com.rw.product.model.entity.PPemonitor;
import com.rw.product.model.entity.PRoom;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import com.rw.common.log.annotation.Log;
import com.rw.common.log.enums.BusinessType;
import com.rw.common.core.web.page.TableDataInfo;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import com.rw.common.core.web.domain.AjaxResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * 动环产品 包含 监控中心,配电系统,UPS系统,空调系统,漏水系统,温湿度检测,视频监控,门禁系统,消防系统接口
 * 
 * <AUTHOR>
 * @date 2024-07-23
 */
@Api(tags = "动环产品 包含 监控中心,配电系统,UPS系统,空调系统,漏水系统,温湿度检测,视频监控,门禁系统,消防系统:PPemonitor")
@FeignClient(Constant.APPLICATION_NAME)
public interface IPPemonitorService {

    @ApiOperation(value = "查询动环产品 包含 监控中心,配电系统,UPS系统,空调系统,漏水系统,温湿度检测,视频监控,门禁系统,消防系统列表", response = AjaxResult.class)
    @GetMapping("pemonitor/list")
    TableDataInfo list(PPemonitor pPemonitor);


    @ApiOperation(value = "获取动环产品 包含 监控中心,配电系统,UPS系统,空调系统,漏水系统,温湿度检测,视频监控,门禁系统,消防系统详细信息", response = AjaxResult.class)
    @GetMapping(value = "pemonitor/{id}")
    AjaxResult getInfo(@PathVariable(value = "id") Integer id);

    @ApiOperation(value = "新增动环产品 包含 监控中心,配电系统,UPS系统,空调系统,漏水系统,温湿度检测,视频监控,门禁系统,消防系统", response = AjaxResult.class)
    @Log(title = "动环产品 包含 监控中心,配电系统,UPS系统,空调系统,漏水系统,温湿度检测,视频监控,门禁系统,消防系统", businessType = BusinessType.INSERT)
    @PostMapping(value = "pemonitor")
    AjaxResult add(@RequestBody PPemonitor pPemonitor);

    @ApiOperation(value = "修改动环产品 包含 监控中心,配电系统,UPS系统,空调系统,漏水系统,温湿度检测,视频监控,门禁系统,消防系统", response = AjaxResult.class)
    @Log(title = "动环产品 包含 监控中心,配电系统,UPS系统,空调系统,漏水系统,温湿度检测,视频监控,门禁系统,消防系统", businessType = BusinessType.UPDATE)
    @PutMapping(value = "pemonitor")
    AjaxResult edit(@RequestBody PPemonitor pPemonitor);

    @ApiOperation(value = "删除动环产品 包含 监控中心,配电系统,UPS系统,空调系统,漏水系统,温湿度检测,视频监控,门禁系统,消防系统", response = AjaxResult.class)
    @Log(title = "动环产品 包含 监控中心,配电系统,UPS系统,空调系统,漏水系统,温湿度检测,视频监控,门禁系统,消防系统", businessType = BusinessType.DELETE)
    @DeleteMapping("pemonitor/{ids}")
    AjaxResult remove(@PathVariable(value = "ids") Integer[] ids);

    @ApiOperation(value = "修改isshow状态", response = AjaxResult.class)
    @Log(title = "修改isshow状态", businessType = BusinessType.UPDATE)
    @PostMapping(value = "pemonitor/updateIsShow")
    AjaxResult updateIsShow(@RequestBody PPemonitor pPemonitor);



    @ApiOperation(value = "分页查询", response = R.class)
    @PostMapping(value = "pemonitor/page")
    R page(@RequestBody PPemonitor pPemonitor);



    @ApiOperation(value = "查询详情", response = R.class)
    @PostMapping(value = "pemonitor/getInfoById")
    R getInfoById(@RequestBody PPemonitor pPemonitor);
}
