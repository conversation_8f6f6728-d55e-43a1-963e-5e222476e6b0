package com.rw.product.service;

import com.rw.product.model.Constant;
import com.rw.product.model.entity.PAir;
import com.rw.product.model.entity.PProduct;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import com.rw.common.log.annotation.Log;
import com.rw.common.log.enums.BusinessType;
import com.rw.common.core.web.page.TableDataInfo;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import com.rw.common.core.web.domain.AjaxResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * 配件和安防信息 包含 断路器,柜体,仪,按钮和指示灯,互感器,浪涌保护,铜排,插座,配件,ATS,摄像机管理,录像机管理,交换机管理,传输类管理,隔离开关,负荷开关,接触器,熔断器,继电器,断路器附件,UPS配件管理,安防配件管理接口
 * 
 * <AUTHOR>
 * @date 2024-07-23
 */
@Api(tags = "配件和安防信息 包含 断路器,柜体,仪,按钮和指示灯,互感器,浪涌保护,铜排,插座,配件,ATS,摄像机管理,录像机管理,交换机管理,传输类管理,隔离开关,负荷开关,接触器,熔断器,继电器,断路器附件,UPS配件管理,安防配件管理:PProduct")
@FeignClient(Constant.APPLICATION_NAME)
public interface IPProductService {

    @ApiOperation(value = "查询配件和安防信息 包含 断路器,柜体,仪,按钮和指示灯,互感器,浪涌保护,铜排,插座,配件,ATS,摄像机管理,录像机管理,交换机管理,传输类管理,隔离开关,负荷开关,接触器,熔断器,继电器,断路器附件,UPS配件管理,安防配件管理列表", response = AjaxResult.class)
    @GetMapping("product/list")
    TableDataInfo list(PProduct pProduct);


    @ApiOperation(value = "获取配件和安防信息 包含 断路器,柜体,仪,按钮和指示灯,互感器,浪涌保护,铜排,插座,配件,ATS,摄像机管理,录像机管理,交换机管理,传输类管理,隔离开关,负荷开关,接触器,熔断器,继电器,断路器附件,UPS配件管理,安防配件管理详细信息", response = AjaxResult.class)
    @GetMapping(value = "product/{id}")
    AjaxResult getInfo(@PathVariable(value = "id") Integer id);

    @ApiOperation(value = "新增配件和安防信息 包含 断路器,柜体,仪,按钮和指示灯,互感器,浪涌保护,铜排,插座,配件,ATS,摄像机管理,录像机管理,交换机管理,传输类管理,隔离开关,负荷开关,接触器,熔断器,继电器,断路器附件,UPS配件管理,安防配件管理", response = AjaxResult.class)
    @Log(title = "配件和安防信息 包含 断路器,柜体,仪,按钮和指示灯,互感器,浪涌保护,铜排,插座,配件,ATS,摄像机管理,录像机管理,交换机管理,传输类管理,隔离开关,负荷开关,接触器,熔断器,继电器,断路器附件,UPS配件管理,安防配件管理", businessType = BusinessType.INSERT)
    @PostMapping(value = "product")
    AjaxResult add(@RequestBody PProduct pProduct);

    @ApiOperation(value = "修改配件和安防信息 包含 断路器,柜体,仪,按钮和指示灯,互感器,浪涌保护,铜排,插座,配件,ATS,摄像机管理,录像机管理,交换机管理,传输类管理,隔离开关,负荷开关,接触器,熔断器,继电器,断路器附件,UPS配件管理,安防配件管理", response = AjaxResult.class)
    @Log(title = "配件和安防信息 包含 断路器,柜体,仪,按钮和指示灯,互感器,浪涌保护,铜排,插座,配件,ATS,摄像机管理,录像机管理,交换机管理,传输类管理,隔离开关,负荷开关,接触器,熔断器,继电器,断路器附件,UPS配件管理,安防配件管理", businessType = BusinessType.UPDATE)
    @PutMapping(value = "product")
    AjaxResult edit(@RequestBody PProduct pProduct);

    @ApiOperation(value = "删除配件和安防信息 包含 断路器,柜体,仪,按钮和指示灯,互感器,浪涌保护,铜排,插座,配件,ATS,摄像机管理,录像机管理,交换机管理,传输类管理,隔离开关,负荷开关,接触器,熔断器,继电器,断路器附件,UPS配件管理,安防配件管理", response = AjaxResult.class)
    @Log(title = "配件和安防信息 包含 断路器,柜体,仪,按钮和指示灯,互感器,浪涌保护,铜排,插座,配件,ATS,摄像机管理,录像机管理,交换机管理,传输类管理,隔离开关,负荷开关,接触器,熔断器,继电器,断路器附件,UPS配件管理,安防配件管理", businessType = BusinessType.DELETE)
    @DeleteMapping("product/{ids}")
    AjaxResult remove(@PathVariable(value = "ids") Integer[] ids);


    @ApiOperation(value = "修改isshow状态", response = AjaxResult.class)
    @Log(title = "修改isshow状态", businessType = BusinessType.UPDATE)
    @PostMapping(value = "product/updateIsShow")
    AjaxResult updateIsShow(@RequestBody PProduct pProduct);

}
