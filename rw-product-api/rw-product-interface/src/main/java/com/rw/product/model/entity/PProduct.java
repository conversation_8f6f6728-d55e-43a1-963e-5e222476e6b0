package com.rw.product.model.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.rw.common.core.web.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.math.BigDecimal;
import java.util.Date;
/**
 * 配件和安防信息 包含 断路器,柜体,仪,按钮和指示灯,互感器,浪涌保护,铜排,插座,配件,ATS,摄像机管理,录像机管理,交换机管理,传输类管理,隔离开关,负荷开关,接触器,熔断器,继电器,断路器附件,UPS配件管理,安防配件管理对象 p_product
 *
 * <AUTHOR>
 * @date 2024-07-23
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "配件和安防信息 包含 断路器,柜体,仪,按钮和指示灯,互感器,浪涌保护,铜排,插座,配件,ATS,摄像机管理,录像机管理,交换机管理,传输类管理,隔离开关,负荷开关,接触器,熔断器,继电器,断路器附件,UPS配件管理,安防配件管理")
public class PProduct extends BaseEntity {
    @TableId
    @ApiModelProperty(value = "内部标识")
    private Integer id;
    @ApiModelProperty(value = "名称")
    private String title;
    @ApiModelProperty(value = "名称(英文)")
    private String titleEn;
    @ApiModelProperty(value = "副标题 简述")
    private String subTitle;
    @ApiModelProperty(value = "副标题 简述(英文)")
    private String subTitleEn;
    @ApiModelProperty(value = "类别")
    private Integer mainClass;
    @ApiModelProperty(value = "目录报价")
    private BigDecimal price;
    @ApiModelProperty(value = "零售价格")
    private BigDecimal priceRetail;
    @ApiModelProperty(value = "成本价")
    private BigDecimal priceCb;
    @ApiModelProperty(value = "批发价")
    private BigDecimal priceWholesale;
    @ApiModelProperty(value = "品牌")
    private Integer brand;
    @TableField(exist = false)
    @ApiModelProperty(value = "品牌名称")
    private String brandTitle;
    @ApiModelProperty(value = "系列")
    private Integer series;
    @TableField(exist = false)
    @ApiModelProperty(value = "系列名称")
    private String seriesTitle;
    @ApiModelProperty(value = "接线方式")
    private Integer enviroment;
    @TableField(exist = false)
    @ApiModelProperty(value = "接线方式")
    private String enviromentTitle;
    @ApiModelProperty(value = "安装方式")
    private Integer method;
    @TableField(exist = false)
    @ApiModelProperty(value = "安装方式")
    private String methodTitle;
    @ApiModelProperty(value = "重量")
    private BigDecimal weight;
    @ApiModelProperty(value = "厚度")
    private Integer thickness;
    @TableField(exist = false)
    @ApiModelProperty(value = "厚度")
    private String thicknessTitle;
    @ApiModelProperty(value = "颜色")
    private Integer color;
    @TableField(exist = false)
    @ApiModelProperty(value = "颜色")
    private String colorTitle;
    @ApiModelProperty(value = "宽度")
    private String width;
    @ApiModelProperty(value = "高度")
    private String height;
    @ApiModelProperty(value = "深度")
    private String deep;
    @ApiModelProperty(value = "品类")
    private Integer material;
    @TableField(exist = false)
    @ApiModelProperty(value = "品类")
    private String materialTitle;
    @ApiModelProperty(value = "脱扣类型")
    private Integer structure;
    @TableField(exist = false)
    @ApiModelProperty(value = "脱扣类型")
    private String structureTitle;
    @ApiModelProperty(value = "防护等级")
    private Integer protection;
    @TableField(exist = false)
    @ApiModelProperty(value = "防护等级")
    private String protectionTitle;
    @ApiModelProperty(value = "柜体门")
    private Integer door;
    @TableField(exist = false)
    @ApiModelProperty(value = "柜体门")
    private String doorTitle;
    @ApiModelProperty(value = "通道数量")
    private Integer channels;
    @TableField(exist = false)
    @ApiModelProperty(value = "通道数量")
    private String channelsTitle;
    @ApiModelProperty(value = "仪表功能")
    private Integer fun;
    @TableField(exist = false)
    @ApiModelProperty(value = "仪表功能")
    private String funTitle;
    @ApiModelProperty(value = "监控接口")
    private Integer monitor;
    @TableField(exist = false)
    @ApiModelProperty(value = "监控接口")
    private String monitorTitle;
    @ApiModelProperty(value = "电压类型")
    private Integer voltageSort;
    @TableField(exist = false)
    @ApiModelProperty(value = "电压类型")
    private String voltageSortTitle;
    @ApiModelProperty(value = "电压")
    private Integer voltage;
    @TableField(exist = false)
    @ApiModelProperty(value = "电压")
    private String voltageTitle;
    @ApiModelProperty(value = "操作方式")
    private Integer turns;
    @TableField(exist = false)
    @ApiModelProperty(value = "操作方式")
    private String turnsTitle;
    @ApiModelProperty(value = "漏电电流")
    private Integer circuit;
    @TableField(exist = false)
    @ApiModelProperty(value = "漏电电流")
    private String circuitTitle;
    @ApiModelProperty(value = "级数")
    private Integer poles;
    @TableField(exist = false)
    @ApiModelProperty(value = "级数")
    private String polesTitle;
    @ApiModelProperty(value = "图片")
    private String img;
    @ApiModelProperty(value = "产品图片缩略图")
    private String imgs;
    @ApiModelProperty(value = "产品图片缩略图英文")
    private String imgsEn;
    @ApiModelProperty(value = "脱扣类型")
    private Integer part;
    @TableField(exist = false)
    @ApiModelProperty(value = "脱扣类型")
    private String partTitle;
    @ApiModelProperty(value = "框架电流")
    private Integer kCircuit;
    @TableField(exist = false)
    @ApiModelProperty(value = "框架电流")
    private String kCircuitTitle;
    @ApiModelProperty(value = "空开容量")
    private Integer capacity;
    @TableField(exist = false)
    @ApiModelProperty(value = "空开容量")
    private String capacityTitle;
    @ApiModelProperty(value = "断开能力")
    private Integer ics;
    @TableField(exist = false)
    @ApiModelProperty(value = "断开能力")
    private String icsTitle;
    @ApiModelProperty(value = "极限分断能力")
    private Integer icu;
    @TableField(exist = false)
    @ApiModelProperty(value = "极限分断能力")
    private String icuTitle;
    @ApiModelProperty(value = "空开类型（交直流）")
    private Integer airSwitchType;
    @TableField(exist = false)
    @ApiModelProperty(value = "空开类型（交直流）")
    private String airSwitchTypeTitle;
    @ApiModelProperty(value = "配件类别")
    private Integer accessory;
    @TableField(exist = false)
    @ApiModelProperty(value = "配件类别")
    private String accessoryTitle;
    @ApiModelProperty(value = "关联url")
    private String linkUrl;
    @ApiModelProperty(value = "毛重")
    private BigDecimal fullWeight;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "发布时间")
    private Date releaseTime;
    @ApiModelProperty(value = "生产状态1：在产，2：停产")
    private Integer produceState;
    @ApiModelProperty(value = "订货号")
    private String orderNo;
    @ApiModelProperty(value = "是否显示 1是")
    private Integer isShow;
    @ApiModelProperty(value = "规格（宽高深）")
    private Integer scale;
    @TableField(exist = false)
    @ApiModelProperty(value = "规格（宽高深）")
    private String scaleTitle;
    @ApiModelProperty(value = "???")
    private String itemno;
    @ApiModelProperty(value = "???")
    private String carrying;
    @ApiModelProperty(value = "???")
    private String name;

    @TableLogic
    @ApiModelProperty(value = "删除状态 1已删除 0未删除")
    private Long delFlag;
    @ApiModelProperty(value = "创建人id")
    private Long createId;

    @ApiModelProperty(value = "更新人id")
    private Long updateId;



    @ApiModelProperty(value = "69码")
    private String barcode;

    @ApiModelProperty(value = "直营价")
    private BigDecimal directSalePrice;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "价格更新时间")
    private Date priceUpdateTime;



    @ApiModelProperty(value = "详情参数")
    @TableField(exist = false)
    private PDetailParameters detailParameters;
}
