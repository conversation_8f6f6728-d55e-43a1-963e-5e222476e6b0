package com.rw.product.service;

import com.rw.common.core.domain.R;
import com.rw.product.model.Constant;
import com.rw.product.model.entity.PAttr;
import com.rw.product.model.entity.SLabel;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import com.rw.common.log.annotation.Log;
import com.rw.common.log.enums.BusinessType;
import com.rw.common.core.web.page.TableDataInfo;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import com.rw.common.core.web.domain.AjaxResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * 方案标签接口
 * 
 * <AUTHOR>
 * @date 2024-07-23
 */
@Api(tags = "方案标签:SLabel")
@FeignClient(Constant.APPLICATION_NAME)
public interface ISLabelService {

    @ApiOperation(value = "查询方案标签列表", response = AjaxResult.class)
    @GetMapping("label/list")
    AjaxResult list(SLabel sLabel);


    @ApiOperation(value = "获取方案标签详细信息", response = AjaxResult.class)
    @GetMapping(value = "label/{id}")
    AjaxResult getInfo(@PathVariable(value = "id") Integer id);

    @ApiOperation(value = "新增方案标签", response = AjaxResult.class)
    @Log(title = "方案标签", businessType = BusinessType.INSERT)
    @PostMapping(value = "label")
    AjaxResult add(@RequestBody SLabel sLabel);

    @ApiOperation(value = "修改方案标签", response = AjaxResult.class)
    @Log(title = "方案标签", businessType = BusinessType.UPDATE)
    @PutMapping(value = "label")
    AjaxResult edit(@RequestBody SLabel sLabel);

    @ApiOperation(value = "删除方案标签", response = AjaxResult.class)
    @Log(title = "方案标签", businessType = BusinessType.DELETE)
    @DeleteMapping("label/{ids}")
    AjaxResult remove(@PathVariable(value = "ids") Integer[] ids);


    @ApiOperation(value = "获取属性", response = R.class)
    @PostMapping(value = "label/getLabeList")
    R getLabelList(@RequestBody SLabel sLabel);





    @ApiOperation(value = "根据classif获取所有属性并分组", response = AjaxResult.class)
    @GetMapping(value = "label/getAllLabelByClassif/{classif}")
    AjaxResult getAllLabelByClassif(@PathVariable(value = "classif") Integer classif);



    @ApiOperation(value = "根据方案类型取出当前方案包含的类别ID  如：品牌，容量等", response = AjaxResult.class)
    @GetMapping(value = "label/getLabelDistinctByClassif/{classif}")
    AjaxResult getLabelDistinctByClassif(@PathVariable(value = "classif") Integer classif);





}
