package com.rw.product.service;

import com.rw.common.core.domain.R;
import com.rw.common.core.utils.PageResultUtils;
import com.rw.common.core.web.domain.AjaxResult;
import com.rw.common.core.web.page.TableDataInfo;
import com.rw.common.log.annotation.Log;
import com.rw.common.log.enums.BusinessType;
import com.rw.product.model.Constant;
import com.rw.product.model.entity.PSeriesAttachment;
import com.rw.product.model.entity.PUps;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

/**
 * 系列附件接口
 * 
 * <AUTHOR>
 * @date 2024-08-16
 */
@Api(tags = "系列附件:PSeriesAttachment")
@FeignClient(Constant.APPLICATION_NAME)
public interface IPSeriesAttachmentService {

    @ApiOperation(value = "查询系列附件列表", response = AjaxResult.class)
    @GetMapping("attachment/list")
    TableDataInfo list(PSeriesAttachment pSeriesAttachment);


    @ApiOperation(value = "获取系列附件详细信息", response = AjaxResult.class)
    @GetMapping(value = "attachment/{id}")
    AjaxResult getInfo(@PathVariable(value = "id") Integer id);

    @ApiOperation(value = "新增系列附件", response = AjaxResult.class)
    @Log(title = "系列附件", businessType = BusinessType.INSERT)
    @PostMapping(value = "attachment")
    AjaxResult add(@RequestBody PSeriesAttachment pSeriesAttachment);

    @ApiOperation(value = "修改系列附件", response = AjaxResult.class)
    @Log(title = "系列附件", businessType = BusinessType.UPDATE)
    @PutMapping(value = "attachment")
    AjaxResult edit(@RequestBody PSeriesAttachment pSeriesAttachment);

    @ApiOperation(value = "删除系列附件", response = AjaxResult.class)
    @Log(title = "系列附件", businessType = BusinessType.DELETE)
    @DeleteMapping("attachment/{ids}")
    AjaxResult remove(@PathVariable(value = "ids") Integer[] ids);



    @ApiOperation(value = "分页查询", response = R.class)
    @PostMapping(value = "attachment/page")
    PageResultUtils<PSeriesAttachment> page(@RequestBody PSeriesAttachment seriesAttachment);

    @ApiOperation(value = "根据系列查询资料", response = R.class)
    @PostMapping(value = "attachment/getListBySeries")
    R getListBySeries(@RequestBody PSeriesAttachment seriesAttachment);





}
