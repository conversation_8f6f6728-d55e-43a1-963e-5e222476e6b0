package com.rw.product.service;

import com.rw.common.core.domain.R;
import com.rw.common.core.utils.PageResultUtils;
import com.rw.product.model.Constant;
import com.rw.product.model.entity.PAir;
import com.rw.product.model.entity.SBoxlist;
import com.rw.product.model.entity.SBoxlistDetail;
import com.rw.product.model.req.BoxList10ByProductIdReq;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;
import com.rw.common.log.annotation.Log;
import com.rw.common.log.enums.BusinessType;
import com.rw.common.core.web.page.TableDataInfo;
import com.rw.common.core.web.domain.AjaxResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * 方案 包含 配电柜,UPS,空调,安防,EPS,动环接口
 * 
 * <AUTHOR>
 * @date 2024-07-23
 */
@Api(tags = "方案 包含 配电柜,UPS,空调,安防,EPS,动环:SBoxlist")
@FeignClient(Constant.APPLICATION_NAME)
public interface ISBoxlistService {

    @ApiOperation(value = "查询方案 包含 配电柜,UPS,空调,安防,EPS,动环列表", response = AjaxResult.class)
    @GetMapping("boxlist/list")
    TableDataInfo list(SBoxlist sBoxlist);


    @ApiOperation(value = "获取方案 包含 配电柜,UPS,空调,安防,EPS,动环详细信息", response = AjaxResult.class)
    @GetMapping(value = "boxlist/{id}")
    AjaxResult getInfo(@PathVariable(value = "id") Integer id);

    @ApiOperation(value = "新增方案 包含 配电柜,UPS,空调,安防,EPS,动环", response = AjaxResult.class)
    @Log(title = "方案 包含 配电柜,UPS,空调,安防,EPS,动环", businessType = BusinessType.INSERT)
    @PostMapping(value = "boxlist")
    AjaxResult add(@RequestBody SBoxlist sBoxlist);

    @ApiOperation(value = "修改方案 包含 配电柜,UPS,空调,安防,EPS,动环", response = AjaxResult.class)
    @Log(title = "方案 包含 配电柜,UPS,空调,安防,EPS,动环", businessType = BusinessType.UPDATE)
    @PutMapping(value = "boxlist")
    AjaxResult edit(@RequestBody SBoxlist sBoxlist);

    @ApiOperation(value = "删除方案 包含 配电柜,UPS,空调,安防,EPS,动环", response = AjaxResult.class)
    @Log(title = "方案 包含 配电柜,UPS,空调,安防,EPS,动环", businessType = BusinessType.DELETE)
    @DeleteMapping("boxlist/{ids}")
    AjaxResult remove(@PathVariable(value = "ids") Integer[] ids);

    @ApiOperation(value = "审核通过", response = AjaxResult.class)
    @Log(title = "审核通过", businessType = BusinessType.UPDATE)
    @DeleteMapping("boxlist/passed")
    AjaxResult passed(@RequestParam(value = "id") Integer id);

    @ApiOperation(value = "审核驳回", response = AjaxResult.class)
    @Log(title = "审核驳回", businessType = BusinessType.UPDATE)
    @DeleteMapping("boxlist/overrule")
    AjaxResult overrule(@RequestParam(value = "id") Integer id);


    @ApiOperation(value = "方案10条数据", response = R.class)
    @PostMapping(value = "boxlist/getTheSameSeries10")
    R getBoxList10ByProductId(@RequestBody BoxList10ByProductIdReq req);


    @ApiOperation(value = "分页查询", response = R.class)
    @PostMapping(value = "boxlist/page")
    PageResultUtils<SBoxlist> page(@RequestBody SBoxlist sBoxlist);



    @ApiOperation(value = "查询详情", response = R.class)
    @PostMapping(value = "boxlist/getInfoById")
    R getInfoById(@RequestBody SBoxlist sBoxlist);

}
