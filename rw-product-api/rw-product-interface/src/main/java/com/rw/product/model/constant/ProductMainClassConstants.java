package com.rw.product.model.constant;

import java.util.Arrays;
import java.util.List;


public class ProductMainClassConstants {

    //室外机管理,室内机管理,气液管管理,水管管理,制冷剂管理,空调配件管理
    public static List<String> air= Arrays.asList("5", "6", "24", "25", "27", "201");
    //EPS管理
    public static  List<String> eps= Arrays.asList("40");
    //监控中心,配电系统,UPS系统,空调系统,漏水系统,温湿度检测,视频监控,门禁系统,消防系统
    public static  List<String> pemonitor= Arrays.asList("70", "71", "72", "73", "74", "75", "76", "77", "78");
    //断路器,柜体,仪表,按钮和指示灯,互感器,浪涌保护,铜排,插座,配件,ATS,摄像机管理,录像机管理,交换机管理,传输类管理,隔离开关,负荷开关,接触器,熔断器,继电器,断路器附件,UPS配件管理,安防配件管理
    public static  List<String> product= Arrays.asList("4", "15", "16", "17", "18", "19", "20", "21", "22", "23", "30", "31", "32", "33", "80", "81", "82", "83", "84", "85","200","202");
    //STS管理 PDU管理
    public static  List<String> room= Arrays.asList("50", "51");
    //稳压电源管理 逆变器管理
    public static  List<String> single= Arrays.asList("52", "53");
    //UPS管理 电池管理 电池柜管理 电池线管理
    public static  List<String> ups= Arrays.asList("1", "2", "68", "69");

    /**UPS管理*/
    public static Integer ups_ups=1;

}
