package com.rw.product.service;

import com.rw.product.model.Constant;
import com.rw.product.model.entity.SProlistDetails;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import com.rw.common.log.annotation.Log;
import com.rw.common.log.enums.BusinessType;
import com.rw.common.core.web.page.TableDataInfo;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import com.rw.common.core.web.domain.AjaxResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * 方案组合详情接口
 * 
 * <AUTHOR>
 * @date 2024-07-23
 */
@Api(tags = "方案组合详情:SProlistDetails")
@FeignClient(Constant.APPLICATION_NAME)
public interface ISProlistDetailsService {

    @ApiOperation(value = "查询方案组合详情列表", response = AjaxResult.class)
    @GetMapping("details/list")
    TableDataInfo list(SProlistDetails sProlistDetails);


    @ApiOperation(value = "获取方案组合详情详细信息", response = AjaxResult.class)
    @GetMapping(value = "details/{id}")
    AjaxResult getInfo(@PathVariable(value = "id") Integer id);

    @ApiOperation(value = "新增方案组合详情", response = AjaxResult.class)
    @Log(title = "方案组合详情", businessType = BusinessType.INSERT)
    @PostMapping(value = "details")
    AjaxResult add(@RequestBody SProlistDetails sProlistDetails);

    @ApiOperation(value = "修改方案组合详情", response = AjaxResult.class)
    @Log(title = "方案组合详情", businessType = BusinessType.UPDATE)
    @PutMapping(value = "details")
    AjaxResult edit(@RequestBody SProlistDetails sProlistDetails);

    @ApiOperation(value = "删除方案组合详情", response = AjaxResult.class)
    @Log(title = "方案组合详情", businessType = BusinessType.DELETE)
    @DeleteMapping("details/{ids}")
    AjaxResult remove(@PathVariable(value = "ids") Integer[] ids);
}
