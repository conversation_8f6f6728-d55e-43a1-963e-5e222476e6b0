package com.rw.product.service;

import com.rw.common.core.domain.R;
import com.rw.common.core.utils.PageResultUtils;
import com.rw.product.model.Constant;
import com.rw.product.model.entity.PAir;
import com.rw.product.model.entity.PRoom;
import com.rw.product.model.entity.PSingle;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import com.rw.common.log.annotation.Log;
import com.rw.common.log.enums.BusinessType;
import com.rw.common.core.web.page.TableDataInfo;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import com.rw.common.core.web.domain.AjaxResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * 整体机房管理 包含 STS管理 PDU管理接口
 * 
 * <AUTHOR>
 * @date 2024-07-23
 */
@Api(tags = "整体机房管理 包含 STS管理 PDU管理:PRoom")
@FeignClient(Constant.APPLICATION_NAME)
public interface IPRoomService {

    @ApiOperation(value = "查询整体机房管理 包含 STS管理 PDU管理列表", response = AjaxResult.class)
    @GetMapping("room/list")
    TableDataInfo list(PRoom pRoom);


    @ApiOperation(value = "获取整体机房管理 包含 STS管理 PDU管理详细信息", response = AjaxResult.class)
    @GetMapping(value = "room/{id}")
    AjaxResult getInfo(@PathVariable(value = "id") Integer id);

    @ApiOperation(value = "新增整体机房管理 包含 STS管理 PDU管理", response = AjaxResult.class)
    @Log(title = "整体机房管理 包含 STS管理 PDU管理", businessType = BusinessType.INSERT)
    @PostMapping(value = "room")
    AjaxResult add(@RequestBody PRoom pRoom);

    @ApiOperation(value = "修改整体机房管理 包含 STS管理 PDU管理", response = AjaxResult.class)
    @Log(title = "整体机房管理 包含 STS管理 PDU管理", businessType = BusinessType.UPDATE)
    @PutMapping(value = "room")
    AjaxResult edit(@RequestBody PRoom pRoom);

    @ApiOperation(value = "删除整体机房管理 包含 STS管理 PDU管理", response = AjaxResult.class)
    @Log(title = "整体机房管理 包含 STS管理 PDU管理", businessType = BusinessType.DELETE)
    @DeleteMapping("room/{ids}")
    AjaxResult remove(@PathVariable(value = "ids") Integer[] ids);


    @ApiOperation(value = "修改isshow状态", response = AjaxResult.class)
    @Log(title = "修改isshow状态", businessType = BusinessType.UPDATE)
    @PostMapping(value = "room/updateIsShow")
    AjaxResult updateIsShow(@RequestBody PRoom pRoom);

    @ApiOperation(value = "分页查询", response = R.class)
    @PostMapping(value = "room/page")
    PageResultUtils<PRoom> page(@RequestBody PRoom pRoom);


    @ApiOperation(value = "查询详情", response = R.class)
    @PostMapping(value = "room/getInfoById")
    R getInfoById(@RequestBody PRoom pRoom);
}
