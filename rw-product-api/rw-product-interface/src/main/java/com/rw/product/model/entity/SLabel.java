package com.rw.product.model.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.rw.common.core.web.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
/**
 * 方案标签对象 s_label
 *
 * <AUTHOR>
 * @date 2024-07-23
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "方案标签")
public class SLabel extends BaseEntity {
    @TableId
    @ApiModelProperty(value = "内部标识")
    private Integer id;
    @ApiModelProperty(value = "标签名")
    private String name;
    @ApiModelProperty(value = "标签名(英文)")
    private String nameEn;
    @ApiModelProperty(value = "分类类别 比如：品牌 容量 特色等等")
    private Integer classsifyId;
    @ApiModelProperty(value = "方案类型 1配电柜 2ups 3空调 4安防 5一级配电柜 6EPS 7动环 8机房能源配置")
    private Integer labelClass;
    @ApiModelProperty(value = "排序")
    private Integer sort;
    @ApiModelProperty(value = "是否显示 1是")
    private Integer isShow;
    @ApiModelProperty(value = "点击量")
    private Integer hits;
    @TableLogic
    @ApiModelProperty(value = "删除状态 1已删除 0未删除")
    private Long delFlag;
    @ApiModelProperty(value = "创建人id")
    private Long createId;

    @ApiModelProperty(value = "更新人id")
    private Long updateId;


}
