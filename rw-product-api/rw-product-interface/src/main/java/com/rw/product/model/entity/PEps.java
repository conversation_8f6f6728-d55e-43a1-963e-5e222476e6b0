package com.rw.product.model.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.rw.common.core.web.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.math.BigDecimal;
import java.util.Date;
/**
 * EPS 包含 EPS管理对象 p_eps
 *
 * <AUTHOR>
 * @date 2024-07-23
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "EPS 包含 EPS管理")
public class PEps extends BaseEntity {
    @TableId
    @ApiModelProperty(value = "内部标识")
    private Integer id;
    @ApiModelProperty(value = "名称")
    private String title;
    @ApiModelProperty(value = "名称(英文)")
    private String titleEn;
    @ApiModelProperty(value = "副标题 简述")
    private String subTitle;
    @ApiModelProperty(value = "副标题 简述(英文)")
    private String subTitleEn;
    @ApiModelProperty(value = "类别")
    private Integer mainClass;
    @ApiModelProperty(value = "目录报价")
    private BigDecimal price;
    @ApiModelProperty(value = "零售价格")
    private BigDecimal priceRetail;
    @ApiModelProperty(value = "品牌")
    private Integer brand;
    @TableField(exist = false)
    @ApiModelProperty(value = "品牌名称")
    private String brandTitle;
    @ApiModelProperty(value = "系列")
    private Integer series;
    @TableField(exist = false)
    @ApiModelProperty(value = "系列名称")
    private String seriesTitle;
    @ApiModelProperty(value = "图片")
    private String img;
    @ApiModelProperty(value = "产品图片缩略图")
    private String imgs;
    @ApiModelProperty(value = "产品图片缩略图英文")
    private String imgsEn;
    @ApiModelProperty(value = "关联url")
    private String linkUrl;
    @ApiModelProperty(value = "重量")
    private BigDecimal weight;
    @ApiModelProperty(value = "毛重")
    private BigDecimal fullWeight;
    @ApiModelProperty(value = "宽(尺寸)")
    private String width;
    @ApiModelProperty(value = "高(作废)")
    private String height;
    @ApiModelProperty(value = "深(作废)")
    private String deep;
    @ApiModelProperty(value = "容量")
    private Integer capacity;
    @TableField(exist = false)
    @ApiModelProperty(value = "容量Name")
    private String capacityTitle;
    @ApiModelProperty(value = "类型")
    private Integer classify;
    @TableField(exist = false)
    @ApiModelProperty(value = "类型Name")
    private String classifyTitle;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "发布时间")
    private Date releaseTime;
    @ApiModelProperty(value = "生产状态1：在产，2：停产")
    private Integer produceState;
    @ApiModelProperty(value = "是否显示 1是")
    private Integer isShow;

    @TableLogic
    @ApiModelProperty(value = "删除状态 1已删除 0未删除")
    private Long delFlag;
    @ApiModelProperty(value = "创建人id")
    private Long createId;

    @ApiModelProperty(value = "更新人id")
    private Long updateId;



    @ApiModelProperty(value = "69码")
    private String barcode;

    @ApiModelProperty(value = "输入输出")
    private Integer voltageSort;
    @TableField(exist = false)

    @ApiModelProperty(value = "输入输出名称")
    private String voltageSortTitle;

    @ApiModelProperty(value = "货期")
    private String deliveryDate;

    @ApiModelProperty(value = "质保期")
    private String warrantyPeriod;

    @ApiModelProperty(value = "直营价")
    private BigDecimal directSalePrice;

    @ApiModelProperty(value = "成本价")
    private BigDecimal priceCb;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "价格更新时间")
    private Date priceUpdateTime;




    @ApiModelProperty(value = "详情参数")
    @TableField(exist = false)
    private PDetailParameters detailParameters;

}
