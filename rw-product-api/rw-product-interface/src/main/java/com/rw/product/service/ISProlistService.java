package com.rw.product.service;

import com.rw.product.model.Constant;
import com.rw.product.model.entity.SProlist;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import com.rw.common.log.annotation.Log;
import com.rw.common.log.enums.BusinessType;
import com.rw.common.core.web.page.TableDataInfo;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import com.rw.common.core.web.domain.AjaxResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * 方案组合 包含 一级配电柜,机房能源配置接口
 * 
 * <AUTHOR>
 * @date 2024-07-23
 */
@Api(tags = "方案组合 包含 一级配电柜,机房能源配置:SProlist")
@FeignClient(Constant.APPLICATION_NAME)
public interface ISProlistService {

    @ApiOperation(value = "查询方案组合 包含 一级配电柜,机房能源配置列表", response = AjaxResult.class)
    @GetMapping("prolist/list")
    TableDataInfo list(SProlist sProlist);


    @ApiOperation(value = "获取方案组合 包含 一级配电柜,机房能源配置详细信息", response = AjaxResult.class)
    @GetMapping(value = "prolist/{id}")
    AjaxResult getInfo(@PathVariable(value = "id") Integer id);

    @ApiOperation(value = "新增方案组合 包含 一级配电柜,机房能源配置", response = AjaxResult.class)
    @Log(title = "方案组合 包含 一级配电柜,机房能源配置", businessType = BusinessType.INSERT)
    @PostMapping(value = "prolist")
    AjaxResult add(@RequestBody SProlist sProlist);

    @ApiOperation(value = "修改方案组合 包含 一级配电柜,机房能源配置", response = AjaxResult.class)
    @Log(title = "方案组合 包含 一级配电柜,机房能源配置", businessType = BusinessType.UPDATE)
    @PutMapping(value = "prolist")
    AjaxResult edit(@RequestBody SProlist sProlist);

    @ApiOperation(value = "删除方案组合 包含 一级配电柜,机房能源配置", response = AjaxResult.class)
    @Log(title = "方案组合 包含 一级配电柜,机房能源配置", businessType = BusinessType.DELETE)
    @DeleteMapping("prolist/{ids}")
    AjaxResult remove(@PathVariable(value = "ids") Integer[] ids);
}
