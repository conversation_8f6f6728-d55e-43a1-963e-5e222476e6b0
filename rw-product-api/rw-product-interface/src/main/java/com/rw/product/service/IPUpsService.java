package com.rw.product.service;

import com.rw.common.core.domain.R;
import com.rw.common.core.utils.PageResultUtils;
import com.rw.product.model.Constant;
import com.rw.product.model.entity.PUps;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import com.rw.common.log.annotation.Log;
import com.rw.common.log.enums.BusinessType;
import com.rw.common.core.web.page.TableDataInfo;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import com.rw.common.core.web.domain.AjaxResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * UPS信息 包含 UPS管理 电池管理  电池柜管理 电池线管理接口
 * 
 * <AUTHOR>
 * @date 2024-07-23
 */
@Api(tags = "UPS信息 包含 UPS管理 电池管理  电池柜管理 电池线管理:PUps")
@FeignClient(Constant.APPLICATION_NAME)
public interface IPUpsService {

    @ApiOperation(value = "查询UPS信息 包含 UPS管理 电池管理  电池柜管理 电池线管理列表", response = AjaxResult.class)
    @GetMapping("ups/list")
    TableDataInfo list(PUps pUps);


    @ApiOperation(value = "获取UPS信息 包含 UPS管理 电池管理  电池柜管理 电池线管理详细信息", response = AjaxResult.class)
    @GetMapping(value = "ups/{id}")
    AjaxResult getInfo(@PathVariable(value = "id") Integer id);

    @ApiOperation(value = "新增UPS信息 包含 UPS管理 电池管理  电池柜管理 电池线管理", response = AjaxResult.class)
    @Log(title = "UPS信息 包含 UPS管理 电池管理  电池柜管理 电池线管理", businessType = BusinessType.INSERT)
    @PostMapping(value = "ups")
    AjaxResult add(@RequestBody PUps pUps);

    @ApiOperation(value = "修改UPS信息 包含 UPS管理 电池管理  电池柜管理 电池线管理", response = AjaxResult.class)
    @Log(title = "UPS信息 包含 UPS管理 电池管理  电池柜管理 电池线管理", businessType = BusinessType.UPDATE)
    @PutMapping(value = "ups")
    AjaxResult edit(@RequestBody PUps pUps);

    @ApiOperation(value = "删除UPS信息 包含 UPS管理 电池管理  电池柜管理 电池线管理", response = AjaxResult.class)
    @Log(title = "UPS信息 包含 UPS管理 电池管理  电池柜管理 电池线管理", businessType = BusinessType.DELETE)
    @DeleteMapping("ups/{ids}")
    AjaxResult remove(@PathVariable(value = "ids") Integer[] ids);


    @ApiOperation(value = "修改isshow状态", response = AjaxResult.class)
    @Log(title = "修改isshow状态", businessType = BusinessType.UPDATE)
    @PostMapping(value = "ups/updateIsShow")
    AjaxResult updateIsShow(@RequestBody PUps pUps);

    @ApiOperation(value = "分页查询", response = R.class)
    @PostMapping(value = "ups/page")
    PageResultUtils<PUps> page(@RequestBody PUps pUps);

    @ApiOperation(value = "查询详情", response = R.class)
    @PostMapping(value = "ups/getInfoById")
    R getInfoById(@RequestBody PUps pUps);

}
