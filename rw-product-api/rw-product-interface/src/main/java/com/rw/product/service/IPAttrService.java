package com.rw.product.service;

import com.rw.common.core.domain.R;
import com.rw.common.core.web.domain.AjaxResult;
import com.rw.common.core.web.page.TableDataInfo;
import com.rw.common.log.annotation.Log;
import com.rw.common.log.enums.BusinessType;
import com.rw.product.model.Constant;
import com.rw.product.model.entity.PAttr;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 属性 存储各类别的属性 比如：品牌 系列 容量 压力等等接口
 * 
 * <AUTHOR>
 * @date 2024-07-23
 */
@Api(tags = "属性 存储各类别的属性 比如：品牌 系列 容量 压力等等:PAttr")
@FeignClient(Constant.APPLICATION_NAME)
public interface IPAttrService {

    @ApiOperation(value = "查询属性 存储各类别的属性 比如：品牌 系列 容量 压力等等列表", response = AjaxResult.class)
    @GetMapping("attr/listAll")
    AjaxResult listAll(PAttr pAttr);

    @ApiOperation(value = "查询属性 存储各类别的属性 比如：品牌 系列 容量 压力等等列表", response = AjaxResult.class)
    @GetMapping("attr/list")
    TableDataInfo list(PAttr pAttr);

    @ApiOperation(value = "获取属性 存储各类别的属性 比如：品牌 系列 容量 压力等等详细信息", response = AjaxResult.class)
    @GetMapping(value = "attr/{id}")
    AjaxResult getInfo(@PathVariable(value = "id") Integer id);

    @ApiOperation(value = "新增属性 存储各类别的属性 比如：品牌 系列 容量 压力等等", response = AjaxResult.class)
    @Log(title = "属性 存储各类别的属性 比如：品牌 系列 容量 压力等等", businessType = BusinessType.INSERT)
    @PostMapping(value = "attr")
    AjaxResult add(@RequestBody PAttr pAttr);

    @ApiOperation(value = "修改属性 存储各类别的属性 比如：品牌 系列 容量 压力等等", response = AjaxResult.class)
    @Log(title = "属性 存储各类别的属性 比如：品牌 系列 容量 压力等等", businessType = BusinessType.UPDATE)
    @PutMapping(value = "attr")
    AjaxResult edit(@RequestBody PAttr pAttr);

    @ApiOperation(value = "删除属性 存储各类别的属性 比如：品牌 系列 容量 压力等等", response = AjaxResult.class)
    @Log(title = "属性 存储各类别的属性 比如：品牌 系列 容量 压力等等", businessType = BusinessType.DELETE)
    @DeleteMapping("attr/{ids}")
    AjaxResult remove(@PathVariable(value = "ids") Integer[] ids);



    @ApiOperation(value = "根据mainClass获取所有属性并分组", response = AjaxResult.class)
    @GetMapping(value = "attr/getAllAttrGroupByMainClass/{mainClass}")
    AjaxResult getAllAttrGroupByMainClass(@PathVariable(value = "mainClass") Integer mainClass);

    @ApiOperation(value = "根据父ID获取所有属性", response = AjaxResult.class)
    @GetMapping(value = "attr/getAttrByFatherId/{fatherId}")
    AjaxResult getAttrByFatherId(@PathVariable(value = "fatherId") Integer fatherId);

    @ApiOperation(value = "根据父ID获取配件相关的所有属性 如UPS配件、配件、空调配件、安防配件   父ID为类别", response = AjaxResult.class)
    @GetMapping(value = "attr/getAccessoryRelatedAttrByFatherId/{fatherId}")
    AjaxResult getAccessoryRelatedAttrByFatherId(@PathVariable(value = "fatherId") Integer fatherId);



    @ApiOperation(value = "获取属性", response = R.class)
    @PostMapping(value = "attr/getAttrList")
    R getAttrList(@RequestBody PAttr pAttr);
}
