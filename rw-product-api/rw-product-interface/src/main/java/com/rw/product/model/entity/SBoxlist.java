package com.rw.product.model.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.rw.common.core.web.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 方案 包含 配电柜,UPS,空调,安防,EPS,动环对象 s_boxlist
 *
 * <AUTHOR>
 * @date 2024-07-23
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "方案 包含 配电柜,UPS,空调,安防,EPS,动环")
public class SBoxlist extends BaseEntity {
    @TableId
    @ApiModelProperty(value = "内部标识")
    private Integer id;
    @ApiModelProperty(value = "系统编号")
    private Long listNo;
    @ApiModelProperty(value = "用户类型 0-前台 1-后台 2-商家")
    private Integer usertype;
    @ApiModelProperty(value = "用户ID")
    private String userid;
    @ApiModelProperty(value = "是否推荐 1是")
    private Integer isFlag;
    @ApiModelProperty(value = "名称")
    private String title;
    @ApiModelProperty(value = "名称(英文)")
    private String titleEn;
    @ApiModelProperty(value = "副标题 简述")
    private String subTitle;
    @ApiModelProperty(value = "副标题 简述(英文)")
    private String subTitleEn;
    @ApiModelProperty(value = "方案关联的标签，存的标签表id,逗号分割")
    private String labelId;
    @ApiModelProperty(value = "方案关联的标签，存的标签表id,逗号分割")
    @TableField(exist = false)
    private String labelIdName;
    @ApiModelProperty(value = "方案类型 1配电柜 2ups 3空调 4安防 5一级配电柜 6EPS 7动环 8机房能源配置")
    private Long classify;
    @ApiModelProperty(value = "是否显示 1是")
    private Integer isShow;
    @ApiModelProperty(value = "CAD文件路径")
    private String cadPath;
    @ApiModelProperty(value = "pdf文件路径")
    private String pdfPath;
    @ApiModelProperty(value = "rar文件路径")
    private String rarPath;
    @ApiModelProperty(value = "图片路径")
    private String imgPath;
    @ApiModelProperty(value = "0 审核中 1 审核通过 2 驳回")
    private Integer status;
    @ApiModelProperty(value = "作者")
    private String author;
    @ApiModelProperty(value = "第二作者")
    private String secondAuthor;
    @ApiModelProperty(value = "运费")
    private BigDecimal freight;
    @ApiModelProperty(value = "安装费")
    private BigDecimal install;
    @ApiModelProperty(value = "就位费")
    private BigDecimal handlingFee;
    @ApiModelProperty(value = "调试费")
    private BigDecimal debugFee;
    @ApiModelProperty(value = "组装费")
    private BigDecimal mprice;
    @ApiModelProperty(value = "利润")
    private BigDecimal profit;
    @ApiModelProperty(value = "辅料价格")
    private BigDecimal acprice;
    @ApiModelProperty(value = "税金")
    private BigDecimal tax;
    @ApiModelProperty(value = "加钣金费用")
    private BigDecimal sheetMetalCost;
    @ApiModelProperty(value = "面板费用")
    private BigDecimal panelCost;
    @ApiModelProperty(value = "包装费")
    private BigDecimal packingFee;
    @ApiModelProperty(value = "总计 列表显示")
    private BigDecimal eleprice;
    @ApiModelProperty(value = "点赞数")
    private Integer likeCount;
    @ApiModelProperty(value = "排序")
    private Integer sort;
    @ApiModelProperty(value = "???")
    private Long category;
    @ApiModelProperty(value = "???")
    private Long inputCap;
    @ApiModelProperty(value = "???")
    private Long ways;
    @ApiModelProperty(value = "???")
    private Long low;
    @ApiModelProperty(value = "???")
    private Long secondary;
    @ApiModelProperty(value = "???")
    private Long high;

    @TableLogic
    @ApiModelProperty(value = "删除状态 1已删除 0未删除")
    private Long delFlag;
    @ApiModelProperty(value = "创建人id")
    private Long createId;

    @ApiModelProperty(value = "更新人id")
    private Long updateId;


    /**
     * 方案包含的产品
     */
    @TableField(exist = false)
    private List<SBoxlistDetail> detailList;


}
