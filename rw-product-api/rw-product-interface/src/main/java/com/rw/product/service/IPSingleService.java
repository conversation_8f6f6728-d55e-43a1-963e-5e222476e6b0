package com.rw.product.service;

import com.rw.common.core.domain.R;
import com.rw.common.core.utils.PageResultUtils;
import com.rw.product.model.Constant;
import com.rw.product.model.entity.PAir;
import com.rw.product.model.entity.PSingle;
import com.rw.product.model.entity.PUps;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import com.rw.common.log.annotation.Log;
import com.rw.common.log.enums.BusinessType;
import com.rw.common.core.web.page.TableDataInfo;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import com.rw.common.core.web.domain.AjaxResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * 非成套产品 包含 稳压电源管理 逆变器管理接口
 * 
 * <AUTHOR>
 * @date 2024-07-23
 */
@Api(tags = "非成套产品 包含 稳压电源管理 逆变器管理:PSingle")
@FeignClient(Constant.APPLICATION_NAME)
public interface IPSingleService {

    @ApiOperation(value = "查询非成套产品 包含 稳压电源管理 逆变器管理列表", response = AjaxResult.class)
    @GetMapping("single/list")
    TableDataInfo list(PSingle pSingle);


    @ApiOperation(value = "获取非成套产品 包含 稳压电源管理 逆变器管理详细信息", response = AjaxResult.class)
    @GetMapping(value = "single/{id}")
    AjaxResult getInfo(@PathVariable(value = "id") Integer id);

    @ApiOperation(value = "新增非成套产品 包含 稳压电源管理 逆变器管理", response = AjaxResult.class)
    @Log(title = "非成套产品 包含 稳压电源管理 逆变器管理", businessType = BusinessType.INSERT)
    @PostMapping(value = "single")
    AjaxResult add(@RequestBody PSingle pSingle);

    @ApiOperation(value = "修改非成套产品 包含 稳压电源管理 逆变器管理", response = AjaxResult.class)
    @Log(title = "非成套产品 包含 稳压电源管理 逆变器管理", businessType = BusinessType.UPDATE)
    @PutMapping(value = "single")
    AjaxResult edit(@RequestBody PSingle pSingle);

    @ApiOperation(value = "删除非成套产品 包含 稳压电源管理 逆变器管理", response = AjaxResult.class)
    @Log(title = "非成套产品 包含 稳压电源管理 逆变器管理", businessType = BusinessType.DELETE)
    @DeleteMapping("single/{ids}")
    AjaxResult remove(@PathVariable(value = "ids") Integer[] ids);


    @ApiOperation(value = "修改isshow状态", response = AjaxResult.class)
    @Log(title = "修改isshow状态", businessType = BusinessType.UPDATE)
    @PostMapping(value = "single/updateIsShow")
    AjaxResult updateIsShow(@RequestBody PSingle pSingle);


    @ApiOperation(value = "分页查询", response = R.class)
    @PostMapping(value = "single/page")
    PageResultUtils<PSingle> page(@RequestBody PSingle pSingle);




    @ApiOperation(value = "查询详情", response = R.class)
    @PostMapping(value = "single/getInfoById")
    R getInfoById(@RequestBody PSingle pSingle);
}
