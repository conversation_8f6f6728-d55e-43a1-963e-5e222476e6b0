package com.rw.product.model.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.rw.common.core.web.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.util.Date;
/**
 * 系列附件对象 p_series_attachment
 *
 * <AUTHOR>
 * @date 2024-08-16
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "系列附件")
public class PSeriesAttachment extends BaseEntity {
    @TableId
    @ApiModelProperty(value = "内部标识")
    private Integer id;
    @ApiModelProperty(value = "名称")
    private String title;
    @ApiModelProperty(value = "名称(英文)")
    private String titleEn;
    @ApiModelProperty(value = "附件")
    private String attachment;
    @ApiModelProperty(value = "附件大小")
    private String attachmentSize;
    @ApiModelProperty(value = "附件(英文)")
    private String attachmentEn;
    @ApiModelProperty(value = "附件(英文)大小")
    private String attachmentEnSize;
    @ApiModelProperty(value = "类别")
    private Integer mainClass;
    @ApiModelProperty(value = "品牌")
    private Integer brand;
    @TableField(exist = false)
    @ApiModelProperty(value = "品牌")
    private String brandTitle;
    @ApiModelProperty(value = "系列")
    private Integer series;
    @TableField(exist = false)
    @ApiModelProperty(value = "系列")
    private String seriesTitle;
    @TableLogic
    @ApiModelProperty(value = "删除状态 1已删除 0未删除")
    private Long delFlag;
    @ApiModelProperty(value = "创建人id")
    private Long createId;
    @ApiModelProperty(value = "更新人id")
    private Long updateId;

}
