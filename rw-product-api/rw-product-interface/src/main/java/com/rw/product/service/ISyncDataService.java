package com.rw.product.service;

import com.rw.common.core.domain.R;
import com.rw.product.model.Constant;
import com.rw.product.model.entity.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 同步数据
 */
@Api(tags = "同步数据")
@FeignClient(Constant.APPLICATION_NAME)
public interface ISyncDataService {

    @ApiOperation(value = "测试", response = R.class)
    @GetMapping(value = "sync/t")
    R t();

    @ApiOperation(value = "添加air表", response = R.class)
    @PostMapping(value = "sync/test")
    R test(@RequestBody PAir air);

    @ApiOperation(value = "添加air表", response = R.class)
    @PostMapping(value = "sync/air/add")
    R addAir(@RequestBody PAir air);

    @ApiOperation(value = "修改air表", response = R.class)
    @PostMapping(value = "sync/air/update")
    R updateAir(@RequestBody PAir air);




    @ApiOperation(value = "添加attr表", response = R.class)
    @PostMapping(value = "sync/attr/add")
    R addAttr(@RequestBody PAttr attr);

    @ApiOperation(value = "修改attr表", response = R.class)
    @PostMapping(value = "sync/attr/update")
    R updateAttr(@RequestBody PAttr attr);


    @ApiOperation(value = "添加eps表", response = R.class)
    @PostMapping(value = "sync/eps/add")
    R addEps(@RequestBody PEps eps);

    @ApiOperation(value = "修改eps表", response = R.class)
    @PostMapping(value = "sync/eps/update")
    R updateEps(@RequestBody PEps eps);



    @ApiOperation(value = "添加pemonitor表", response = R.class)
    @PostMapping(value = "sync/pemonitor/add")
    R addPemonitor(@RequestBody PPemonitor pemonitor);

    @ApiOperation(value = "修改pemonitor表", response = R.class)
    @PostMapping(value = "sync/pemonitor/update")
    R updatePemonitor(@RequestBody PPemonitor pemonitor);





    @ApiOperation(value = "添加product表", response = R.class)
    @PostMapping(value = "sync/product/add")
    R addProduct(@RequestBody PProduct product);

    @ApiOperation(value = "修改product表", response = R.class)
    @PostMapping(value = "sync/product/update")
    R updateProduct(@RequestBody PProduct product);



    @ApiOperation(value = "添加room表", response = R.class)
    @PostMapping(value = "sync/room/add")
    R addRoom(@RequestBody PRoom room);

    @ApiOperation(value = "修改room表", response = R.class)
    @PostMapping(value = "sync/room/update")
    R updateRoom(@RequestBody  PRoom room);




    @ApiOperation(value = "添加single表", response = R.class)
    @PostMapping(value = "sync/single/add")
    R addSingle(@RequestBody PSingle single);

    @ApiOperation(value = "修改room表", response = R.class)
    @PostMapping(value = "sync/single/update")
    R updateSingle(@RequestBody  PSingle single);



    @ApiOperation(value = "添加ups表", response = R.class)
    @PostMapping(value = "sync/ups/add")
    R addUps(@RequestBody PUps ups);

    @ApiOperation(value = "修改ups表", response = R.class)
    @PostMapping(value = "sync/ups/update")
    R updateUps(@RequestBody PUps ups);




    @ApiOperation(value = "添加label表", response = R.class)
    @PostMapping(value = "sync/label/add")
    R addLabel(@RequestBody SLabel label);

    @ApiOperation(value = "修改label表", response = R.class)
    @PostMapping(value = "sync/label/update")
    R updateLabel(@RequestBody SLabel label);



    @ApiOperation(value = "添加boxlist表", response = R.class)
    @PostMapping(value = "sync/boxlist/add")
    R addBoxlist(@RequestBody SBoxlist boxlist);

    @ApiOperation(value = "修改boxlist表", response = R.class)
    @PostMapping(value = "sync/boxlist/update")
    R updateBoxlist(@RequestBody SBoxlist boxlist);


    @ApiOperation(value = "删除boxlist", response = R.class)
    @PostMapping(value = "sync/boxlist/delBoxlist")
    R delBoxlist(@RequestBody SBoxlist boxlist);



    @ApiOperation(value = "添加boxlist_detail表", response = R.class)
    @PostMapping(value = "sync/boxlistdetail/add")
    R addBoxlistDetail(@RequestBody SBoxlistDetail boxlistDetail);

    @ApiOperation(value = "修改boxlist_detail表", response = R.class)
    @PostMapping(value = "sync/boxlistdetail/update")
    R updateBoxlistDetail(@RequestBody SBoxlistDetail boxlistDetail);



    @ApiOperation(value = "添加prolist表", response = R.class)
    @PostMapping(value = "sync/prolist/add")
    R addProlist(@RequestBody SProlist prolist);

    @ApiOperation(value = "修改prolist表", response = R.class)
    @PostMapping(value = "sync/prolist/update")
    R updateProlist(@RequestBody SProlist prolist);

    @ApiOperation(value = "删除prolist", response = R.class)
    @PostMapping(value = "sync/prolist/delProlist")
    R delProlist(@RequestBody SProlist prolist);





    @ApiOperation(value = "添加prolist_detail表", response = R.class)
    @PostMapping(value = "sync/prolistdetail/add")
    R addProlistDetail(@RequestBody SProlistDetails prolistDetails);

    @ApiOperation(value = "修改prolist_detail表", response = R.class)
    @PostMapping(value = "sync/prolistdetail/update")
    R updateProlistDetail(@RequestBody SProlistDetails prolistDetails);













    @ApiOperation(value = "同步所有图片", response = R.class)
    @GetMapping(value = "sync/allImg")
    R allImg();


}
