package com.rw.product.model.enums;

public class ConstData {
    public final static String encodePrefix = "-JHD-_";
    public final static String userName = "jhd_user";
    public final static String password = "xMT7Gg9HsLTxoXPW";

    //单条插入影响行数
    public final static Integer INSERT_AFFECT_ROWS = 1;

    //树图区块链默认合约id
//    public final static Integer DEFAULT_CONFLUX_CONTRACT_ID = 8;
    //至信区块链默认合约id  todo 这里不需要
    public final static Long DEFAULT_ZXCHAIN_CONTRACT_ID = -1L;

    //至信区块链默认合约id TODO 这里未启用
    public final static Long DEFAULT_ANTCHAIN_CONTRACT_ID = -2L;

}
