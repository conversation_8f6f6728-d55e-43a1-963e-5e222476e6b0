package com.rw.product.service;

import com.rw.product.model.Constant;
import com.rw.common.core.web.domain.AjaxResult;
import io.swagger.annotations.Api;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;

/**
 * 创作家接口
 *
 * <AUTHOR>
 * @date 2022-10-09
 */
@Api(tags = "测试:Test")
@FeignClient(Constant.APPLICATION_NAME)
public interface ITestService {

    @GetMapping(value = "test/test")
    AjaxResult test();


    @GetMapping(value = "test/mqtt")
    AjaxResult mqtt();
    @GetMapping(value = "test/test2")
    AjaxResult test2();

    @GetMapping(value = "test/wav")
    AjaxResult wav();
}
