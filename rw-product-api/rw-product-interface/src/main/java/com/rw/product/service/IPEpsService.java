package com.rw.product.service;

import com.rw.common.core.domain.R;
import com.rw.common.core.utils.PageResultUtils;
import com.rw.product.model.Constant;
import com.rw.product.model.entity.PAir;
import com.rw.product.model.entity.PEps;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import com.rw.common.log.annotation.Log;
import com.rw.common.log.enums.BusinessType;
import com.rw.common.core.web.page.TableDataInfo;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import com.rw.common.core.web.domain.AjaxResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * EPS 包含 EPS管理接口
 * 
 * <AUTHOR>
 * @date 2024-07-23
 */
@Api(tags = "EPS 包含 EPS管理:PEps")
@FeignClient(Constant.APPLICATION_NAME)
public interface IPEpsService {

    @ApiOperation(value = "查询EPS 包含 EPS管理列表", response = AjaxResult.class)
    @GetMapping("eps/list")
    TableDataInfo list(PEps pEps);


    @ApiOperation(value = "获取EPS 包含 EPS管理详细信息", response = AjaxResult.class)
    @GetMapping(value = "eps/{id}")
    AjaxResult getInfo(@PathVariable(value = "id") Integer id);

    @ApiOperation(value = "新增EPS 包含 EPS管理", response = AjaxResult.class)
    @Log(title = "EPS 包含 EPS管理", businessType = BusinessType.INSERT)
    @PostMapping(value = "eps")
    AjaxResult add(@RequestBody PEps pEps);

    @ApiOperation(value = "修改EPS 包含 EPS管理", response = AjaxResult.class)
    @Log(title = "EPS 包含 EPS管理", businessType = BusinessType.UPDATE)
    @PutMapping(value = "eps")
    AjaxResult edit(@RequestBody PEps pEps);

    @ApiOperation(value = "删除EPS 包含 EPS管理", response = AjaxResult.class)
    @Log(title = "EPS 包含 EPS管理", businessType = BusinessType.DELETE)
    @DeleteMapping("eps/{ids}")
    AjaxResult remove(@PathVariable(value = "ids") Integer[] ids);

    @ApiOperation(value = "修改isshow状态", response = AjaxResult.class)
    @Log(title = "修改isshow状态", businessType = BusinessType.UPDATE)
    @PostMapping(value = "eps/updateIsShow")
    AjaxResult updateIsShow(@RequestBody PEps pEps);

    @ApiOperation(value = "分页查询", response = R.class)
    @PostMapping(value = "eps/page")
    PageResultUtils<PEps> page(@RequestBody PEps pEps);

    @ApiOperation(value = "查询详情", response = R.class)
    @PostMapping(value = "eps/getInfoById")
    R getInfoById(@RequestBody PEps pEps);
}
