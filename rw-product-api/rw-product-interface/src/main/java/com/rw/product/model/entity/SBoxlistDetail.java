package com.rw.product.model.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.rw.common.core.web.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.math.BigDecimal;

/**
 * 方案详情对象 s_boxlist_detail
 *
 * <AUTHOR>
 * @date 2024-07-23
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "方案详情")
public class SBoxlistDetail extends BaseEntity {
    @TableId
    @ApiModelProperty(value = "内部标识")
    private Integer id;
    @ApiModelProperty(value = "产品ID")
    private Integer productId;
    @ApiModelProperty(value = "单品数量")
    private Integer num;
    @ApiModelProperty(value = "类别")
    private Integer mainClass;
    @ApiModelProperty(value = "系统编号")
    private Long listNo;
    @ApiModelProperty(value = "产品类别名称")
    private String type;
    @ApiModelProperty(value = "图片路径")
    private String imgPath;
    @ApiModelProperty(value = "清单单品简述")
    private String title2;
    @ApiModelProperty(value = "场所")
    private String place;
    @TableLogic
    @ApiModelProperty(value = "删除状态 1已删除 0未删除")
    private Long delFlag;
    @ApiModelProperty(value = "创建人id")
    private Long createId;

    @ApiModelProperty(value = "更新人id")
    private Long updateId;


    @ApiModelProperty(value = "产品名称")
    @TableField(exist = false)
    private String productTitle;

    @ApiModelProperty(value = "目录价格")
    @TableField(exist = false)
    private BigDecimal price;

    @ApiModelProperty(value = "零售价格")
    @TableField(exist = false)
    private BigDecimal priceRetail;

    @ApiModelProperty(value = "总价")
    @TableField(exist = false)
    private BigDecimal totalPrice;

    @ApiModelProperty(value = "品牌")
    @TableField(exist = false)
    private String brandTitle;

}
