package com.rw.product.model.entity;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.rw.common.core.annotation.Excel;
import com.rw.common.core.web.domain.BaseEntity;
import lombok.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.util.Date;
import java.util.List;

import com.rw.common.core.web.domain.BaseEntity;

/**
 * 方案组合 包含 一级配电柜,机房能源配置对象 s_prolist
 *
 * <AUTHOR>
 * @date 2024-07-23
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "方案组合 包含 一级配电柜,机房能源配置")
public class SProlist extends BaseEntity {
    @TableId
    @ApiModelProperty(value = "内部标识")
    private Integer id;
    @ApiModelProperty(value = "系统编号")
    private Long listNo;
    @ApiModelProperty(value = "方案类型 1配电柜 2ups 3空调 4安防 5一级配电柜 6EPS 7动环 8机房能源配置")
    private Long classify;
    @ApiModelProperty(value = "名称")
    private String title;
    @ApiModelProperty(value = "名称(英文)")
    private String titleEn;
    @ApiModelProperty(value = "副标题 简述")
    private String subTitle;
    @ApiModelProperty(value = "副标题 简述(英文)")
    private String subTitleEn;
    @ApiModelProperty(value = "方案关联的标签，存的标签表id,逗号分割")
    private String labelId;
    @ApiModelProperty(value = "用户类型 0-前台 1-后台 2-商家")
    private Integer usertype;
    @ApiModelProperty(value = "用户ID")
    private String userid;
    @ApiModelProperty(value = "是否推荐 1是")
    private Integer isFlag;
    @ApiModelProperty(value = "是否显示 1是")
    private Integer isShow;
    @ApiModelProperty(value = "0 审核中 1 审核通过 2 驳回")
    private Integer status;
    @ApiModelProperty(value = "CAD文件路径")
    private String cadPath;
    @ApiModelProperty(value = "pdf文件路径")
    private String pdfPath;
    @ApiModelProperty(value = "rar文件路径")
    private String rarPath;
    @ApiModelProperty(value = "图片路径")
    private String imgPath;
    @ApiModelProperty(value = "作者")
    private String author;
    @ApiModelProperty(value = "第二作者")
    private String secondAuthor;
    @ApiModelProperty(value = "点赞数")
    private Integer likeCount;
    @ApiModelProperty(value = "标识 排序")
    private Integer sort;

    @TableLogic
    @ApiModelProperty(value = "删除状态 1已删除 0未删除")
    private Long delFlag;
    @ApiModelProperty(value = "创建人id")
    private Long createId;
    @ApiModelProperty(value = "更新人id")
    private Long updateId;



    /**
     * 方案包含的产品
     */
    @TableField(exist = false)
    private List<SProlistDetails> detailList;
}
