package com.rw.product.service;

import com.rw.common.core.domain.R;
import com.rw.common.core.utils.PageResultUtils;
import com.rw.common.core.web.domain.AjaxResult;
import com.rw.common.core.web.page.TableDataInfo;
import com.rw.common.log.annotation.Log;
import com.rw.common.log.enums.BusinessType;
import com.rw.product.model.Constant;
import com.rw.product.model.entity.PAir;
import com.rw.product.model.entity.PRoom;
import com.rw.product.model.entity.PUps;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

/**
 * 空调 包含 室外机管理,室内机管理,气液管管理,水管管理,制冷剂管理,空调配件管理接口
 * 
 * <AUTHOR>
 * @date 2024-07-23
 */
@Api(tags = "空调 包含 室外机管理,室内机管理,气液管管理,水管管理,制冷剂管理,空调配件管理:PAir")
@FeignClient(Constant.APPLICATION_NAME)
public interface IPAirService {

    @ApiOperation(value = "查询空调 包含 室外机管理,室内机管理,气液管管理,水管管理,制冷剂管理,空调配件管理列表", response = AjaxResult.class)
    @GetMapping("air/list")
    TableDataInfo list(PAir pAir);


    @ApiOperation(value = "获取空调 包含 室外机管理,室内机管理,气液管管理,水管管理,制冷剂管理,空调配件管理详细信息", response = AjaxResult.class)
    @GetMapping(value = "air/{id}")
    AjaxResult getInfo(@PathVariable(value = "id") Integer id);

    @ApiOperation(value = "新增空调 包含 室外机管理,室内机管理,气液管管理,水管管理,制冷剂管理,空调配件管理", response = AjaxResult.class)
    @Log(title = "空调 包含 室外机管理,室内机管理,气液管管理,水管管理,制冷剂管理,空调配件管理", businessType = BusinessType.INSERT)
    @PostMapping(value = "air")
    AjaxResult add(@RequestBody PAir pAir);

    @ApiOperation(value = "修改空调 包含 室外机管理,室内机管理,气液管管理,水管管理,制冷剂管理,空调配件管理", response = AjaxResult.class)
    @Log(title = "空调 包含 室外机管理,室内机管理,气液管管理,水管管理,制冷剂管理,空调配件管理", businessType = BusinessType.UPDATE)
    @PutMapping(value = "air")
    AjaxResult edit(@RequestBody PAir pAir);

    @ApiOperation(value = "删除空调 包含 室外机管理,室内机管理,气液管管理,水管管理,制冷剂管理,空调配件管理", response = AjaxResult.class)
    @Log(title = "空调 包含 室外机管理,室内机管理,气液管管理,水管管理,制冷剂管理,空调配件管理", businessType = BusinessType.DELETE)
    @DeleteMapping("air/{ids}")
    AjaxResult remove(@PathVariable(value = "ids") Integer[] ids);


    @ApiOperation(value = "修改isshow状态", response = AjaxResult.class)
    @Log(title = "修改isshow状态", businessType = BusinessType.UPDATE)
    @PostMapping(value = "air/updateIsShow")
    AjaxResult updateIsShow(@RequestBody PAir pAir);

    @ApiOperation(value = "分页查询", response = R.class)
    @PostMapping(value = "air/page")
    PageResultUtils<PAir> page(@RequestBody  PAir pAir);


    @ApiOperation(value = "查询详情", response = R.class)
    @PostMapping(value = "air/getInfoById")
    R getInfoById(@RequestBody PAir pAir);
}
