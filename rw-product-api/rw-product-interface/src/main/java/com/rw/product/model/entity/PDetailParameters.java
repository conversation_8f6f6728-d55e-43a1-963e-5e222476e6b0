package com.rw.product.model.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.rw.common.core.web.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 详细参数
 * */

@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "详细参数")
public class PDetailParameters extends BaseEntity {
    @TableId
    @ApiModelProperty(value = "内部标识")
    private Integer id;
    @ApiModelProperty(value = "内容")
    private String content;
    @ApiModelProperty(value = "内容(英文)")
    private String contentEn;
    @ApiModelProperty(value = "类别")
    private Integer mainClass;
    @ApiModelProperty(value = "产品ID")
    private Integer productId;


    @TableLogic
    @ApiModelProperty(value = "删除状态 1已删除 0未删除")
    private Long delFlag;
    @ApiModelProperty(value = "创建人id")
    private Long createId;
    @ApiModelProperty(value = "更新人id")
    private Long updateId;

}
