package com.rw.product.model.enums;

public enum BoxlistStatusEnum {

    under_review(0, "审核中"),
    review_passed(1, "审核通过"),
    reject(2, "驳回");
    private int code;
    private String desc;

    BoxlistStatusEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
