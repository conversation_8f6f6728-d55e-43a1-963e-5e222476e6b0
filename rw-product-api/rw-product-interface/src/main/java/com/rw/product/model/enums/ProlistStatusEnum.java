package com.rw.product.model.enums;

public enum ProlistStatusEnum {

    under_review(0, "审核中"),
    review_passed(1, "审核通过"),
    reject(2, "驳回");
    private int code;
    private String desc;

    ProlistStatusEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
