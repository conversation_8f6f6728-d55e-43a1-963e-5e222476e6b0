package com.rw.product.model.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.rw.common.core.annotation.Excel;
import com.rw.common.core.web.domain.BaseEntity;
import lombok.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.math.BigDecimal;
import java.util.Date;

import com.rw.common.core.web.domain.BaseEntity;

/**
 * 方案组合详情对象 s_prolist_details
 *
 * <AUTHOR>
 * @date 2024-07-23
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "方案组合详情")
public class SProlistDetails extends BaseEntity {
    @TableId
    @ApiModelProperty(value = "内部标识")
    private Integer id;
    @ApiModelProperty(value = "系统编号 s_boxlist表list_no")
    private Long listNo;
    @ApiModelProperty(value = "个数")
    private Long num;
    @ApiModelProperty(value = "对应list_no的 方案类型 1配电柜 2ups 3空调 4安防 5一级配电柜 6EPS 7动环 8机房能源配置")
    private Long listclass;
    @ApiModelProperty(value = "系统编号 s_prolist表的list_no")
    private Long proNo;
    @TableLogic
    @ApiModelProperty(value = "删除状态 1已删除 0未删除")
    private Long delFlag;
    @ApiModelProperty(value = "创建人id")
    private Long createId;
    @ApiModelProperty(value = "创建人")
    private String createBy;

    @ApiModelProperty(value = "更新人id")
    private Long updateId;





    @ApiModelProperty(value = "方案名称")
    @TableField(exist = false)
    private String boxlistTitle;

    @ApiModelProperty(value = "方案价格")
    @TableField(exist = false)
    private BigDecimal eleprice;

    @ApiModelProperty(value = "总计")
    @TableField(exist = false)
    private BigDecimal totalPrice;

    @TableField(exist = false)
    @ApiModelProperty(value = "图片路径")
    private String imgPath;

}
